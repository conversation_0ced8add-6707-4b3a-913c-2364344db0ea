/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'SummarizationResult' and 'SummarizeExtractor' models.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { ErrorHandlerFn } from '../../typing/error_handler.js';
import { numTokensFromString } from '../../utils/tokens.js';
import { ChatModel } from '../../../language_model/protocol/base.js';
import { SUMMARIZE_PROMPT } from '../../../prompts/index/summarize_descriptions.js';

// These tokens are used in the prompt
const ENTITY_NAME_KEY = "entity_name";
const DESCRIPTION_LIST_KEY = "description_list";
const MAX_LENGTH_KEY = "max_length";

/**
 * Summarization result class definition.
 * Matches the Python SummarizationResult dataclass exactly.
 */
export interface SummarizationResult {
    id: string | [string, string];
    description: string;
}

/**
 * Summarize extractor class definition.
 * Matches the Python SummarizeExtractor class exactly.
 */
export class SummarizeExtractor {
    private _model: ChatModel;
    private _summarization_prompt: string;
    private _on_error: ErrorHandlerFn;
    private _max_summary_length: number;
    private _max_input_tokens: number;

    /**
     * Initialize the SummarizeExtractor.
     * Matches the Python __init__ method exactly.
     */
    constructor(
        model_invoker: ChatModel,
        max_summary_length: number,
        max_input_tokens: number,
        summarization_prompt?: string,
        on_error?: ErrorHandlerFn
    ) {
        // Python: self._model = model_invoker
        this._model = model_invoker;
        
        // Python: self._summarization_prompt = summarization_prompt or SUMMARIZE_PROMPT
        this._summarization_prompt = summarization_prompt || SUMMARIZE_PROMPT;
        
        // Python: self._on_error = on_error or (lambda _e, _s, _d: None)
        this._on_error = on_error || ((_e, _s, _d) => {});
        
        // Python: self._max_summary_length = max_summary_length
        this._max_summary_length = max_summary_length;
        
        // Python: self._max_input_tokens = max_input_tokens
        this._max_input_tokens = max_input_tokens;
    }

    /**
     * Call method definition.
     * Matches the Python __call__ method exactly.
     */
    async call(
        id: string | [string, string],
        descriptions: string[]
    ): Promise<SummarizationResult> {
        // Python: result = ""
        let result = "";
        
        // Python: if len(descriptions) == 0:
        if (descriptions.length === 0) {
            // Python: result = ""
            result = "";
        } 
        // Python: elif len(descriptions) == 1:
        else if (descriptions.length === 1) {
            // Python: result = descriptions[0]
            result = descriptions[0];
        } 
        // Python: else:
        else {
            // Python: result = await self._summarize_descriptions(id, descriptions)
            result = await this._summarize_descriptions(id, descriptions);
        }

        // Python: return SummarizationResult(id=id, description=result or "")
        return {
            id: id,
            description: result || ""
        };
    }

    /**
     * Summarize descriptions into a single description.
     * Matches the Python _summarize_descriptions method exactly.
     */
    private async _summarize_descriptions(
        id: string | [string, string], 
        descriptions: string[]
    ): Promise<string> {
        // Python: sorted_id = sorted(id) if isinstance(id, list) else id
        const sorted_id = Array.isArray(id) ? [...id].sort() : id;

        // Python: # Safety check, should always be a list
        // Python: if not isinstance(descriptions, list):
        //     descriptions = [descriptions]
        if (!Array.isArray(descriptions)) {
            descriptions = [descriptions as any];
        }

        // Python: # Sort description lists
        // Python: if len(descriptions) > 1:
        //     descriptions = sorted(descriptions)
        if (descriptions.length > 1) {
            descriptions = [...descriptions].sort();
        }

        // Python: # Iterate over descriptions, adding all until the max input tokens is reached
        // Python: usable_tokens = self._max_input_tokens - num_tokens_from_string(self._summarization_prompt)
        let usable_tokens = this._max_input_tokens - numTokensFromString(this._summarization_prompt);
        
        // Python: descriptions_collected = []
        const descriptions_collected: string[] = [];
        
        // Python: result = ""
        let result = "";

        // Python: for i, description in enumerate(descriptions):
        for (let i = 0; i < descriptions.length; i++) {
            const description = descriptions[i];
            
            // Python: usable_tokens -= num_tokens_from_string(description)
            usable_tokens -= numTokensFromString(description);
            
            // Python: descriptions_collected.append(description)
            descriptions_collected.push(description);

            // Python: # If buffer is full, or all descriptions have been added, summarize
            // Python: if (usable_tokens < 0 and len(descriptions_collected) > 1) or (i == len(descriptions) - 1):
            if ((usable_tokens < 0 && descriptions_collected.length > 1) || (i === descriptions.length - 1)) {
                // Python: # Calculate result (final or partial)
                // Python: result = await self._summarize_descriptions_with_llm(sorted_id, descriptions_collected)
                result = await this._summarize_descriptions_with_llm(sorted_id, descriptions_collected);

                // Python: # If we go for another loop, reset values to new
                // Python: if i != len(descriptions) - 1:
                if (i !== descriptions.length - 1) {
                    // Python: descriptions_collected = [result]
                    descriptions_collected.length = 0;
                    descriptions_collected.push(result);
                    
                    // Python: usable_tokens = (
                    //     self._max_input_tokens
                    //     - num_tokens_from_string(self._summarization_prompt)
                    //     - num_tokens_from_string(result)
                    // )
                    usable_tokens = this._max_input_tokens 
                        - numTokensFromString(this._summarization_prompt)
                        - numTokensFromString(result);
                }
            }
        }

        // Python: return result
        return result;
    }

    /**
     * Summarize descriptions using the LLM.
     * Matches the Python _summarize_descriptions_with_llm method exactly.
     */
    private async _summarize_descriptions_with_llm(
        id: string | [string, string] | string[], 
        descriptions: string[]
    ): Promise<string> {
        try {
            // Python: response = await self._model.achat(
            //     self._summarization_prompt.format(**{
            //         ENTITY_NAME_KEY: json.dumps(id, ensure_ascii=False),
            //         DESCRIPTION_LIST_KEY: json.dumps(sorted(descriptions), ensure_ascii=False),
            //         MAX_LENGTH_KEY: self._max_summary_length,
            //     }),
            //     name="summarize",
            // )
            const prompt = this._summarization_prompt
                .replace(`{${ENTITY_NAME_KEY}}`, JSON.stringify(id))
                .replace(`{${DESCRIPTION_LIST_KEY}}`, JSON.stringify([...descriptions].sort()))
                .replace(`{${MAX_LENGTH_KEY}}`, this._max_summary_length.toString());

            const response = await this._model.achat(prompt, { name: "summarize" });
            
            // Python: # Calculate result
            // Python: return str(response.output.content)
            return String(response.output?.content || "");
            
        } catch (error) {
            // Handle errors using the error handler
            const err = error instanceof Error ? error : new Error(String(error));
            this._on_error(err, err.stack || null, { id, descriptions });
            return "";
        }
    }
}
