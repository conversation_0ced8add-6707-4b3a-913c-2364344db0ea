/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';

const logger = console;

/**
 * Clean the state after the update.
 */
export async function runWorkflow(
    _config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: update_clean_state");
    
    const keysToDelete = Object.keys(context.state).filter(keyName =>
        keyName.startsWith("incremental_update_")
    );

    for (const keyName of keysToDelete) {
        delete context.state[keyName];
    }

    logger.info("Workflow completed: update_clean_state");
    return { result: null };
}