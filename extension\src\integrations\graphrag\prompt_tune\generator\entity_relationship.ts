// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Entity relationship example generation module.
 */

import { ChatModel } from '../../language_model/protocol/base';
import {
    ENTITY_RELATIONSHIPS_GENERATION_JSON_PROMPT,
    ENTITY_RELATIONSHIPS_GENERATION_PROMPT,
    UNTYPED_ENTITY_RELATIONSHIPS_GENERATION_PROMPT,
} from '../prompt/entity_relationship';

const MAX_EXAMPLES = 5;

/**
 * Generate a list of entity/relationships examples for use in generating an entity configuration.
 * 
 * Will return entity/relationships examples as either JSON or in tuple_delimiter format depending
 * on the jsonMode parameter.
 * 
 * @param model - The chat model to use
 * @param persona - The persona to use for generation
 * @param entityTypes - The entity types to use (can be string, array, or null)
 * @param docs - The documents to analyze
 * @param language - The language to use
 * @param jsonMode - Whether to use JSON mode
 * @returns Array of generated entity relationship examples
 */
export async function generateEntityRelationshipExamples(
    model: ChatModel,
    persona: string,
    entityTypes: string | string[] | null,
    docs: string | string[],
    language: string,
    jsonMode: boolean = false
): Promise<string[]> {
    const docsList = Array.isArray(docs) ? docs : [docs];
    const history = [{ content: persona, role: 'system' }];

    let messages: string[];

    if (entityTypes) {
        const entityTypesStr = Array.isArray(entityTypes) 
            ? entityTypes.join(', ')
            : entityTypes;

        messages = docsList.map(doc => {
            const template = jsonMode 
                ? ENTITY_RELATIONSHIPS_GENERATION_JSON_PROMPT
                : ENTITY_RELATIONSHIPS_GENERATION_PROMPT;
            
            return template
                .replace('{entity_types}', entityTypesStr)
                .replace('{input_text}', doc)
                .replace('{language}', language);
        });
    } else {
        messages = docsList.map(doc => 
            UNTYPED_ENTITY_RELATIONSHIPS_GENERATION_PROMPT
                .replace('{input_text}', doc)
                .replace('{language}', language)
        );
    }

    // Limit to MAX_EXAMPLES
    messages = messages.slice(0, MAX_EXAMPLES);

    // Create tasks for parallel execution
    const tasks = messages.map(message => 
        model.achat(message, { history, json: jsonMode })
    );

    // Execute all tasks in parallel
    const responses = await Promise.all(tasks);

    return responses.map(response => response.output.content || '');
}

/**
 * Generate entity relationship examples with error handling.
 */
export async function generateEntityRelationshipExamplesSafe(
    model: ChatModel,
    persona: string,
    entityTypes: string | string[] | null,
    docs: string | string[],
    language: string,
    jsonMode: boolean = false
): Promise<{ success: string[], errors: Error[] }> {
    const docsList = Array.isArray(docs) ? docs : [docs];
    const history = [{ content: persona, role: 'system' }];
    
    const success: string[] = [];
    const errors: Error[] = [];

    let messages: string[];

    if (entityTypes) {
        const entityTypesStr = Array.isArray(entityTypes) 
            ? entityTypes.join(', ')
            : entityTypes;

        messages = docsList.map(doc => {
            const template = jsonMode 
                ? ENTITY_RELATIONSHIPS_GENERATION_JSON_PROMPT
                : ENTITY_RELATIONSHIPS_GENERATION_PROMPT;
            
            return template
                .replace('{entity_types}', entityTypesStr)
                .replace('{input_text}', doc)
                .replace('{language}', language);
        });
    } else {
        messages = docsList.map(doc => 
            UNTYPED_ENTITY_RELATIONSHIPS_GENERATION_PROMPT
                .replace('{input_text}', doc)
                .replace('{language}', language)
        );
    }

    // Limit to MAX_EXAMPLES
    messages = messages.slice(0, MAX_EXAMPLES);

    // Process each message individually with error handling
    for (const message of messages) {
        try {
            const response = await model.achat(message, { history, json: jsonMode });
            success.push(response.output.content || '');
        } catch (error) {
            errors.push(error instanceof Error ? error : new Error(String(error)));
        }
    }

    return { success, errors };
}
