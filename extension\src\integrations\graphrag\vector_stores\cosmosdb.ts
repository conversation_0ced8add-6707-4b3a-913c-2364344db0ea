// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A package containing the CosmosDB vector store implementation.
 */

import { TextEmbedder } from '../data_model/types';
import {
    DEFAULT_VECTOR_SIZE,
    BaseVectorStore,
    VectorStoreDocument,
    VectorStoreSearchResult,
} from './base';

// Azure Cosmos SDK interfaces (would need to be imported from actual Azure SDK)
interface CosmosClient {
    createDatabaseIfNotExists(options: { id: string }): Promise<any>;
    getDatabaseClient(id: string): DatabaseProxy;
    listDatabases(): any[];
    deleteDatabase(id: string): Promise<void>;
}

interface DatabaseProxy {
    createContainerIfNotExists(options: any): Promise<any>;
    getContainerClient(id: string): ContainerProxy;
    listContainers(): any[];
    deleteContainer(id: string): Promise<void>;
}

interface ContainerProxy {
    upsertItem(item: any): Promise<any>;
    queryItems(options: any): any[];
    readItem(item: string, partitionKey: string): Promise<any>;
}

interface DefaultAzureCredential {
    // Azure credential interface
}

// Mock implementations for Azure Cosmos SDK components
class MockCosmosClient implements CosmosClient {
    private databases: Map<string, MockDatabaseProxy> = new Map();

    static fromConnectionString(connectionString: string): MockCosmosClient {
        return new MockCosmosClient();
    }

    constructor(options?: any) {}

    async createDatabaseIfNotExists(options: { id: string }): Promise<any> {
        if (!this.databases.has(options.id)) {
            this.databases.set(options.id, new MockDatabaseProxy());
        }
        return Promise.resolve();
    }

    getDatabaseClient(id: string): DatabaseProxy {
        if (!this.databases.has(id)) {
            this.databases.set(id, new MockDatabaseProxy());
        }
        return this.databases.get(id)!;
    }

    listDatabases(): any[] {
        return Array.from(this.databases.keys()).map(id => ({ id }));
    }

    async deleteDatabase(id: string): Promise<void> {
        this.databases.delete(id);
        return Promise.resolve();
    }
}

class MockDatabaseProxy implements DatabaseProxy {
    private containers: Map<string, MockContainerProxy> = new Map();

    async createContainerIfNotExists(options: any): Promise<any> {
        if (!this.containers.has(options.id)) {
            this.containers.set(options.id, new MockContainerProxy());
        }
        return Promise.resolve();
    }

    getContainerClient(id: string): ContainerProxy {
        if (!this.containers.has(id)) {
            this.containers.set(id, new MockContainerProxy());
        }
        return this.containers.get(id)!;
    }

    listContainers(): any[] {
        return Array.from(this.containers.keys()).map(id => ({ id }));
    }

    async deleteContainer(id: string): Promise<void> {
        this.containers.delete(id);
        return Promise.resolve();
    }
}

class MockContainerProxy implements ContainerProxy {
    private items: Map<string, any> = new Map();

    async upsertItem(item: any): Promise<any> {
        this.items.set(item.id, item);
        return Promise.resolve(item);
    }

    queryItems(options: any): any[] {
        // Mock implementation - would need proper query parsing
        return Array.from(this.items.values());
    }

    async readItem(item: string, partitionKey: string): Promise<any> {
        return Promise.resolve(this.items.get(item) || {});
    }
}

class MockDefaultAzureCredential implements DefaultAzureCredential {}

/**
 * Azure CosmosDB vector storage implementation.
 */
export class CosmosDBVectorStore extends BaseVectorStore {
    private cosmosClient!: CosmosClient;
    private databaseClient!: DatabaseProxy;
    private containerClient!: ContainerProxy;
    private databaseName!: string;
    private containerName!: string;
    private vectorSize: number = DEFAULT_VECTOR_SIZE;

    constructor(kwargs: Record<string, any> = {}) {
        super(
            kwargs.collection_name || '',
            kwargs.db_connection,
            kwargs.document_collection,
            kwargs.query_filter,
            kwargs
        );
    }

    /**
     * Connect to CosmosDB vector storage.
     */
    async connect(kwargs: Record<string, any> = {}): Promise<void> {
        const connectionString = kwargs.connection_string;
        if (connectionString) {
            this.cosmosClient = MockCosmosClient.fromConnectionString(connectionString);
        } else {
            const url = kwargs.url;
            if (!url) {
                const msg = "Either connection_string or url must be provided.";
                throw new Error(msg);
            }
            this.cosmosClient = new MockCosmosClient({
                url,
                credential: new MockDefaultAzureCredential()
            });
        }

        const databaseName = kwargs.database_name;
        if (!databaseName) {
            const msg = "Database name must be provided.";
            throw new Error(msg);
        }
        this.databaseName = databaseName;

        const collectionName = this.collection_name;
        if (!collectionName) {
            const msg = "Collection name is empty or not provided.";
            throw new Error(msg);
        }
        this.containerName = collectionName;

        this.vectorSize = kwargs.vector_size || DEFAULT_VECTOR_SIZE;
        await this.createDatabase();
        await this.createContainer();
    }

    /**
     * Create the database if it doesn't exist.
     */
    private async createDatabase(): Promise<void> {
        await this.cosmosClient.createDatabaseIfNotExists({ id: this.databaseName });
        this.databaseClient = this.cosmosClient.getDatabaseClient(this.databaseName);
    }

    /**
     * Delete the database if it exists.
     */
    private async deleteDatabase(): Promise<void> {
        if (this.databaseExists()) {
            await this.cosmosClient.deleteDatabase(this.databaseName);
        }
    }

    /**
     * Check if the database exists.
     */
    private databaseExists(): boolean {
        const existingDatabaseNames = this.cosmosClient.listDatabases().map(db => db.id);
        return existingDatabaseNames.includes(this.databaseName);
    }

    /**
     * Create the container if it doesn't exist.
     */
    private async createContainer(): Promise<void> {
        const partitionKey = { paths: ["/id"], kind: "Hash" };

        // Define the container vector policy
        const vectorEmbeddingPolicy = {
            vectorEmbeddings: [
                {
                    path: "/vector",
                    dataType: "float32",
                    distanceFunction: "cosine",
                    dimensions: this.vectorSize,
                }
            ]
        };

        // Define the vector indexing policy
        const indexingPolicy = {
            indexingMode: "consistent",
            automatic: true,
            includedPaths: [{ path: "/*" }],
            excludedPaths: [{ path: "/_etag/?" }, { path: "/vector/*" }],
        };

        // Currently, the CosmosDB emulator does not support the diskANN policy.
        try {
            // First try with the standard diskANN policy
            const indexingPolicyWithVector = {
                ...indexingPolicy,
                vectorIndexes: [{ path: "/vector", type: "diskANN" }]
            };

            // Create the container and container client
            await this.databaseClient.createContainerIfNotExists({
                id: this.containerName,
                partitionKey,
                indexingPolicy: indexingPolicyWithVector,
                vectorEmbeddingPolicy,
            });
        } catch (error) {
            // If diskANN fails (likely in emulator), retry without vector indexes
            // Create the container with compatible indexing policy
            await this.databaseClient.createContainerIfNotExists({
                id: this.containerName,
                partitionKey,
                indexingPolicy,
                vectorEmbeddingPolicy,
            });
        }

        this.containerClient = this.databaseClient.getContainerClient(this.containerName);
    }

    /**
     * Delete the vector store container in the database if it exists.
     */
    private async deleteContainer(): Promise<void> {
        if (this.containerExists()) {
            await this.databaseClient.deleteContainer(this.containerName);
        }
    }

    /**
     * Check if the container name exists in the database.
     */
    private containerExists(): boolean {
        const existingContainerNames = this.databaseClient.listContainers().map(container => container.id);
        return existingContainerNames.includes(this.containerName);
    }

    /**
     * Load documents into CosmosDB.
     */
    async loadDocuments(documents: VectorStoreDocument[], overwrite: boolean = true): Promise<void> {
        // Create a CosmosDB container on overwrite
        if (overwrite) {
            await this.deleteContainer();
            await this.createContainer();
        }

        if (!this.containerClient) {
            const msg = "Container client is not initialized.";
            throw new Error(msg);
        }

        // Upload documents to CosmosDB
        for (const doc of documents) {
            if (doc.vector !== null && doc.vector !== undefined) {
                const docJson = {
                    id: doc.id,
                    vector: doc.vector,
                    text: doc.text,
                    attributes: JSON.stringify(doc.attributes),
                };
                await this.containerClient.upsertItem(docJson);
            }
        }
    }

    /**
     * Perform a vector-based similarity search.
     */
    async similaritySearchByVector(
        queryEmbedding: number[],
        k: number = 10,
        kwargs: Record<string, any> = {}
    ): Promise<VectorStoreSearchResult[]> {
        if (!this.containerClient) {
            const msg = "Container client is not initialized.";
            throw new Error(msg);
        }

        let items: any[];

        try {
            const query = `SELECT TOP ${k} c.id, c.text, c.vector, c.attributes, VectorDistance(c.vector, @embedding) AS SimilarityScore FROM c ORDER BY VectorDistance(c.vector, @embedding)`;
            const queryParams = [{ name: "@embedding", value: queryEmbedding }];
            items = this.containerClient.queryItems({
                query,
                parameters: queryParams,
                enableCrossPartitionQuery: true,
            });
        } catch (error) {
            // Currently, the CosmosDB emulator does not support the VectorDistance function.
            // For emulator or test environments - fetch all items and calculate distance locally
            const query = "SELECT c.id, c.text, c.vector, c.attributes FROM c";
            items = this.containerClient.queryItems({
                query,
                enableCrossPartitionQuery: true,
            });

            // Calculate cosine similarity locally (1 - cosine distance)
            const cosineSimilarity = (a: number[], b: number[]): number => {
                const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
                const normA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
                const normB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
                
                if (normA * normB === 0) {
                    return 0.0;
                }
                return dotProduct / (normA * normB);
            };

            // Calculate scores for all items
            for (const item of items) {
                const itemVector = item.vector || [];
                const similarity = cosineSimilarity(queryEmbedding, itemVector);
                item.SimilarityScore = similarity;
            }

            // Sort by similarity score (higher is better) and take top k
            items = items
                .sort((a, b) => (b.SimilarityScore || 0) - (a.SimilarityScore || 0))
                .slice(0, k);
        }

        return items.map(item => ({
            document: {
                id: item.id || "",
                text: item.text || "",
                vector: item.vector || [],
                attributes: JSON.parse(item.attributes || "{}"),
            },
            score: item.SimilarityScore || 0.0,
        }));
    }

    /**
     * Perform a text-based similarity search.
     */
    async similaritySearchByText(
        text: string,
        textEmbedder: TextEmbedder,
        k: number = 10,
        kwargs: Record<string, any> = {}
    ): Promise<VectorStoreSearchResult[]> {
        const queryEmbedding = textEmbedder(text);
        if (queryEmbedding) {
            return this.similaritySearchByVector(queryEmbedding, k);
        }
        return [];
    }

    /**
     * Build a query filter to filter documents by a list of ids.
     */
    filterById(includeIds: (string | number)[]): any {
        if (!includeIds || includeIds.length === 0) {
            this.query_filter = null;
        } else {
            const idFilter = includeIds.map(id => 
                typeof id === 'string' ? `'${id}'` : String(id)
            ).join(', ');
            this.query_filter = `SELECT * FROM c WHERE c.id IN (${idFilter})`;
        }
        return this.query_filter;
    }

    /**
     * Search for a document by id.
     */
    async searchById(id: string): Promise<VectorStoreDocument> {
        if (!this.containerClient) {
            const msg = "Container client is not initialized.";
            throw new Error(msg);
        }

        const item = await this.containerClient.readItem(id, id);
        return {
            id: item.id || "",
            vector: item.vector || [],
            text: item.text || "",
            attributes: JSON.parse(item.attributes || "{}"),
        };
    }

    /**
     * Clear the vector store.
     */
    async clear(): Promise<void> {
        await this.deleteContainer();
        await this.deleteDatabase();
    }
}