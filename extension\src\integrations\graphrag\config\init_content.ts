/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Content for the init CLI command to generate a default configuration.
 */

import * as defs from './defaults';
import {
    graphragConfigDefaults,
    languageModelDefaults,
    vectorStoreDefaults,
} from './defaults';

export const INIT_YAML = `### This config file contains required core defaults that must be set, along with a handful of common optional settings.
### For a full list of available settings, see https://microsoft.github.io/graphrag/config/yaml/

### LLM settings ###
## There are a number of settings to tune the threading and token limits for LLM calls - check the docs.

models:
  ${defs.DEFAULT_CHAT_MODEL_ID}:
    type: ${defs.DEFAULT_CHAT_MODEL_TYPE} # or azure_openai_chat
    # api_base: https://<instance>.openai.azure.com
    # api_version: 2024-05-01-preview
    auth_type: ${defs.DEFAULT_CHAT_MODEL_AUTH_TYPE} # or azure_managed_identity
    api_key: \${{GRAPHRAG_API_KEY}} # set this in the generated .env file
    # audience: "https://cognitiveservices.azure.com/.default"
    # organization: <organization_id>
    model: ${defs.DEFAULT_CHAT_MODEL}
    # deployment_name: <azure_model_deployment_name>
    # encoding_model: ${defs.ENCODING_MODEL} # automatically set by tiktoken if left undefined
    model_supports_json: true # recommended if this is available for your model.
    concurrent_requests: ${languageModelDefaults.concurrent_requests} # max number of simultaneous LLM requests allowed
    async_mode: ${languageModelDefaults.async_mode} # or asyncio
    retry_strategy: native
    max_retries: ${languageModelDefaults.max_retries}
    tokens_per_minute: ${languageModelDefaults.tokens_per_minute}              # set to null to disable rate limiting
    requests_per_minute: ${languageModelDefaults.requests_per_minute}            # set to null to disable rate limiting
  ${defs.DEFAULT_EMBEDDING_MODEL_ID}:
    type: ${defs.DEFAULT_EMBEDDING_MODEL_TYPE} # or azure_openai_embedding
    # api_base: https://<instance>.openai.azure.com
    # api_version: 2024-05-01-preview
    auth_type: ${defs.DEFAULT_EMBEDDING_MODEL_AUTH_TYPE} # or azure_managed_identity
    api_key: \${{GRAPHRAG_API_KEY}}
    # audience: "https://cognitiveservices.azure.com/.default"
    # organization: <organization_id>
    model: ${defs.DEFAULT_EMBEDDING_MODEL}
    # deployment_name: <azure_model_deployment_name>
    # encoding_model: ${defs.ENCODING_MODEL} # automatically set by tiktoken if left undefined
    model_supports_json: true # recommended if this is available for your model.
    concurrent_requests: ${languageModelDefaults.concurrent_requests} # max number of simultaneous LLM requests allowed
    async_mode: ${languageModelDefaults.async_mode} # or asyncio
    retry_strategy: native
    max_retries: ${languageModelDefaults.max_retries}
    tokens_per_minute: null              # set to null to disable rate limiting or auto for dynamic
    requests_per_minute: null            # set to null to disable rate limiting or auto for dynamic

### Input settings ###

input:
  storage:
    type: ${graphragConfigDefaults.input.storage.type} # or blob
    base_dir: "${graphragConfigDefaults.input.storage.base_dir}"
  file_type: ${graphragConfigDefaults.input.file_type} # [csv, text, json]


chunks:
  size: ${graphragConfigDefaults.chunks.size}
  overlap: ${graphragConfigDefaults.chunks.overlap}
  group_by_columns: [${graphragConfigDefaults.chunks.group_by_columns.join(",")}]

### Output/storage settings ###
## If blob storage is specified in the following four sections,
## connection_string and container_name must be provided

output:
  type: ${graphragConfigDefaults.output.type} # [file, blob, cosmosdb]
  base_dir: "${graphragConfigDefaults.output.baseDir}"
    
cache:
  type: ${graphragConfigDefaults.cache.type} # [file, blob, cosmosdb]
  base_dir: "${graphragConfigDefaults.cache.baseDir}"

reporting:
  type: ${graphragConfigDefaults.reporting.type} # [file, blob, cosmosdb]
  base_dir: "${graphragConfigDefaults.reporting.baseDir}"

vector_store:
  ${defs.DEFAULT_VECTOR_STORE_ID}:
    type: ${vectorStoreDefaults.type}
    db_uri: ${vectorStoreDefaults.dbUri}
    container_name: ${vectorStoreDefaults.containerName}
    overwrite: ${vectorStoreDefaults.overwrite}

### Workflow settings ###

embed_text:
  model_id: ${graphragConfigDefaults.embedText.modelId}
  vector_store_id: ${graphragConfigDefaults.embedText.vectorStoreId}

extract_graph:
  model_id: ${graphragConfigDefaults.extractGraph.modelId}
  prompt: "prompts/extract_graph.txt"
  entity_types: [${graphragConfigDefaults.extractGraph.entityTypes.join(",")}]
  max_gleanings: ${graphragConfigDefaults.extractGraph.maxGleanings}

summarize_descriptions:
  model_id: ${graphragConfigDefaults.summarizeDescriptions.modelId}
  prompt: "prompts/summarize_descriptions.txt"
  max_length: ${graphragConfigDefaults.summarizeDescriptions.maxLength}

extract_graph_nlp:
  text_analyzer:
    extractor_type: ${graphragConfigDefaults.extractGraphNlp.textAnalyzer.extractorType} # [regex_english, syntactic_parser, cfg]

cluster_graph:
  max_cluster_size: ${graphragConfigDefaults.clusterGraph.maxClusterSize}

extract_claims:
  enabled: false
  model_id: ${graphragConfigDefaults.extractClaims.modelId}
  prompt: "prompts/extract_claims.txt"
  description: "${graphragConfigDefaults.extractClaims.description}"
  max_gleanings: ${graphragConfigDefaults.extractClaims.maxGleanings}

community_reports:
  model_id: ${graphragConfigDefaults.communityReports.modelId}
  graph_prompt: "prompts/community_report_graph.txt"
  text_prompt: "prompts/community_report_text.txt"
  max_length: ${graphragConfigDefaults.communityReports.maxLength}
  max_input_length: ${graphragConfigDefaults.communityReports.maxInputLength}

embed_graph:
  enabled: false # if true, will generate node2vec embeddings for nodes

umap:
  enabled: false # if true, will generate UMAP embeddings for nodes (embed_graph must also be enabled)

snapshots:
  graphml: false
  embeddings: false

### Query settings ###
## The prompt locations are required here, but each search method has a number of optional knobs that can be tuned.
## See the config docs: https://microsoft.github.io/graphrag/config/yaml/#query

local_search:
  chat_model_id: ${graphragConfigDefaults.localSearch.chatModelId}
  embedding_model_id: ${graphragConfigDefaults.localSearch.embeddingModelId}
  prompt: "prompts/local_search_system_prompt.txt"

global_search:
  chat_model_id: ${graphragConfigDefaults.globalSearch.chatModelId}
  map_prompt: "prompts/global_search_map_system_prompt.txt"
  reduce_prompt: "prompts/global_search_reduce_system_prompt.txt"
  knowledge_prompt: "prompts/global_search_knowledge_system_prompt.txt"

drift_search:
  chat_model_id: ${graphragConfigDefaults.driftSearch.chatModelId}
  embedding_model_id: ${graphragConfigDefaults.driftSearch.embeddingModelId}
  prompt: "prompts/drift_search_system_prompt.txt"
  reduce_prompt: "prompts/drift_search_reduce_prompt.txt"

basic_search:
  chat_model_id: ${graphragConfigDefaults.basicSearch.chatModelId}
  embedding_model_id: ${graphragConfigDefaults.basicSearch.embeddingModelId}
  prompt: "prompts/basic_search_system_prompt.txt"
`;

export const INIT_DOTENV = `GRAPHRAG_API_KEY=<API_KEY>
`;