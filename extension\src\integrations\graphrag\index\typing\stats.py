# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""Pipeline stats types."""

from dataclasses import dataclass, field


@dataclass
class PipelineRunStats:
    """Pipeline running stats."""

    total_runtime: float = field(default=0)
    """Float representing the total runtime."""

    num_documents: int = field(default=0)
    """Number of documents."""
    update_documents: int = field(default=0)
    """Number of update documents."""

    input_load_time: float = field(default=0)
    """Float representing the input load time."""

    workflows: dict[str, dict[str, float]] = field(default_factory=dict)
    """A dictionary of workflows."""
