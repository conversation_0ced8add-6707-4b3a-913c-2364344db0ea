// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the global search configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';

/**
 * The configuration section for Global Search.
 */
export interface GlobalSearchConfig {
  /**
   * The global search mapper prompt to use.
   */
  mapPrompt?: string;

  /**
   * The global search reducer to use.
   */
  reducePrompt?: string;

  /**
   * The model ID to use for global search.
   */
  chatModelId: string;

  /**
   * The global search general prompt to use.
   */
  knowledgePrompt?: string;

  /**
   * The maximum context size in tokens.
   */
  maxContextTokens: number;

  /**
   * The data llm maximum tokens.
   */
  dataMaxTokens: number;

  /**
   * The map llm maximum response length in words.
   */
  mapMaxLength: number;

  /**
   * The reduce llm maximum response length in words.
   */
  reduceMaxLength: number;

  /**
   * Rating threshold in include a community report.
   */
  dynamicSearchThreshold: number;

  /**
   * Keep parent community if any of the child communities are relevant.
   */
  dynamicSearchKeepParent: boolean;

  /**
   * Number of times to rate the same community report.
   */
  dynamicSearchNumRepeats: number;

  /**
   * Use community summary instead of full_context.
   */
  dynamicSearchUseSummary: boolean;

  /**
   * The maximum level of community hierarchy to consider if none of the processed communities are relevant.
   */
  dynamicSearchMaxLevel: number;
}

/**
 * Create a GlobalSearchConfig with default values.
 */
export function createGlobalSearchConfig(config: Partial<GlobalSearchConfig> = {}): GlobalSearchConfig {
  const defaults = graphragConfigDefaults.globalSearch;
  
  return {
    mapPrompt: config.mapPrompt ?? defaults.mapPrompt,
    reducePrompt: config.reducePrompt ?? defaults.reducePrompt,
    chatModelId: config.chatModelId ?? defaults.chatModelId,
    knowledgePrompt: config.knowledgePrompt ?? defaults.knowledgePrompt,
    maxContextTokens: config.maxContextTokens ?? defaults.maxContextTokens,
    dataMaxTokens: config.dataMaxTokens ?? defaults.dataMaxTokens,
    mapMaxLength: config.mapMaxLength ?? defaults.mapMaxLength,
    reduceMaxLength: config.reduceMaxLength ?? defaults.reduceMaxLength,
    dynamicSearchThreshold: config.dynamicSearchThreshold ?? defaults.dynamicSearchThreshold,
    dynamicSearchKeepParent: config.dynamicSearchKeepParent ?? defaults.dynamicSearchKeepParent,
    dynamicSearchNumRepeats: config.dynamicSearchNumRepeats ?? defaults.dynamicSearchNumRepeats,
    dynamicSearchUseSummary: config.dynamicSearchUseSummary ?? defaults.dynamicSearchUseSummary,
    dynamicSearchMaxLevel: config.dynamicSearchMaxLevel ?? defaults.dynamicSearchMaxLevel,
  };
}
