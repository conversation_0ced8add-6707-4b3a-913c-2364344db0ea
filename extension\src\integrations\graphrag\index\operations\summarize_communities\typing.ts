/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'Finding' and 'CommunityReport' models.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../callbacks/workflow_callbacks.js';

export type ExtractedEntity = Record<string, any>;
export type StrategyConfig = Record<string, any>;
export type RowContext = Record<string, any>;
export type EntityTypes = string[];
export type Claim = Record<string, any>;

/**
 * Finding class definition.
 */
export interface Finding {
    summary: string;
    explanation: string;
}

/**
 * Community report class definition.
 * Matches the Python CommunityReport dataclass exactly.
 */
export interface CommunityReport {
    community_id: string;
    level: number;
    title: string;
    summary: string;
    full_content: string;
    full_content_json: Record<string, any>;
    rank: number;
    rank_explanation: string;
    findings: Finding[];
}

/**
 * Community reports strategy function type.
 * Matches the Python CommunityReportsStrategy type exactly.
 */
export type CommunityReportsStrategy = (
    community_id: string,
    community_context: string,
    community_level: number,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    config: StrategyConfig
) => Promise<CommunityReport | null>;

/**
 * CreateCommunityReportsStrategyType enum definition.
 */
export enum CreateCommunityReportsStrategyType {
    graph_intelligence = "graph_intelligence"
}