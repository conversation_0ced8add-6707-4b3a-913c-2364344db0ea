// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * API functions for the GraphRAG module.
 */

import * as path from 'path';
import * as fs from 'fs';
import { CacheFactory } from '../cache/factory';
import { PipelineCache } from '../cache/pipeline-cache';
import { createCollectionName } from '../config/embeddings';
import { CacheConfig } from '../config/models/graph-rag-config';
import { StorageConfig } from '../config/models/graph-rag-config';
import { TextEmbedder } from '../data_model/types';
import { StorageFactory } from '../storage/factory';
import { PipelineStorage } from '../storage/pipeline-storage';
import {
    BaseVectorStore,
    VectorStoreDocument,
    VectorStoreSearchResult,
} from '../vector_stores/base';
import { VectorStoreFactory } from '../vector_stores/factory';

/**
 * Multi Vector Store wrapper implementation.
 */
export class MultiVectorStore extends BaseVectorStore {
    private embeddingStores: BaseVectorStore[];
    private indexNames: string[];

    constructor(
        embeddingStores: BaseVectorStore[],
        indexNames: string[]
    ) {
        super('multi-vector-store');
        this.embeddingStores = embeddingStores;
        this.indexNames = indexNames;
    }

    /**
     * Load documents into the vector store.
     */
    loadDocuments(documents: VectorStoreDocument[], overwrite: boolean = true): void {
        const msg = "load_documents method not implemented";
        throw new Error(msg);
    }

    /**
     * Connect to vector storage.
     */
    connect(kwargs?: Record<string, any>): void {
        const msg = "connect method not implemented";
        throw new Error(msg);
    }

    /**
     * Build a query filter to filter documents by id.
     */
    filterById(includeIds: (string | number)[]): any {
        const msg = "filter_by_id method not implemented";
        throw new Error(msg);
    }

    /**
     * Search for a document by id.
     */
    searchById(id: string): VectorStoreDocument {
        const parts = id.split('-');
        const searchIndexId = parts[0];
        const searchIndexName = parts[1];
        
        for (let i = 0; i < this.indexNames.length; i++) {
            const indexName = this.indexNames[i];
            const embeddingStore = this.embeddingStores[i];
            if (indexName === searchIndexName) {
                return embeddingStore.searchById(searchIndexId);
            }
        }
        
        const message = `Index ${searchIndexName} not found.`;
        throw new Error(message);
    }

    /**
     * Perform a vector-based similarity search.
     */
    similaritySearchByVector(
        queryEmbedding: number[],
        k: number = 10,
        kwargs?: Record<string, any>
    ): VectorStoreSearchResult[] {
        const allResults: VectorStoreSearchResult[] = [];
        
        for (let i = 0; i < this.indexNames.length; i++) {
            const indexName = this.indexNames[i];
            const embeddingStore = this.embeddingStores[i];
            const results = embeddingStore.similaritySearchByVector(queryEmbedding, k);
            
            const modResults = results.map(r => ({
                ...r,
                document: {
                    ...r.document,
                    id: `${r.document.id}-${indexName}`
                }
            }));
            
            allResults.push(...modResults);
        }
        
        return allResults
            .sort((a, b) => b.score - a.score)
            .slice(0, k);
    }

    /**
     * Perform a text-based similarity search.
     */
    similaritySearchByText(
        text: string,
        textEmbedder: TextEmbedder,
        k: number = 10,
        kwargs?: Record<string, any>
    ): VectorStoreSearchResult[] {
        const queryEmbedding = textEmbedder(text);
        if (queryEmbedding) {
            return this.similaritySearchByVector(queryEmbedding, k);
        }
        return [];
    }
}

/**
 * Get the embedding description store.
 */
export function getEmbeddingStore(
    configArgs: Record<string, Record<string, any>>,
    embeddingName: string
): BaseVectorStore {
    const numIndexes = Object.keys(configArgs).length;
    const embeddingStores: BaseVectorStore[] = [];
    const indexNames: string[] = [];
    
    for (const [index, store] of Object.entries(configArgs)) {
        const vectorStoreType = store.type;
        const collectionName = createCollectionName(
            store.container_name || "default",
            embeddingName
        );
        
        const embeddingStore = VectorStoreFactory.createVectorStore(
            vectorStoreType,
            { ...store, collection_name: collectionName }
        );
        
        embeddingStore.connect(store);
        
        // If there is only a single index, return the embedding store directly
        if (numIndexes === 1) {
            return embeddingStore;
        }
        
        embeddingStores.push(embeddingStore);
        indexNames.push(index);
    }
    
    return new MultiVectorStore(embeddingStores, indexNames);
}

/**
 * Reformats context_data for all query responses.
 * 
 * Reformats a dictionary of dataframes into a dictionary of lists.
 * One list entry for each record. Records are grouped by original
 * dictionary keys.
 * 
 * Note: depending on which query algorithm is used, the context_data may not
 *       contain the same information (keys). In this case, the default behavior will be to
 *       set these keys as empty lists to preserve a standard output format.
 */
export function reformatContextData(contextData: Record<string, any>): Record<string, any[]> {
    const finalFormat: Record<string, any[]> = {
        reports: [],
        entities: [],
        relationships: [],
        claims: [],
        sources: [],
    };
    
    for (const [key, value] of Object.entries(contextData)) {
        let records: any[];
        
        if (value === null || value === undefined) {
            continue;
        }
        
        if (typeof value === 'object' && !Array.isArray(value)) {
            // Handle dataframe-like objects - convert to records
            records = Object.values(value);
        } else if (Array.isArray(value)) {
            records = value;
        } else {
            records = [value];
        }
        
        if (records.length < 1) {
            continue;
        }
        
        finalFormat[key] = records;
    }
    
    return finalFormat;
}

/**
 * Update context data with the links dict so that it contains both the index name and community id.
 */
export function updateContextData(
    contextData: any,
    links: Record<string, any>
): any {
    const updatedContextData: Record<string, any[]> = {};
    
    for (const [key, entries] of Object.entries(contextData)) {
        let updatedEntry: any[] = [];
        
        if (key === "reports") {
            updatedEntry = (entries as any[]).map(entry => ({
                ...entry,
                index_name: links.community_reports[parseInt(entry.id)].index_name,
                index_id: links.community_reports[parseInt(entry.id)].id,
            }));
        }
        
        if (key === "entities") {
            updatedEntry = (entries as any[]).map(entry => ({
                ...entry,
                entity: entry.entity.split("-")[0],
                index_name: links.entities[parseInt(entry.id)].index_name,
                index_id: links.entities[parseInt(entry.id)].id,
            }));
        }
        
        if (key === "relationships") {
            updatedEntry = (entries as any[]).map(entry => ({
                ...entry,
                source: entry.source.split("-")[0],
                target: entry.target.split("-")[0],
                index_name: links.relationships[parseInt(entry.id)].index_name,
                index_id: links.relationships[parseInt(entry.id)].id,
            }));
        }
        
        if (key === "claims") {
            updatedEntry = (entries as any[]).map(entry => ({
                ...entry,
                entity: entry.entity.split("-")[0],
                index_name: links.covariates[parseInt(entry.id)].index_name,
                index_id: links.covariates[parseInt(entry.id)].id,
            }));
        }
        
        if (key === "sources") {
            updatedEntry = (entries as any[]).map(entry => ({
                ...entry,
                index_name: links.text_units[parseInt(entry.id)].index_name,
                index_id: links.text_units[parseInt(entry.id)].id,
            }));
        }
        
        updatedContextData[key] = updatedEntry;
    }
    
    return updatedContextData;
}

/**
 * Load the search prompt from disk if configured.
 * 
 * If not, leave it empty - the search functions will load their defaults.
 */
export function loadSearchPrompt(rootDir: string, promptConfig?: string | null): string | null {
    if (promptConfig) {
        const promptFile = path.join(rootDir, promptConfig);
        if (fs.existsSync(promptFile)) {
            return fs.readFileSync(promptFile, 'utf-8');
        }
    }
    return null;
}

/**
 * Create a storage object from the config.
 */
export function createStorageFromConfig(output: StorageConfig): PipelineStorage {
    const storageConfig = { ...output };
    return StorageFactory.createStorage(
        storageConfig.type,
        storageConfig
    );
}

/**
 * Create a cache object from the config.
 */
export function createCacheFromConfig(cache: CacheConfig, rootDir: string): PipelineCache {
    const cacheConfig = { ...cache };
    return CacheFactory.createCache(
        cacheConfig.type,
        rootDir,
        cacheConfig
    );
}

/**
 * Truncate a string to a maximum length.
 */
export function truncate(text: string, maxLength: number): string {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + "...[truncated]";
}