/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Defines the is_null utility.
 */

/**
 * Check if value is null or is NaN.
 * @param value - The value to check
 * @returns True if value is null, undefined, or NaN
 */
export function isNull(value: any): boolean {
    const isNone = (): boolean => {
        return value === null || value === undefined;
    };

    const isNan = (): boolean => {
        return typeof value === 'number' && isNaN(value);
    };

    return isNone() || isNan();
}