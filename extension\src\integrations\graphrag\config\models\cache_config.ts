// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the cache configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';
import { CacheType } from '../enums.js';

/**
 * The configuration section for Cache.
 */
export interface CacheConfig {
  /**
   * The cache type to use.
   */
  type: CacheType;

  /**
   * The base directory for the cache.
   */
  baseDir: string;

  /**
   * The cache connection string to use.
   */
  connectionString?: string;

  /**
   * The cache container name to use.
   */
  containerName?: string;

  /**
   * The storage account blob url to use.
   */
  storageAccountBlobUrl?: string;

  /**
   * The cosmosdb account url to use.
   */
  cosmosdbAccountUrl?: string;
}

/**
 * Create a CacheConfig with default values.
 */
export function createCacheConfig(config: Partial<CacheConfig> = {}): CacheConfig {
  return {
    type: config.type ?? graphragConfigDefaults.cache.type,
    baseDir: config.baseDir ?? graphragConfigDefaults.cache.baseDir,
    connectionString: config.connectionString ?? graphragConfigDefaults.cache.connectionString,
    containerName: config.containerName ?? graphragConfigDefaults.cache.containerName,
    storageAccountBlobUrl: config.storageAccountBlobUrl ?? graphragConfigDefaults.cache.storageAccountBlobUrl,
    cosmosdbAccountUrl: config.cosmosdbAccountUrl ?? graphragConfigDefaults.cache.cosmosdbAccountUrl,
  };
}
