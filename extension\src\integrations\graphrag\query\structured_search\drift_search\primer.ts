// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Primer for DRIFT search.
 */

import { DRIFTSearchConfig } from '../../../config/models/drift-search-config';
import { CommunityReport } from '../../../data_model/community-report';
import { ChatModel, EmbeddingModel } from '../../../language_model/protocol/base';
import { DRIFT_PRIMER_PROMPT } from '../../../prompts/query/drift-search-system-prompt';
import { numTokens } from '../../llm/text-utils';
import { SearchResult } from '../base';
import { Encoding } from 'tiktoken';

const logger = console;

export interface PrimerQueryProcessorConfig {
    chatModel: ChatModel;
    textEmbedder: EmbeddingModel;
    reports: CommunityReport[];
    tokenEncoder?: Encoding;
}

export class PrimerQueryProcessor {
    private readonly chatModel: ChatModel;
    private readonly textEmbedder: EmbeddingModel;
    private readonly tokenEncoder?: Encoding;
    private readonly reports: CommunityReport[];

    constructor(config: PrimerQueryProcessorConfig) {
        this.chatModel = config.chatModel;
        this.textEmbedder = config.textEmbedder;
        this.tokenEncoder = config.tokenEncoder;
        this.reports = config.reports;
    }

    /**
     * Expand the query using a random community report template.
     */
    public async expandQuery(query: string): Promise<[string, Record<string, number>]> {
        const template = this.reports[Math.floor(Math.random() * this.reports.length)].fullContent;

        const prompt = `Create a hypothetical answer to the following query: ${query}\n\n
                  Format it to follow the structure of the template below:\n\n
                  ${template}\n"
                  Ensure that the hypothetical answer does not reference new named entities that are not present in the original query.`;

        const modelResponse = await this.chatModel.achat(prompt);
        const text = modelResponse.output.content;

        const promptTokens = numTokens(prompt, this.tokenEncoder);
        const outputTokens = numTokens(text, this.tokenEncoder);
        const tokenCt = {
            llm_calls: 1,
            prompt_tokens: promptTokens,
            output_tokens: outputTokens,
        };

        if (text === "") {
            logger.warn(`Failed to generate expansion for query: ${query}`);
            return [query, tokenCt];
        }
        return [text, tokenCt];
    }

    /**
     * Process the query, expand it, and embed the result.
     */
    public async process(query: string): Promise<[number[], Record<string, number>]> {
        const [hydeQuery, tokenCt] = await this.expandQuery(query);
        logger.debug(`Expanded query: ${hydeQuery}`);
        const embedding = await this.textEmbedder.embed(hydeQuery);
        return [embedding, tokenCt];
    }
}

export interface DRIFTPrimerConfig {
    config: DRIFTSearchConfig;
    chatModel: ChatModel;
    tokenEncoder?: Encoding;
}

export class DRIFTPrimer {
    private readonly chatModel: ChatModel;
    private readonly config: DRIFTSearchConfig;
    private readonly tokenEncoder?: Encoding;

    constructor(config: DRIFTPrimerConfig) {
        this.chatModel = config.chatModel;
        this.config = config.config;
        this.tokenEncoder = config.tokenEncoder;
    }

    /**
     * Decompose the query into subqueries based on the fetched global structures.
     */
    public async decomposeQuery(
        query: string, 
        reports: Record<string, any>[]
    ): Promise<[Record<string, any>, Record<string, number>]> {
        const communityReports = reports.map(report => report.full_content).join('\n\n');
        const prompt = DRIFT_PRIMER_PROMPT.replace('{query}', query).replace('{community_reports}', communityReports);
        
        const modelResponse = await this.chatModel.achat(prompt, { json: true });
        const response = modelResponse.output.content;

        let parsedResponse: Record<string, any>;
        try {
            parsedResponse = JSON.parse(response);
        } catch (error) {
            throw new Error(`Failed to parse JSON response: ${response}`);
        }

        const tokenCt = {
            llm_calls: 1,
            prompt_tokens: numTokens(prompt, this.tokenEncoder),
            output_tokens: numTokens(response, this.tokenEncoder),
        };

        return [parsedResponse, tokenCt];
    }

    /**
     * Asynchronous search method that processes the query and returns a SearchResult.
     */
    public async search(
        query: string,
        topKReports: Record<string, any>[]
    ): Promise<SearchResult> {
        const startTime = performance.now();
        const reportFolds = this.splitReports(topKReports);
        
        const tasks = reportFolds.map(fold => this.decomposeQuery(query, fold));
        const resultsWithTokens = await Promise.all(tasks);

        const completionTime = (performance.now() - startTime) / 1000; // Convert to seconds

        return new SearchResult({
            response: resultsWithTokens.map(([response]) => response),
            contextData: { top_k_reports: topKReports },
            contextText: JSON.stringify(topKReports) || "",
            completionTime,
            llmCalls: resultsWithTokens.length,
            promptTokens: resultsWithTokens.reduce((sum, [, ct]) => sum + ct.prompt_tokens, 0),
            outputTokens: resultsWithTokens.reduce((sum, [, ct]) => sum + ct.output_tokens, 0),
        });
    }

    /**
     * Split the reports into folds, allowing for parallel processing.
     */
    private splitReports(reports: Record<string, any>[]): Record<string, any>[][] {
        const primerFolds = this.config.primerFolds || 1; // Ensure at least one fold
        if (primerFolds === 1) {
            return [reports];
        }

        const foldSize = Math.ceil(reports.length / primerFolds);
        const folds: Record<string, any>[][] = [];
        
        for (let i = 0; i < reports.length; i += foldSize) {
            folds.push(reports.slice(i, i + foldSize));
        }
        
        return folds;
    }
}
