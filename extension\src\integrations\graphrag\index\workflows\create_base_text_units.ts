/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing run_workflow method definition.
 */

import { DataFrame } from '../../data-model/types';
import { WorkflowCallbacks } from '../../callbacks/workflow-callbacks';
import { ChunkStrategyType } from '../../config/models/chunking-config';
import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { chunkText } from '../operations/chunk-text/chunk-text';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { genSha512Hash } from '../utils/hashing';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

/**
 * All the steps to transform base text_units.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: create_base_text_units");
    
    const documents = await loadTableFromStorage("documents", context.outputStorage);
    const chunks = config.chunks;

    const output = createBaseTextUnits(
        documents,
        context.callbacks,
        chunks.groupByColumns,
        chunks.size,
        chunks.overlap,
        chunks.encodingModel,
        chunks.strategy,
        chunks.prependMetadata,
        chunks.chunkSizeIncludesMetadata
    );

    await writeTableToStorage(output, "text_units", context.outputStorage);

    logger.info("Workflow completed: create_base_text_units");
    return {
        result: output,
        stop: false
    };
}

/**
 * All the steps to transform base text_units.
 */
export function createBaseTextUnits(
    documents: DataFrame,
    callbacks: WorkflowCallbacks,
    groupByColumns: string[],
    size: number,
    overlap: number,
    encodingModel: string,
    strategy: ChunkStrategyType,
    prependMetadata: boolean = false,
    chunkSizeIncludesMetadata: boolean = false
): DataFrame {
    // Sort documents by id
    const sortedData = documents.data.sort((a, b) => 
        String(a.id).localeCompare(String(b.id))
    );

    // Add text_with_ids column
    const dataWithIds = sortedData.map(row => ({
        ...row,
        text_with_ids: [row.id, row.text]
    }));

    callbacks.progress?.(0, 'Starting text unit creation');

    // Group by specified columns
    const groupedData = new Map<string, any[]>();
    
    dataWithIds.forEach(row => {
        const groupKey = groupByColumns.length > 0 
            ? groupByColumns.map(col => row[col]).join('|')
            : 'default';
        
        if (!groupedData.has(groupKey)) {
            groupedData.set(groupKey, []);
        }
        groupedData.get(groupKey)!.push(row);
    });

    // Aggregate grouped data
    const aggregated = Array.from(groupedData.entries()).map(([groupKey, rows]) => {
        const firstRow = rows[0];
        const result: any = {};
        
        // Copy group by columns
        groupByColumns.forEach(col => {
            result[col] = firstRow[col];
        });
        
        // Aggregate text_with_ids
        result.texts = rows.map(row => row.text_with_ids);
        
        // Handle metadata
        if (rows[0].metadata) {
            result.metadata = rows[0].metadata;
        }
        
        return result;
    });

    const totalRows = aggregated.length;
    logger.info(`Starting chunking process for ${totalRows} documents`);

    // Process each aggregated row
    const processedRows: any[] = [];
    
    aggregated.forEach((row, index) => {
        const lineDelimiter = ".\n";
        let metadataStr = "";
        let metadataTokens = 0;

        // Handle metadata prepending
        if (prependMetadata && row.metadata) {
            let metadata = row.metadata;
            if (typeof metadata === 'string') {
                try {
                    metadata = JSON.parse(metadata);
                } catch (e) {
                    // If parsing fails, use as is
                }
            }
            
            if (typeof metadata === 'object' && metadata !== null) {
                metadataStr = Object.entries(metadata)
                    .map(([k, v]) => `${k}: ${v}`)
                    .join(lineDelimiter) + lineDelimiter;
            }

            if (chunkSizeIncludesMetadata) {
                // Simple token estimation - in reality you'd use proper tokenization
                metadataTokens = Math.ceil(metadataStr.length / 4);
                if (metadataTokens >= size) {
                    throw new Error("Metadata tokens exceeds the maximum tokens per chunk. Please increase the tokens per chunk.");
                }
            }
        }

        // Create temporary DataFrame for chunking
        const tempDF: DataFrame = {
            columns: ['texts'],
            data: [{ texts: row.texts }]
        };

        // Chunk the text
        const chunked = chunkText(
            tempDF,
            'texts',
            size - metadataTokens,
            overlap,
            encodingModel,
            strategy,
            callbacks
        )[0];

        // Prepend metadata if needed
        let finalChunks = chunked;
        if (prependMetadata && metadataStr) {
            finalChunks = chunked.map(chunk => {
                if (typeof chunk === 'string') {
                    return metadataStr + chunk;
                } else if (Array.isArray(chunk)) {
                    return [chunk[0], metadataStr + chunk[1], chunk[2]];
                }
                return chunk;
            });
        }

        // Add chunks to row
        const processedRow = { ...row, chunks: finalChunks };
        processedRows.push(processedRow);

        logger.info(`chunker progress: ${index + 1}/${totalRows}`);
    });

    // Explode chunks and create final structure
    const explodedData: any[] = [];
    
    processedRows.forEach(row => {
        if (row.chunks && Array.isArray(row.chunks)) {
            row.chunks.forEach((chunk: any) => {
                const explodedRow: any = {};
                
                // Copy group by columns
                groupByColumns.forEach(col => {
                    explodedRow[col] = row[col];
                });
                
                explodedRow.chunk = chunk;
                explodedData.push(explodedRow);
            });
        }
    });

    // Generate IDs and process chunks
    const finalData = explodedData
        .filter(row => row.chunk != null)
        .map(row => {
            // Generate ID
            const id = genSha512Hash(row, ['chunk']);
            
            // Extract chunk data
            let documentIds: string[];
            let text: string;
            let nTokens: number;
            
            if (typeof row.chunk === 'string') {
                documentIds = [];
                text = row.chunk;
                nTokens = Math.ceil(text.length / 4); // Simple estimation
            } else if (Array.isArray(row.chunk)) {
                documentIds = row.chunk[0] || [];
                text = row.chunk[1] || '';
                nTokens = row.chunk[2] || Math.ceil(text.length / 4);
            } else {
                documentIds = [];
                text = String(row.chunk);
                nTokens = Math.ceil(text.length / 4);
            }

            return {
                ...row,
                id,
                document_ids: documentIds,
                text,
                n_tokens: nTokens
            };
        })
        .filter(row => row.text && row.text.trim().length > 0);

    // Create final columns
    const finalColumns = [...groupByColumns, 'id', 'document_ids', 'text', 'n_tokens'];
    
    return {
        columns: finalColumns,
        data: finalData
    };
}