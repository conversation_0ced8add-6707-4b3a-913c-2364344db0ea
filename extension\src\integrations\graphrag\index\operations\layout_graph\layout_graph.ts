/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing layout_graph, _run_layout and _apply_layout_to_graph methods definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../data_model/types.js';
import { Graph } from '../../utils/graphs.js';
import { NodeEmbeddings } from '../embed_graph/typing.js';
import { GraphLayout } from './typing.js';
import { run as run_umap } from './umap.js';
import { run as run_zero } from './zero.js';

const logger = console;

/**
 * Apply a layout algorithm to a nx.Graph. The method returns a dataframe containing the node positions.
 * Matches the Python layout_graph function exactly.
 *
 * ## Usage
 * ```yaml
 * args:
 *     graph: The nx.Graph to layout
 *     embeddings: Embeddings for each node in the graph
 *     strategy: <strategy config> # See strategies section below
 * ```
 *
 * ## Strategies
 * The layout graph verb uses a strategy to layout the graph. The strategy is a json object which defines the strategy to use. The following strategies are available:
 *
 * ### umap
 * This strategy uses the umap algorithm to layout a graph. The strategy config is as follows:
 * ```yaml
 * strategy:
 *     type: umap
 *     n_neighbors: 5 # Optional, The number of neighbors to use for the umap algorithm, default: 5
 *     min_dist: 0.75 # Optional, The min distance to use for the umap algorithm, default: 0.75
 * ```
 */
export function layout_graph(
    graph: Graph,
    enabled: boolean,
    embeddings?: NodeEmbeddings | null
): DataFrame {
    const layout = _run_layout(
        graph,
        enabled,
        embeddings !== null && embeddings !== undefined ? embeddings : {}
    );

    // Python: layout_df = pd.DataFrame(layout)
    // return layout_df.loc[:, ["label", "x", "y", "size"]]
    const layout_data = layout.map(position => ({
        label: position.label,
        x: position.x,
        y: position.y,
        size: position.size
    }));

    return {
        columns: ['label', 'x', 'y', 'size'],
        data: layout_data
    };
}

/**
 * Run layout algorithm based on enabled flag.
 * Matches the Python _run_layout function exactly.
 */
function _run_layout(
    graph: Graph,
    enabled: boolean,
    embeddings: NodeEmbeddings
): GraphLayout {
    if (enabled) {
        // Python: from graphrag.index.operations.layout_graph.umap import run as run_umap
        return run_umap(
            graph,
            embeddings,
            (e: Error | null, stack: string | null, d: any) => {
                logger.error('Error in Umap', { exc_info: e, extra: { stack: stack, details: d } });
            }
        );
    }
    // Python: from graphrag.index.operations.layout_graph.zero import run as run_zero
    return run_zero(
        graph,
        (e: Error | null, stack: string | null, d: any) => {
            logger.error('Error in Zero', { exc_info: e, extra: { stack: stack, details: d } });
        }
    );
}