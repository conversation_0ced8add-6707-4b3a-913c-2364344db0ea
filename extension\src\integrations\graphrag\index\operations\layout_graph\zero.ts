/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing run and _create_node_position methods definitions.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Graph } from '../../utils/graphs.js';
import { GraphLayout, NodePosition } from './typing.js';
import { ErrorHandlerFn } from '../../typing/error_handler.js';

const logger = console;

/**
 * Run zero-position layout algorithm (places all nodes at origin).
 * Matches the Python run function exactly.
 */
export function run(
    graph: Graph,
    on_error: ErrorHandlerFn
): GraphLayout {
    const node_clusters: number[] = [];
    const node_sizes: number[] = [];

    const nodes = Array.from(graph.nodes.keys());

    for (const node_id of nodes) {
        const node = graph.nodes.get(node_id) || {};
        const cluster = node.cluster || node.community || -1;
        node_clusters.push(cluster);
        const size = node.degree || node.size || 0;
        node_sizes.push(size);
    }

    const additional_args: any = {};
    if (node_clusters.length > 0) {
        additional_args.node_categories = node_clusters;
    }
    if (node_sizes.length > 0) {
        additional_args.node_sizes = node_sizes;
    }

    try {
        return get_zero_positions(
            nodes,
            additional_args.node_categories,
            additional_args.node_sizes
        );
    } catch (e) {
        const error = e instanceof Error ? e : new Error(String(e));
        logger.error('Error running zero-position', error);
        on_error(error, error.stack || '', null);

        // Umap may fail due to input sparseness or memory pressure.
        // For now, in these cases, we'll just return a layout with all nodes at (0, 0)
        const result: NodePosition[] = [];
        for (let i = 0; i < nodes.length; i++) {
            const cluster = node_clusters.length > 0 ? node_clusters[i] : 1;
            result.push({
                x: 0,
                y: 0,
                label: nodes[i],
                size: 0,
                cluster: String(cluster)
            });
        }
        return result;
    }
}

/**
 * Get zero positions for all nodes (places all nodes at origin).
 * Matches the Python get_zero_positions function exactly.
 */
function get_zero_positions(
    node_labels: string[],
    node_categories?: number[],
    node_sizes?: number[],
    three_d: boolean = false
): NodePosition[] {
    const embedding_position_data: NodePosition[] = [];

    for (let index = 0; index < node_labels.length; index++) {
        const node_name = node_labels[index];
        const node_category = node_categories ? node_categories[index] : 1;
        const node_size = node_sizes ? node_sizes[index] : 1;

        if (!three_d) {
            embedding_position_data.push({
                label: String(node_name),
                x: 0,
                y: 0,
                cluster: String(Math.floor(node_category)),
                size: Math.floor(node_size)
            });
        } else {
            embedding_position_data.push({
                label: String(node_name),
                x: 0,
                y: 0,
                z: 0,
                cluster: String(Math.floor(node_category)),
                size: Math.floor(node_size)
            });
        }
    }

    return embedding_position_data;
}