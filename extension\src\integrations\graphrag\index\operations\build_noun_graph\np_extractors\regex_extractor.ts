/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Functions to analyze text data using regex patterns.
 * Converted from Python to TypeScript for GraphRAG integration.
 *
 * This extractor is based on TextBlob's fast NP extractor implementation,
 * which uses regex POS tagging and context-free grammars to detect noun phrases.
 * It only works for English but is much faster than syntactic parser-based extractors.
 */

import { BaseNounPhraseExtractor } from './base.js';

/**
 * Regular expression-based noun phrase extractor for English.
 *
 * This extractor is based on TextBlob's fast NP extractor, which uses a regex POS tagger
 * and context-free grammars to detect noun phrases. It's much faster but likely less
 * accurate than the syntactic parser-based extractor.
 */
export class RegexENNounPhraseExtractor extends BaseNounPhraseExtractor {
    constructor(
        excludeNouns: string[],
        maxWordLength: number,
        wordDelimiter: string
    ) {
        super(undefined, maxWordLength, excludeNouns, wordDelimiter);

        // In the Python version, this would download NLTK resources:
        // - brown corpus
        // - treebank corpus
        // - averaged_perceptron_tagger_eng
        // - punkt tokenizer
        // - punkt_tab tokenizer

        // For TypeScript, we use simplified regex-based patterns
        console.info('Using simplified regex-based noun phrase extraction for English');
    }

    /**
     * Extract noun phrases from text using regex patterns.
     * Note: This is a simplified implementation. In production, use proper NLP libraries.
     */
    extract(text: string): string[] {
        // Simplified noun phrase extraction using regex patterns
        // In a real implementation, you would use libraries like compromise or natural
        
        // Extract noun phrases using improved patterns
        const matches = this.extractNounPhrases(text);
        
        const properNouns = this.extractProperNouns(text);
        const taggedNounPhrases = matches.map((chunk: string) =>
            this.tagNounPhrases(chunk, properNouns)
        );

        const filteredNounPhrases = new Set<string>();
        
        for (const taggedNp of taggedNounPhrases) {
            if ((taggedNp.hasProperNouns || 
                 taggedNp.cleanedTokens.length > 1 || 
                 taggedNp.hasCompoundWords) && 
                taggedNp.hasValidTokens) {
                filteredNounPhrases.add(taggedNp.cleanedText);
            }
        }
        
        return Array.from(filteredNounPhrases);
    }

    /**
     * Extract noun phrases using multiple regex patterns.
     * This method attempts to mimic TextBlob's noun phrase extraction patterns.
     */
    private extractNounPhrases(text: string): string[] {
        const nounPhrases = new Set<string>();

        // Pattern 1: Capitalized words (proper nouns and noun phrases)
        const capitalizedPattern = /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g;
        let matches = text.match(capitalizedPattern) || [];
        matches.forEach(match => nounPhrases.add(match));

        // Pattern 2: Adjective + Noun patterns
        const adjNounPattern = /\b[a-z]+(?:ed|ing|ly|ous|ful|less|able|ible)\s+[a-z]+(?:s|es|ies)?\b/gi;
        matches = text.match(adjNounPattern) || [];
        matches.forEach(match => nounPhrases.add(match));

        // Pattern 3: Noun + Noun compounds
        const nounNounPattern = /\b[a-z]+(?:tion|sion|ment|ness|ity|ty|cy|ry|ly|er|or|ist|ism)\s+[a-z]+(?:s|es|ies)?\b/gi;
        matches = text.match(nounNounPattern) || [];
        matches.forEach(match => nounPhrases.add(match));

        // Pattern 4: Simple noun patterns
        const simpleNounPattern = /\b[a-z]+(?:s|es|ies|tion|sion|ment|ness|ity|ty|cy|ry)\b/gi;
        matches = text.match(simpleNounPattern) || [];
        matches.forEach(match => {
            if (match.length > 3) { // Filter out very short words
                nounPhrases.add(match);
            }
        });

        return Array.from(nounPhrases);
    }

    /**
     * Extract proper nouns from text (simplified implementation).
     */
    private extractProperNouns(text: string): string[] {
        // Simple pattern to match capitalized words
        const properNounPattern = /\b[A-Z][a-z]+\b/g;
        const matches = text.match(properNounPattern) || [];
        return matches.map((match: string) => match.toUpperCase());
    }

    /**
     * Extract attributes of a noun chunk, to be used for filtering.
     */
    private tagNounPhrases(
        nounPhrase: string, 
        allProperNouns: string[] = []
    ): {
        cleanedTokens: string[];
        cleanedText: string;
        hasProperNouns: boolean;
        hasCompoundWords: boolean;
        hasValidTokens: boolean;
    } {
        const tokens = nounPhrase.split(/\s+/).filter(token => token.length > 0);
        const cleanedTokens = tokens.filter(token => 
            !this.excludeNouns.includes(token.toUpperCase())
        );
        
        const hasProperNouns = cleanedTokens.some(token => 
            allProperNouns.includes(token.toUpperCase())
        );
        
        const hasCompoundWords = cleanedTokens.some(token => 
            token.includes('-') && 
            token.trim().length > 1 && 
            token.trim().split('-').length > 1
        );
        
        const hasValidTokens = cleanedTokens.every(token => 
            /^[a-zA-Z0-9\-]+\n?$/.test(token)
        ) && cleanedTokens.every(token => 
            token.length <= this.maxWordLength
        );
        
        const cleanedText = cleanedTokens
            .join(this.wordDelimiter)
            .replace(/\n/g, '')
            .toUpperCase();

        return {
            cleanedTokens,
            cleanedText,
            hasProperNouns,
            hasCompoundWords,
            hasValidTokens
        };
    }

    /**
     * Return string representation of the extractor, used for cache key generation.
     */
    toString(): string {
        return `regex_en_${this.excludeNouns.join(',')}_${this.maxWordLength}_${this.wordDelimiter}`;
    }
}