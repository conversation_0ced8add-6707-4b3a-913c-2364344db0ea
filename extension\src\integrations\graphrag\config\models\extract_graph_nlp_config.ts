// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the extract graph NLP configuration.
 */

import { graphragConfigDefaults } from '../defaults';
import { NounPhraseExtractorType } from '../enums';

/**
 * Configuration section for NLP text analyzer.
 */
export interface TextAnalyzerConfig {
  /**
   * The noun phrase extractor type.
   */
  extractorType: NounPhraseExtractorType;

  /**
   * The SpaCy model name.
   */
  modelName: string;

  /**
   * The max word length for NLP parsing.
   */
  maxWordLength: number;

  /**
   * The delimiter for splitting words.
   */
  wordDelimiter: string;

  /**
   * Whether to include named entities in noun phrases.
   */
  includeNamedEntities: boolean;

  /**
   * The list of excluded nouns (i.e., stopwords). If undefined, will use a default stopword list.
   */
  excludeNouns?: string[];

  /**
   * The list of named entity tags to exclude in noun phrases.
   */
  excludeEntityTags: string[];

  /**
   * The list of part-of-speech tags to remove in noun phrases.
   */
  excludePosTags: string[];

  /**
   * The list of noun phrase tags.
   */
  nounPhraseTags: string[];

  /**
   * The CFG for matching noun phrases. The key is a tuple of POS tags and the value is the grammar.
   */
  nounPhraseGrammars: Record<string, string>;
}

/**
 * Configuration section for graph extraction via NLP.
 */
export interface ExtractGraphNLPConfig {
  /**
   * Whether to normalize edge weights.
   */
  normalizeEdgeWeights: boolean;

  /**
   * The text analyzer configuration.
   */
  textAnalyzer: TextAnalyzerConfig;

  /**
   * The number of threads to use for the extraction process.
   */
  concurrentRequests: number;
}

/**
 * Create a TextAnalyzerConfig with default values.
 */
export function createTextAnalyzerConfig(config: Partial<TextAnalyzerConfig> = {}): TextAnalyzerConfig {
  const defaults = graphragConfigDefaults.extract_graph_nlp.text_analyzer;

  return {
    extractorType: config.extractorType ?? defaults.extractor_type,
    modelName: config.modelName ?? defaults.model_name,
    maxWordLength: config.maxWordLength ?? defaults.max_word_length,
    wordDelimiter: config.wordDelimiter ?? defaults.word_delimiter,
    includeNamedEntities: config.includeNamedEntities ?? defaults.include_named_entities,
    excludeNouns: config.excludeNouns ?? defaults.exclude_nouns,
    excludeEntityTags: config.excludeEntityTags ?? defaults.exclude_entity_tags,
    excludePosTags: config.excludePosTags ?? defaults.exclude_pos_tags,
    nounPhraseTags: config.nounPhraseTags ?? defaults.noun_phrase_tags,
    nounPhraseGrammars: config.nounPhraseGrammars ?? defaults.noun_phrase_grammars,
  };
}

/**
 * Create an ExtractGraphNLPConfig with default values.
 */
export function createExtractGraphNLPConfig(config: Partial<ExtractGraphNLPConfig> = {}): ExtractGraphNLPConfig {
  return {
    normalizeEdgeWeights: config.normalizeEdgeWeights ?? graphragConfigDefaults.extract_graph_nlp.normalize_edge_weights,
    textAnalyzer: config.textAnalyzer ?? createTextAnalyzerConfig(),
    concurrentRequests: config.concurrentRequests ?? graphragConfigDefaults.extract_graph_nlp.concurrent_requests,
  };
}

/**
 * Validation errors for configuration.
 */
export class ConfigValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ConfigValidationError';
  }
}

/**
 * Validate TextAnalyzerConfig.
 */
export function validateTextAnalyzerConfig(config: TextAnalyzerConfig): void {
  if (!config.modelName || config.modelName.trim() === '') {
    throw new ConfigValidationError('modelName cannot be empty');
  }

  if (config.maxWordLength <= 0) {
    throw new ConfigValidationError('maxWordLength must be positive');
  }

  if (!config.wordDelimiter) {
    throw new ConfigValidationError('wordDelimiter cannot be empty');
  }

  if (!Array.isArray(config.excludeEntityTags)) {
    throw new ConfigValidationError('excludeEntityTags must be an array');
  }

  if (!Array.isArray(config.excludePosTags)) {
    throw new ConfigValidationError('excludePosTags must be an array');
  }

  if (!Array.isArray(config.nounPhraseTags)) {
    throw new ConfigValidationError('nounPhraseTags must be an array');
  }

  if (typeof config.nounPhraseGrammars !== 'object' || config.nounPhraseGrammars === null) {
    throw new ConfigValidationError('nounPhraseGrammars must be an object');
  }
}

/**
 * Validate ExtractGraphNLPConfig.
 */
export function validateExtractGraphNLPConfig(config: ExtractGraphNLPConfig): void {
  if (typeof config.normalizeEdgeWeights !== 'boolean') {
    throw new ConfigValidationError('normalizeEdgeWeights must be a boolean');
  }

  if (config.concurrentRequests <= 0) {
    throw new ConfigValidationError('concurrentRequests must be positive');
  }

  validateTextAnalyzerConfig(config.textAnalyzer);
}

/**
 * Serialize TextAnalyzerConfig to JSON-compatible object.
 */
export function serializeTextAnalyzerConfig(config: TextAnalyzerConfig): Record<string, any> {
  return {
    extractor_type: config.extractorType,
    model_name: config.modelName,
    max_word_length: config.maxWordLength,
    word_delimiter: config.wordDelimiter,
    include_named_entities: config.includeNamedEntities,
    exclude_nouns: config.excludeNouns,
    exclude_entity_tags: config.excludeEntityTags,
    exclude_pos_tags: config.excludePosTags,
    noun_phrase_tags: config.nounPhraseTags,
    noun_phrase_grammars: config.nounPhraseGrammars,
  };
}

/**
 * Serialize ExtractGraphNLPConfig to JSON-compatible object.
 */
export function serializeExtractGraphNLPConfig(config: ExtractGraphNLPConfig): Record<string, any> {
  return {
    normalize_edge_weights: config.normalizeEdgeWeights,
    text_analyzer: serializeTextAnalyzerConfig(config.textAnalyzer),
    concurrent_requests: config.concurrentRequests,
  };
}

/**
 * Deserialize TextAnalyzerConfig from JSON-compatible object.
 */
export function deserializeTextAnalyzerConfig(data: Record<string, any>): TextAnalyzerConfig {
  return createTextAnalyzerConfig({
    extractorType: data.extractor_type,
    modelName: data.model_name,
    maxWordLength: data.max_word_length,
    wordDelimiter: data.word_delimiter,
    includeNamedEntities: data.include_named_entities,
    excludeNouns: data.exclude_nouns,
    excludeEntityTags: data.exclude_entity_tags,
    excludePosTags: data.exclude_pos_tags,
    nounPhraseTags: data.noun_phrase_tags,
    nounPhraseGrammars: data.noun_phrase_grammars,
  });
}

/**
 * Deserialize ExtractGraphNLPConfig from JSON-compatible object.
 */
export function deserializeExtractGraphNLPConfig(data: Record<string, any>): ExtractGraphNLPConfig {
  return createExtractGraphNLPConfig({
    normalizeEdgeWeights: data.normalize_edge_weights,
    textAnalyzer: data.text_analyzer ? deserializeTextAnalyzerConfig(data.text_analyzer) : undefined,
    concurrentRequests: data.concurrent_requests,
  });
}
