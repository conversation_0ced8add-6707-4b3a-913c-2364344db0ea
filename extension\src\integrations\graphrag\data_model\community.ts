/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A package containing the 'Community' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Named } from './named'

/**
 * An interface for a community in the system.
 */
export interface Community extends Named {
    /** Community level. */
    level: string

    /** Community ID of the parent node of this community. */
    parent: string

    /** List of community IDs of the child nodes of this community. */
    children: string[]

    /** List of entity IDs related to the community (optional). */
    entity_ids?: string[]

    /** List of relationship IDs related to the community (optional). */
    relationship_ids?: string[]

    /** List of text unit IDs related to the community (optional). */
    text_unit_ids?: string[]

    /** Dictionary of different types of covariates related to the community (optional), e.g. claims */
    covariate_ids?: Record<string, string[]>

    /** A dictionary of additional attributes associated with the community (optional). To be included in the search prompt. */
    attributes?: Record<string, any>

    /** The size of the community (Amount of text units). */
    size?: number

    /** Period information. */
    period?: string
}

/**
 * Create a new community from the dict data.
 */
export function createCommunityFromDict(
    d: Record<string, any>,
    options: {
        id_key?: string
        title_key?: string
        short_id_key?: string
        level_key?: string
        entities_key?: string
        relationships_key?: string
        text_units_key?: string
        covariates_key?: string
        parent_key?: string
        children_key?: string
        attributes_key?: string
        size_key?: string
        period_key?: string
    } = {}
): Community {
    const {
        id_key = "id",
        title_key = "title",
        short_id_key = "human_readable_id",
        level_key = "level",
        entities_key = "entity_ids",
        relationships_key = "relationship_ids",
        text_units_key = "text_unit_ids",
        covariates_key = "covariate_ids",
        parent_key = "parent",
        children_key = "children",
        attributes_key = "attributes",
        size_key = "size",
        period_key = "period"
    } = options

    return {
        id: d[id_key],
        title: d[title_key],
        level: d[level_key],
        parent: d[parent_key],
        children: d[children_key],
        short_id: d[short_id_key],
        entity_ids: d[entities_key],
        relationship_ids: d[relationships_key],
        text_unit_ids: d[text_units_key],
        covariate_ids: d[covariates_key],
        attributes: d[attributes_key],
        size: d[size_key],
        period: d[period_key]
    }
}