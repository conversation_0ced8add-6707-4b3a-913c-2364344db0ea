// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * CLI functions for the GraphRAG module.
 */

import * as fs from 'fs';
import * as path from 'path';

/**
 * Check for file existence.
 */
export function fileExist(filePath: string): string {
    if (!fs.existsSync(filePath) || !fs.statSync(filePath).isFile()) {
        const msg = `File not found: ${filePath}`;
        throw new Error(msg);
    }
    return filePath;
}

/**
 * Check for directory existence.
 */
export function dirExist(dirPath: string): string {
    if (!fs.existsSync(dirPath) || !fs.statSync(dirPath).isDirectory()) {
        const msg = `Directory not found: ${dirPath}`;
        throw new Error(msg);
    }
    return dirPath;
}

/**
 * Sanitize secrets in a config object.
 */
export function redact(config: Record<string, any>): string {
    // Redact any sensitive configuration
    function redactDict(config: any): any {
        if (typeof config !== 'object' || config === null) {
            return config;
        }

        if (Array.isArray(config)) {
            return config.map(item => redactDict(item));
        }

        const result: Record<string, any> = {};
        for (const [key, value] of Object.entries(config)) {
            if ([
                "api_key",
                "connection_string",
                "container_name",
                "organization",
            ].includes(key)) {
                if (value !== null && value !== undefined) {
                    result[key] = "==== REDACTED ====";
                } else {
                    result[key] = value;
                }
            } else if (typeof value === 'object' && value !== null) {
                if (Array.isArray(value)) {
                    result[key] = value.map(item => redactDict(item));
                } else {
                    result[key] = redactDict(value);
                }
            } else {
                result[key] = value;
            }
        }
        return result;
    }

    const redactedDict = redactDict(config);
    return JSON.stringify(redactedDict, null, 4);
}