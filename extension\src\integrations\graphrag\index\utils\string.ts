/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * String utilities.
 */

/**
 * Clean an input string by removing HTML escapes, control characters, and other unwanted characters.
 * @param input - The input to clean
 * @returns Cleaned string
 */
export function cleanStr(input: any): string {
    // If we get non-string input, just give it back
    if (typeof input !== 'string') {
        return input;
    }

    // Unescape HTML entities
    const unescaped = input
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .trim();

    // Remove control characters (0x00-0x1f and 0x7f-0x9f)
    return unescaped.replace(/[\x00-\x1f\x7f-\x9f]/g, '');
}