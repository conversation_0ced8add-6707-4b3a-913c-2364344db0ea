/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A package containing the 'Covariate' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Identified } from './identified'

/**
 * An interface for a covariate in the system.
 * 
 * Covariates are metadata associated with a subject, e.g. entity claims.
 * Each subject (e.g. entity) may be associated with multiple types of covariates.
 */
export interface Covariate extends Identified {
    /** The subject id. */
    subject_id: string

    /** The subject type. */
    subject_type: string

    /** The covariate type. */
    covariate_type: string

    /** List of text unit IDs in which the covariate info appears (optional). */
    text_unit_ids?: string[]

    /** Additional attributes associated with the covariate (optional). */
    attributes?: Record<string, any>
}

/**
 * Create a new covariate with default values.
 */
export function createCovariate(
    id: string,
    subject_id: string,
    options: {
        short_id?: string
        subject_type?: string
        covariate_type?: string
        text_unit_ids?: string[]
        attributes?: Record<string, any>
    } = {}
): Covariate {
    return {
        id,
        subject_id,
        short_id: options.short_id,
        subject_type: options.subject_type ?? "entity",
        covariate_type: options.covariate_type ?? "claim",
        text_unit_ids: options.text_unit_ids,
        attributes: options.attributes
    }
}

/**
 * Create a new covariate from the dict data.
 */
export function createCovariateFromDict(
    d: Record<string, any>,
    options: {
        id_key?: string
        subject_id_key?: string
        covariate_type_key?: string
        short_id_key?: string
        text_unit_ids_key?: string
        attributes_key?: string
    } = {}
): Covariate {
    const {
        id_key = "id",
        subject_id_key = "subject_id",
        covariate_type_key = "covariate_type",
        short_id_key = "human_readable_id",
        text_unit_ids_key = "text_unit_ids",
        attributes_key = "attributes"
    } = options

    return {
        id: d[id_key],
        short_id: d[short_id_key],
        subject_id: d[subject_id_key],
        subject_type: d["subject_type"] ?? "entity",
        covariate_type: d[covariate_type_key] ?? "claim",
        text_unit_ids: d[text_unit_ids_key],
        attributes: d[attributes_key]
    }
}
