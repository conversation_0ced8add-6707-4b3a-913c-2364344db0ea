/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Base class for noun phrase extractors.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

const logger = console;

/**
 * SpaCy token interface for TypeScript compatibility.
 */
export interface SpacyToken {
    text: string;
    pos_: string;
    lemma_: string;
    is_space: boolean;
    is_punct: boolean;
    ent_type_: string;
}

/**
 * SpaCy span interface for TypeScript compatibility.
 */
export interface SpacySpan {
    text: string;
    label_: string;
    start: number;
    end: number;
}

/**
 * SpaCy document interface for TypeScript compatibility.
 */
export interface SpacyDoc {
    text: string;
    tokens: SpacyToken[];
    ents: SpacySpan[];
    noun_chunks: SpacySpan[];
}

/**
 * SpaCy NLP processor interface.
 */
export interface SpacyNLP {
    (text: string): SpacyDoc;
}

/**
 * Abstract base class for noun phrase extractors.
 */
export abstract class BaseNounPhraseExtractor {
    protected modelName?: string;
    protected maxWordLength: number;
    protected excludeNouns: string[];
    protected wordDelimiter: string;

    constructor(
        modelName?: string,
        maxWordLength: number = 15,
        excludeNouns?: string[],
        wordDelimiter: string = " "
    ) {
        this.modelName = modelName;
        this.maxWordLength = maxWordLength;
        this.excludeNouns = (excludeNouns || []).map(noun => noun.toUpperCase());
        this.wordDelimiter = wordDelimiter;
    }

    /**
     * Extract noun phrases from text.
     * @param text - Text to extract noun phrases from
     * @returns List of noun phrases
     */
    abstract extract(text: string): string[];

    /**
     * Return string representation of the extractor, used for cache key generation.
     */
    abstract toString(): string;

    /**
     * Load a SpaCy-like model with proper configuration.
     * This is a mock implementation - in production, you would integrate with
     * a JavaScript NLP library like compromise, natural, or spacy-js.
     *
     * @param modelName - Name of the model to load
     * @param exclude - List of pipeline components to exclude
     * @returns SpaCy NLP processor
     */
    public loadSpacyModel(
        modelName: string,
        exclude?: string[]
    ): SpacyNLP {
        logger.info(`Loading SpaCy model: ${modelName}, excluding: ${exclude?.join(', ') || 'none'}`);

        // Mock implementation - in reality you'd use a proper NLP library
        return (text: string): SpacyDoc => {
            // Simple tokenization and mock processing
            const tokens = this.tokenizeText(text);
            const entities = this.extractEntities(text, tokens);
            const nounChunks = this.extractNounChunks(tokens);

            return {
                text,
                tokens,
                ents: entities,
                noun_chunks: nounChunks
            };
        };
    }

    /**
     * Simple text tokenization (mock implementation).
     */
    private tokenizeText(text: string): SpacyToken[] {
        const words = text.split(/\s+/);
        return words.map(word => ({
            text: word,
            pos_: this.mockPosTag(word),
            lemma_: word.toLowerCase(),
            is_space: false,
            is_punct: /^[^\w\s]$/.test(word),
            ent_type_: ''
        }));
    }

    /**
     * Mock POS tagging (simplified implementation).
     */
    private mockPosTag(word: string): string {
        // Very basic POS tagging rules
        if (/^[A-Z]/.test(word)) return 'PROPN';
        if (/ing$/.test(word)) return 'VERB';
        if (/ed$/.test(word)) return 'VERB';
        if (/ly$/.test(word)) return 'ADV';
        if (/tion$/.test(word)) return 'NOUN';
        return 'NOUN'; // Default to noun
    }

    /**
     * Mock entity extraction (simplified implementation).
     */
    private extractEntities(text: string, _tokens: SpacyToken[]): SpacySpan[] {
        const entities: SpacySpan[] = [];
        // Simple pattern matching for entities
        const patterns = [
            { regex: /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g, label: 'PERSON' },
            { regex: /\b[A-Z][a-z]+ Inc\b/g, label: 'ORG' },
            { regex: /\b[A-Z][a-z]+ Corp\b/g, label: 'ORG' }
        ];

        for (const pattern of patterns) {
            let match: RegExpExecArray | null;
            while ((match = pattern.regex.exec(text)) !== null) {
                entities.push({
                    text: match[0],
                    label_: pattern.label,
                    start: match.index,
                    end: match.index + match[0].length
                });
            }
        }

        return entities;
    }

    /**
     * Mock noun chunk extraction (simplified implementation).
     */
    private extractNounChunks(tokens: SpacyToken[]): SpacySpan[] {
        const chunks: SpacySpan[] = [];
        let currentChunk: string[] = [];
        let startIndex = 0;

        for (let i = 0; i < tokens.length; i++) {
            const token = tokens[i];

            if (token.pos_ === 'NOUN' || token.pos_ === 'PROPN' || token.pos_ === 'ADJ') {
                if (currentChunk.length === 0) {
                    startIndex = i;
                }
                currentChunk.push(token.text);
            } else {
                if (currentChunk.length > 0) {
                    chunks.push({
                        text: currentChunk.join(' '),
                        label_: 'NP',
                        start: startIndex,
                        end: i
                    });
                    currentChunk = [];
                }
            }
        }

        // Handle final chunk
        if (currentChunk.length > 0) {
            chunks.push({
                text: currentChunk.join(' '),
                label_: 'NP',
                start: startIndex,
                end: tokens.length
            });
        }

        return chunks;
    }
}