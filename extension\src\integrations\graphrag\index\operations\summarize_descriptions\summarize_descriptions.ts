/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the summarize_descriptions verb.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../data_model/types.js';
import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../callbacks/workflow_callbacks.js';
import {
    SummarizationStrategy,
    SummarizeStrategyType,
    SummarizedDescriptionResult
} from './typing.js';
import { progressTicker, ProgressTicker } from '../../../logger/progress.js';
import { run_graph_intelligence } from './graph_intelligence_strategy.js';

const logger = console;

/**
 * Summarize entity and relationship descriptions from an entity graph, using a language model.
 * Matches the Python summarize_descriptions function exactly.
 */
export async function summarize_descriptions(
    entities_df: DataFrame,
    relationships_df: DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    strategy?: Record<string, any>,
    num_threads: number = 4
): Promise<[DataFrame, DataFrame]> {
    // Python: logger.debug("summarize_descriptions strategy=%s", strategy)
    logger.debug("summarize_descriptions strategy=", strategy);

    // Python: strategy = strategy or {}
    strategy = strategy || {};

    // Python: strategy_exec = load_strategy(strategy.get("type", SummarizeStrategyType.graph_intelligence))
    const strategy_exec = load_strategy(
        strategy.type || SummarizeStrategyType.graph_intelligence
    );

    // Python: strategy_config = {**strategy}
    const strategy_config = { ...strategy };

    // Python: async def get_summarized(nodes: pd.DataFrame, edges: pd.DataFrame, semaphore: asyncio.Semaphore):
    async function get_summarized(
        nodes: DataFrame,
        edges: DataFrame,
        semaphore: Semaphore
    ): Promise<[DataFrame, DataFrame]> {
        // Python: ticker_length = len(nodes) + len(edges)
        const ticker_length = nodes.data.length + edges.data.length;

        // Python: ticker = progress_ticker(callbacks.progress, ticker_length, description="Summarize entity/relationship description progress: ")
        const ticker = progressTicker(
            callbacks.progress,
            ticker_length,
            "Summarize entity/relationship description progress: "
        );

        // Python: node_futures = [do_summarize_descriptions(...) for row in nodes.itertuples(index=False)]
        const node_futures = nodes.data.map(row =>
            do_summarize_descriptions(
                String(row.title),
                Array.from(new Set(row.description as string[])).sort(),
                ticker,
                semaphore
            )
        );

        // Python: node_results = await asyncio.gather(*node_futures)
        const node_results = await Promise.all(node_futures);

        // Python: node_descriptions = [{"title": result.id, "description": result.description} for result in node_results]
        const node_descriptions = node_results.map(result => ({
            title: result.id,
            description: result.description
        }));

        // Python: edge_futures = [do_summarize_descriptions(...) for row in edges.itertuples(index=False)]
        const edge_futures = edges.data.map(row =>
            do_summarize_descriptions(
                [String(row.source), String(row.target)],
                Array.from(new Set(row.description as string[])).sort(),
                ticker,
                semaphore
            )
        );

        // Python: edge_results = await asyncio.gather(*edge_futures)
        const edge_results = await Promise.all(edge_futures);

        // Python: edge_descriptions = [{"source": result.id[0], "target": result.id[1], "description": result.description} for result in edge_results]
        const edge_descriptions = edge_results.map(result => ({
            source: (result.id as [string, string])[0],
            target: (result.id as [string, string])[1],
            description: result.description
        }));

        // Python: entity_descriptions = pd.DataFrame(node_descriptions)
        const entity_descriptions: DataFrame = {
            columns: ['title', 'description'],
            data: node_descriptions
        };

        // Python: relationship_descriptions = pd.DataFrame(edge_descriptions)
        const relationship_descriptions: DataFrame = {
            columns: ['source', 'target', 'description'],
            data: edge_descriptions
        };

        // Python: return entity_descriptions, relationship_descriptions
        return [entity_descriptions, relationship_descriptions];
    }

    // Python: async def do_summarize_descriptions(id, descriptions, ticker, semaphore):
    async function do_summarize_descriptions(
        id: string | [string, string],
        descriptions: string[],
        ticker: ProgressTicker,
        semaphore: Semaphore
    ): Promise<SummarizedDescriptionResult> {
        // Python: async with semaphore:
        await semaphore.acquire();
        try {
            // Python: results = await strategy_exec(id, descriptions, cache, strategy_config)
            const results = await strategy_exec(id, descriptions, cache, strategy_config);
            // Python: ticker(1)
            ticker(1);
            return results;
        } finally {
            semaphore.release();
        }
    }

    // Python: semaphore = asyncio.Semaphore(num_threads)
    const semaphore = new Semaphore(num_threads);

    // Python: return await get_summarized(entities_df, relationships_df, semaphore)
    return await get_summarized(entities_df, relationships_df, semaphore);
}

/**
 * Load strategy method definition.
 * Matches the Python load_strategy function exactly.
 */
function load_strategy(strategy_type: SummarizeStrategyType): SummarizationStrategy {
    // Python: match strategy_type:
    switch (strategy_type) {
        // Python: case SummarizeStrategyType.graph_intelligence:
        case SummarizeStrategyType.graph_intelligence:
            // Python: from graphrag.index.operations.summarize_descriptions.graph_intelligence_strategy import run_graph_intelligence
            // Python: return run_graph_intelligence
            return run_graph_intelligence;
        // Python: case _:
        default:
            // Python: msg = f"Unknown strategy: {strategy_type}"
            // Python: raise ValueError(msg)
            const msg = `Unknown strategy: ${strategy_type}`;
            throw new Error(msg);
    }
}

// Compatibility export for existing code
export const summarizeDescriptions = summarize_descriptions;
export const loadStrategy = load_strategy;

/**
 * Simple semaphore implementation for controlling concurrency.
 */
class Semaphore {
    private permits: number;
    private waitQueue: Array<() => void> = [];

    constructor(permits: number) {
        this.permits = permits;
    }

    async acquire(): Promise<void> {
        if (this.permits > 0) {
            this.permits--;
            return;
        }

        return new Promise<void>((resolve) => {
            this.waitQueue.push(resolve);
        });
    }

    release(): void {
        if (this.waitQueue.length > 0) {
            const resolve = this.waitQueue.shift()!;
            resolve();
        } else {
            this.permits++;
        }
    }
}