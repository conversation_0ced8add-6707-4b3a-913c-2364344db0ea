/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * UUID utilities.
 */

import { randomBytes } from 'crypto';

/**
 * Generate a random UUID v4.
 * @param rd - Optional random number generator (not used in Node.js implementation)
 * @returns UUID v4 string without dashes
 */
export function genUuid(rd?: any): string {
    // Generate 16 random bytes
    const bytes = randomBytes(16);
    
    // Set version (4) and variant bits
    bytes[6] = (bytes[6] & 0x0f) | 0x40; // Version 4
    bytes[8] = (bytes[8] & 0x3f) | 0x80; // Variant 10
    
    // Convert to hex string
    return bytes.toString('hex');
}