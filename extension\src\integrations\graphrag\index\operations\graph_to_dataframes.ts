/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing graph_to_dataframes definition.
 */

import { DataFrame } from '../../data-model/types';
import { Graph } from '../utils/graphs';

/**
 * Deconstructs a Graph into nodes and edges dataframes.
 * @param graph - The graph to deconstruct
 * @param nodeColumns - Optional list of node columns to include
 * @param edgeColumns - Optional list of edge columns to include
 * @param nodeId - Column name for node ID (default: "title")
 * @returns Tuple of [nodes DataFrame, edges DataFrame]
 */
export function graphToDataframes(
    graph: Graph,
    nodeColumns?: string[],
    edgeColumns?: string[],
    nodeId: string = "title"
): [DataFrame, DataFrame] {
    // Create nodes DataFrame
    const nodesData: any[] = [];
    graph.nodes.forEach((nodeData, nodeKey) => {
        const row = {
            ...nodeData,
            [nodeId]: nodeKey
        };
        nodesData.push(row);
    });

    // Create edges DataFrame
    const edgesData: any[] = [];
    graph.edges.forEach((edge, edgeKey) => {
        // Ensure consistent ordering for undirected graphs
        const source = edge.source;
        const target = edge.target;
        const minSource = source < target ? source : target;
        const maxTarget = source < target ? target : source;

        const row = {
            ...edge.data,
            source: minSource,
            target: maxTarget,
            weight: edge.weight
        };
        edgesData.push(row);
    });

    // Create DataFrames
    let nodesDF: DataFrame = {
        columns: nodeColumns || (nodesData.length > 0 ? Object.keys(nodesData[0]) : [nodeId]),
        data: nodesData
    };

    let edgesDF: DataFrame = {
        columns: edgeColumns || (edgesData.length > 0 ? Object.keys(edgesData[0]) : ['source', 'target']),
        data: edgesData
    };

    // Filter columns if specified
    if (nodeColumns) {
        const filteredNodesData = nodesData.map(row => {
            const newRow: Record<string, any> = {};
            nodeColumns.forEach(col => {
                if (col in row) {
                    newRow[col] = row[col];
                }
            });
            return newRow;
        });
        nodesDF = {
            columns: nodeColumns,
            data: filteredNodesData
        };
    }

    if (edgeColumns) {
        const filteredEdgesData = edgesData.map(row => {
            const newRow: Record<string, any> = {};
            edgeColumns.forEach(col => {
                if (col in row) {
                    newRow[col] = row[col];
                }
            });
            return newRow;
        });
        edgesDF = {
            columns: edgeColumns,
            data: filteredEdgesData
        };
    }

    return [nodesDF, edgesDF];
}