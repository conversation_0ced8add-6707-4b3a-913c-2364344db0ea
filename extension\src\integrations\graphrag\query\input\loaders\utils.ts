// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Data load utils.
 */

/**
 * Retrieve a column value from data.
 * 
 * If `required` is true, throws an error when:
 *   - columnName is undefined, or
 *   - columnName is not in data.
 * 
 * For optional columns (required=false), returns undefined if columnName is undefined.
 */
function getValue(
  data: Record<string, any>,
  columnName: string | undefined,
  required: boolean = true
): any {
  if (columnName === undefined) {
    if (required) {
      throw new Error('Column name is undefined');
    }
    return undefined;
  }
  
  if (columnName in data) {
    return data[columnName];
  }
  
  if (required) {
    throw new Error(`Column [${columnName}] not found in data`);
  }
  
  return undefined;
}

/**
 * Convert and validate a value to a string.
 */
export function toStr(data: Record<string, any>, columnName: string | undefined): string {
  const value = getValue(data, columnName, true);
  return String(value);
}

/**
 * Convert and validate a value to an optional string.
 */
export function toOptionalStr(data: Record<string, any>, columnName: string | undefined): string | undefined {
  const value = getValue(data, columnName, true);
  return value === null || value === undefined ? undefined : String(value);
}

/**
 * Convert and validate a value to a list.
 */
export function toList<T = any>(
  data: Record<string, any>,
  columnName: string | undefined,
  itemType?: new (...args: any[]) => T
): T[] {
  let value = getValue(data, columnName, true);
  
  // Handle numpy-like arrays (convert to regular array)
  if (value && typeof value === 'object' && 'tolist' in value) {
    value = value.tolist();
  }
  
  if (!Array.isArray(value)) {
    throw new TypeError(`value is not a list: ${value} (${typeof value})`);
  }
  
  if (itemType) {
    for (const v of value) {
      if (typeof itemType === 'function') {
        // For primitive types like String, Number
        if (itemType === String && typeof v !== 'string') {
          throw new TypeError(`list item is not string: ${v} (${typeof v})`);
        }
        if (itemType === Number && typeof v !== 'number') {
          throw new TypeError(`list item is not number: ${v} (${typeof v})`);
        }
      }
    }
  }
  
  return value;
}

/**
 * Convert and validate a value to an optional list.
 */
export function toOptionalList<T = any>(
  data: Record<string, any>,
  columnName: string | undefined,
  itemType?: new (...args: any[]) => T
): T[] | undefined {
  if (columnName === undefined || !(columnName in data)) {
    return undefined;
  }
  
  let value = data[columnName];
  if (value === null || value === undefined) {
    return undefined;
  }
  
  // Handle numpy-like arrays
  if (value && typeof value === 'object' && 'tolist' in value) {
    value = value.tolist();
  }
  
  // Handle single string values
  if (typeof value === 'string') {
    value = [value];
  }
  
  if (!Array.isArray(value)) {
    throw new TypeError(`value is not a list: ${value} (${typeof value})`);
  }
  
  if (itemType) {
    for (const v of value) {
      if (typeof itemType === 'function') {
        if (itemType === String && typeof v !== 'string') {
          throw new TypeError(`list item is not string: ${v} (${typeof v})`);
        }
        if (itemType === Number && typeof v !== 'number') {
          throw new TypeError(`list item is not number: ${v} (${typeof v})`);
        }
      }
    }
  }
  
  return value;
}

/**
 * Convert and validate a value to an int.
 */
export function toInt(data: Record<string, any>, columnName: string | undefined): number {
  let value = getValue(data, columnName, true);
  
  if (typeof value === 'string') {
    value = parseInt(value, 10);
  }
  
  if (typeof value === 'number' && !Number.isInteger(value)) {
    value = Math.floor(value);
  }
  
  if (!Number.isInteger(value)) {
    throw new TypeError(`value is not an int: ${value} (${typeof value})`);
  }
  
  return value;
}

/**
 * Convert and validate a value to an optional int.
 */
export function toOptionalInt(data: Record<string, any>, columnName: string | undefined): number | undefined {
  if (columnName === undefined || !(columnName in data)) {
    return undefined;
  }
  
  let value = data[columnName];
  if (value === null || value === undefined) {
    return undefined;
  }
  
  if (typeof value === 'string') {
    value = parseInt(value, 10);
  }
  
  if (typeof value === 'number' && !Number.isInteger(value)) {
    value = Math.floor(value);
  }
  
  if (!Number.isInteger(value)) {
    throw new TypeError(`value is not an int: ${value} (${typeof value})`);
  }
  
  return value;
}

/**
 * Convert and validate a value to a float.
 */
export function toFloat(data: Record<string, any>, columnName: string | undefined): number {
  const value = getValue(data, columnName, true);
  
  if (typeof value !== 'number') {
    throw new TypeError(`value is not a float: ${value} (${typeof value})`);
  }
  
  return value;
}

/**
 * Convert and validate a value to an optional float.
 */
export function toOptionalFloat(data: Record<string, any>, columnName: string | undefined): number | undefined {
  if (columnName === undefined || !(columnName in data)) {
    return undefined;
  }
  
  const value = data[columnName];
  if (value === null || value === undefined) {
    return undefined;
  }
  
  if (typeof value !== 'number') {
    return Number(value);
  }
  
  return value;
}

/**
 * Convert and validate a value to a dict.
 */
export function toDict<K = string, V = any>(
  data: Record<string, any>,
  columnName: string | undefined,
  keyType?: new (...args: any[]) => K,
  valueType?: new (...args: any[]) => V
): Record<string, V> {
  const value = getValue(data, columnName, true);
  
  if (typeof value !== 'object' || value === null || Array.isArray(value)) {
    throw new TypeError(`value is not a dict: ${value} (${typeof value})`);
  }
  
  if (keyType) {
    for (const k of Object.keys(value)) {
      if (keyType === String && typeof k !== 'string') {
        throw new TypeError(`dict key is not string: ${k} (${typeof k})`);
      }
    }
  }
  
  if (valueType) {
    for (const v of Object.values(value)) {
      if (valueType === String && typeof v !== 'string') {
        throw new TypeError(`dict value is not string: ${v} (${typeof v})`);
      }
    }
  }
  
  return value;
}

/**
 * Convert and validate a value to an optional dict.
 */
export function toOptionalDict<K = string, V = any>(
  data: Record<string, any>,
  columnName: string | undefined,
  keyType?: new (...args: any[]) => K,
  valueType?: new (...args: any[]) => V
): Record<string, V> | undefined {
  if (columnName === undefined || !(columnName in data)) {
    return undefined;
  }
  
  const value = data[columnName];
  if (value === null || value === undefined) {
    return undefined;
  }
  
  if (typeof value !== 'object' || Array.isArray(value)) {
    throw new TypeError(`value is not a dict: ${value} (${typeof value})`);
  }
  
  if (keyType) {
    for (const k of Object.keys(value)) {
      if (keyType === String && typeof k !== 'string') {
        throw new TypeError(`dict key is not string: ${k} (${typeof k})`);
      }
    }
  }
  
  if (valueType) {
    for (const v of Object.values(value)) {
      if (valueType === String && typeof v !== 'string') {
        throw new TypeError(`dict value is not string: ${v} (${typeof v})`);
      }
    }
  }
  
  return value;
}