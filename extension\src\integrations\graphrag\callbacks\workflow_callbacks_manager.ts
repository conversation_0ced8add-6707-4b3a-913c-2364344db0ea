// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing the WorkflowCallbacks registry.
 */

import { WorkflowCallbacks } from './workflow_callbacks';
import { PipelineRunResult } from '../index/typing/pipeline_run_result';
import { Progress } from '../logger/progress';

/**
 * A registry of WorkflowCallbacks.
 */
export class WorkflowCallbacksManager implements WorkflowCallbacks {
    private _callbacks: WorkflowCallbacks[];

    /**
     * Create a new instance of WorkflowCallbacksRegistry.
     */
    constructor() {
        this._callbacks = [];
    }

    /**
     * Register a new WorkflowCallbacks type.
     */
    register(callbacks: WorkflowCallbacks): void {
        this._callbacks.push(callbacks);
    }

    /**
     * Execute this callback when a the entire pipeline starts.
     */
    pipeline_start(names: string[]): void {
        for (const callback of this._callbacks) {
            if ('pipeline_start' in callback && typeof callback.pipeline_start === 'function') {
                callback.pipeline_start(names);
            }
        }
    }

    /**
     * Execute this callback when the entire pipeline ends.
     */
    pipeline_end(results: PipelineRunResult[]): void {
        for (const callback of this._callbacks) {
            if ('pipeline_end' in callback && typeof callback.pipeline_end === 'function') {
                callback.pipeline_end(results);
            }
        }
    }

    /**
     * Execute this callback when a workflow starts.
     */
    workflow_start(name: string, instance: object): void {
        for (const callback of this._callbacks) {
            if ('workflow_start' in callback && typeof callback.workflow_start === 'function') {
                callback.workflow_start(name, instance);
            }
        }
    }

    /**
     * Execute this callback when a workflow ends.
     */
    workflow_end(name: string, instance: object): void {
        for (const callback of this._callbacks) {
            if ('workflow_end' in callback && typeof callback.workflow_end === 'function') {
                callback.workflow_end(name, instance);
            }
        }
    }

    /**
     * Handle when progress occurs.
     */
    progress(progress: Progress): void {
        for (const callback of this._callbacks) {
            if ('progress' in callback && typeof callback.progress === 'function') {
                callback.progress(progress);
            }
        }
    }
}