/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Graph intelligence strategy for description summarization.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { SummarizedDescriptionResult, StrategyConfig } from './typing.js';
import { SummarizeExtractor } from './description_summary_extractor.js';
import { LanguageModelConfig } from '../../../config/models/language_model_config.js';
import { ModelManager } from '../../../language_model/manager.js';
import { ChatModel } from '../../../language_model/protocol/base.js';

const logger = console;

/**
 * Run graph intelligence strategy for description summarization.
 * Matches the Python run_graph_intelligence function exactly.
 */
export async function run_graph_intelligence(
    id: string | [string, string],
    descriptions: string[],
    cache: PipelineCache,
    args: StrategyConfig
): Promise<SummarizedDescriptionResult> {
    // Python: llm_config = LanguageModelConfig(**args["llm"])
    const llm_config = new LanguageModelConfig(args.llm);

    // Python: llm = ModelManager().get_or_create_chat_model(
    //     name="summarize_descriptions",
    //     model_type=llm_config.type,
    //     config=llm_config,
    //     cache=cache,
    // )
    const llm = ModelManager.getInstance().getOrCreateChatModel(
        "summarize_descriptions",
        llm_config.type,
        llm_config,
        cache
    );

    // Python: return await run_summarize_descriptions(llm, id, descriptions, args)
    return await run_summarize_descriptions(llm, id, descriptions, args);
}

/**
 * Run the entity extraction chain.
 * Matches the Python run_summarize_descriptions function exactly.
 */
async function run_summarize_descriptions(
    model: ChatModel,
    id: string | [string, string],
    descriptions: string[],
    args: StrategyConfig
): Promise<SummarizedDescriptionResult> {
    // Python: # Extraction Arguments
    // Python: summarize_prompt = args.get("summarize_prompt", None)
    const summarize_prompt = args.summarize_prompt || undefined;

    // Python: max_input_tokens = args["max_input_tokens"]
    const max_input_tokens = args.max_input_tokens;

    // Python: max_summary_length = args["max_summary_length"]
    const max_summary_length = args.max_summary_length;

    // Python: extractor = SummarizeExtractor(
    //     model_invoker=model,
    //     summarization_prompt=summarize_prompt,
    //     on_error=lambda e, stack, details: logger.error(
    //         "Entity Extraction Error",
    //         exc_info=e,
    //         extra={"stack": stack, "details": details},
    //     ),
    //     max_summary_length=max_summary_length,
    //     max_input_tokens=max_input_tokens,
    // )
    const extractor = new SummarizeExtractor(
        model,
        max_summary_length,
        max_input_tokens,
        summarize_prompt,
        (e, stack, details) => {
            logger.error(
                "Entity Extraction Error",
                e,
                { stack, details }
            );
        }
    );

    // Python: result = await extractor(id=id, descriptions=descriptions)
    const result = await extractor.call(id, descriptions);

    // Python: return SummarizedDescriptionResult(id=result.id, description=result.description)
    return {
        id: result.id,
        description: result.description
    };
}

// Compatibility export for existing code
export const runGraphIntelligence = run_graph_intelligence;