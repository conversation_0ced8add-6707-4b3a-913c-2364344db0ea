/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the build_mixed_context method definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import * as schemas from '../../../data_model/schemas.js';
import { sort_context } from './graph_context/sort_context.js';
import { numTokens } from '../../../query/llm/text_utils.js';

/**
 * Build parent context by concatenating all sub-communities' contexts.
 * Matches the Python build_mixed_context function exactly.
 * 
 * If the context exceeds the limit, we use sub-community reports instead.
 */
export function build_mixed_context(context: Record<string, any>[], max_context_tokens: number): string {
    // Python: sorted_context = sorted(context, key=lambda x: x[schemas.CONTEXT_SIZE], reverse=True)
    const sorted_context = [...context].sort((a, b) => {
        const size_a = a[schemas.CONTEXT_SIZE] || 0;
        const size_b = b[schemas.CONTEXT_SIZE] || 0;
        return size_b - size_a;
    });

    // Python: substitute_reports = []
    // Python: final_local_contexts = []
    // Python: exceeded_limit = True
    // Python: context_string = ""
    const substitute_reports: Record<string, any>[] = [];
    const final_local_contexts: Record<string, any>[] = [];
    let exceeded_limit = true;
    let context_string = "";

    // Python: for idx, sub_community_context in enumerate(sorted_context):
    for (let idx = 0; idx < sorted_context.length; idx++) {
        const sub_community_context = sorted_context[idx];
        
        if (exceeded_limit) {
            // Python: if sub_community_context[schemas.FULL_CONTENT]:
            if (sub_community_context[schemas.FULL_CONTENT]) {
                substitute_reports.push({
                    [schemas.COMMUNITY_ID]: sub_community_context[schemas.SUB_COMMUNITY],
                    [schemas.FULL_CONTENT]: sub_community_context[schemas.FULL_CONTENT],
                });
            } else {
                // Python: # this sub-community has no report, so we will use its local context
                // Python: final_local_contexts.extend(sub_community_context[schemas.ALL_CONTEXT])
                // Python: continue
                final_local_contexts.push(...sub_community_context[schemas.ALL_CONTEXT]);
                continue;
            }

            // Python: # add local context for the remaining sub-communities
            // Python: remaining_local_context = []
            // Python: for rid in range(idx + 1, len(sorted_context)):
            //     remaining_local_context.extend(sorted_context[rid][schemas.ALL_CONTEXT])
            const remaining_local_context: Record<string, any>[] = [];
            for (let rid = idx + 1; rid < sorted_context.length; rid++) {
                remaining_local_context.push(...sorted_context[rid][schemas.ALL_CONTEXT]);
            }
            
            // Python: new_context_string = sort_context(
            //     local_context=remaining_local_context + final_local_contexts,
            //     sub_community_reports=substitute_reports,
            // )
            const new_context_string = sort_context(
                remaining_local_context.concat(final_local_contexts),
                substitute_reports
            );
            
            // Python: if num_tokens(new_context_string) <= max_context_tokens:
            if (numTokens(new_context_string) <= max_context_tokens) {
                exceeded_limit = false;
                context_string = new_context_string;
                break;
            }
        }
    }

    // Python: if exceeded_limit:
    if (exceeded_limit) {
        // Python: # if all sub-community reports exceed the limit, we add reports until context is full
        // Python: substitute_reports = []
        const final_substitute_reports: Record<string, any>[] = [];
        
        // Python: for sub_community_context in sorted_context:
        for (const sub_community_context of sorted_context) {
            final_substitute_reports.push({
                [schemas.COMMUNITY_ID]: sub_community_context[schemas.SUB_COMMUNITY],
                [schemas.FULL_CONTENT]: sub_community_context[schemas.FULL_CONTENT],
            });
            
            // Python: new_context_string = pd.DataFrame(substitute_reports).to_csv(index=False, sep=",")
            const new_context_string = dataFrameToCsv(final_substitute_reports);
            
            // Python: if num_tokens(new_context_string) > max_context_tokens:
            if (numTokens(new_context_string) > max_context_tokens) {
                break;
            }

            context_string = new_context_string;
        }
    }
    
    // Python: return context_string
    return context_string;
}

/**
 * Helper function to convert array of objects to CSV format.
 * Matches pandas DataFrame.to_csv behavior.
 */
function dataFrameToCsv(data: Record<string, any>[]): string {
    if (data.length === 0) {
        return '';
    }
    
    const headers = Object.keys(data[0]);
    const headerRow = headers.join(',');
    
    const dataRows = data.map(row => {
        return headers.map(header => {
            const value = row[header];
            if (value === null || value === undefined) {
                return '';
            }
            // Escape commas and quotes in CSV
            const stringValue = String(value);
            if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
                return `"${stringValue.replace(/"/g, '""')}"`;
            }
            return stringValue;
        }).join(',');
    });
    
    return [headerRow, ...dataRows].join('\n');
}

// Compatibility export for existing code
export const buildMixedContext = build_mixed_context;
