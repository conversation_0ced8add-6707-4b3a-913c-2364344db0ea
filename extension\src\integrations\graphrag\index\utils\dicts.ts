/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A utility module containing methods for inspecting and verifying dictionary types.
 */

/**
 * Type constructor function
 */
type TypeConstructor<T = any> = (value: any) => T;

/**
 * Return True if the given dictionary has the given keys with the given types.
 * @param data - The dictionary to check
 * @param expectedFields - Array of [key, type] tuples
 * @param inplace - Whether to modify the data in place with cast values
 * @returns True if all fields exist with correct types
 */
export function dictHasKeysWithTypes(
    data: Record<string, any>,
    expectedFields: Array<[string, TypeConstructor]>,
    inplace: boolean = false
): boolean {
    for (const [field, fieldType] of expectedFields) {
        if (!(field in data)) {
            return false;
        }

        const value = data[field];
        try {
            const castValue = fieldType(value);
            if (inplace) {
                data[field] = castValue;
            }
        } catch (error) {
            return false;
        }
    }
    return true;
}