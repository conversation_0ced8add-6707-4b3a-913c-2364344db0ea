// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the prune graph configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';

/**
 * Configuration section for pruning graphs.
 */
export interface PruneGraphConfig {
  /**
   * The minimum node frequency to allow.
   */
  minNodeFreq: number;

  /**
   * The maximum standard deviation of node frequency to allow.
   */
  maxNodeFreqStd?: number;

  /**
   * The minimum node degree to allow.
   */
  minNodeDegree: number;

  /**
   * The maximum standard deviation of node degree to allow.
   */
  maxNodeDegreeStd?: number;

  /**
   * The minimum edge weight percentile to allow. Use e.g, `40` for 40%.
   */
  minEdgeWeightPct: number;

  /**
   * Remove ego nodes.
   */
  removeEgoNodes: boolean;

  /**
   * Only use largest connected component.
   */
  lccOnly: boolean;
}

/**
 * Create a PruneGraphConfig with default values.
 */
export function createPruneGraphConfig(config: Partial<PruneGraphConfig> = {}): PruneGraphConfig {
  return {
    minNodeFreq: config.minNodeFreq ?? graphragConfigDefaults.pruneGraph.minNodeFreq,
    maxNodeFreqStd: config.maxNodeFreqStd ?? graphragConfigDefaults.pruneGraph.maxNodeFreqStd,
    minNodeDegree: config.minNodeDegree ?? graphragConfigDefaults.pruneGraph.minNodeDegree,
    maxNodeDegreeStd: config.maxNodeDegreeStd ?? graphragConfigDefaults.pruneGraph.maxNodeDegreeStd,
    minEdgeWeightPct: config.minEdgeWeightPct ?? graphragConfigDefaults.pruneGraph.minEdgeWeightPct,
    removeEgoNodes: config.removeEgoNodes ?? graphragConfigDefaults.pruneGraph.removeEgoNodes,
    lccOnly: config.lccOnly ?? graphragConfigDefaults.pruneGraph.lccOnly,
  };
}
