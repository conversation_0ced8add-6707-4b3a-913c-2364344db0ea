/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 *
 * Common types for the GraphRAG knowledge model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * DataFrame interface for TypeScript - represents tabular data similar to pandas DataFrame.
 */
export interface DataFrame {
    /** Column names in the DataFrame */
    columns: string[];
    /** Array of row data, where each row is a record with column names as keys */
    data: Record<string, any>[];
}

/**
 * Type for text embedding function.
 */
export type TextEmbedder = (text: string) => Promise<number[]>

/**
 * Synchronous version of text embedder.
 */
export type TextEmbedderSync = (text: string) => number[]