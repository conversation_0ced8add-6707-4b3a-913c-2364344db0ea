// Copyright (c) 2025 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Base llm response protocol.
 */

/**
 * Protocol for Model response's output object.
 */
export interface ModelOutput {
    /** Return the textual content of the output. */
    readonly content: string;
    /** Return the complete JSON response returned by the model. */
    readonly fullResponse: Record<string, any> | null;
}

/**
 * Protocol for LLM response.
 */
export interface ModelResponse<T = any> {
    /** Return the output of the response. */
    readonly output: ModelOutput;
    /** Return the parsed response. */
    readonly parsedResponse: T | null;
    /** Return the history of the response. */
    readonly history: any[];
}

/**
 * Base class for LLM output.
 */
export class BaseModelOutput implements ModelOutput {
    /** The textual content of the output. */
    public readonly content: string;
    /** The complete JSON response returned by the LLM provider. */
    public readonly fullResponse: Record<string, any> | null;

    constructor(content: string, fullResponse?: Record<string, any> | null) {
        this.content = content;
        this.fullResponse = fullResponse || null;
    }
}

/**
 * Base class for a Model response.
 */
export class BaseModelResponse<T = any> implements ModelResponse<T> {
    /** The output of the response. */
    public readonly output: BaseModelOutput;
    /** Parsed response. */
    public readonly parsedResponse: T | null;
    /** History of the response. */
    public readonly history: any[];
    /** Tool calls required by the Model. These will be instances of the LLM tools (with filled parameters). */
    public readonly toolCalls: any[];
    /** Request/response metrics. */
    public readonly metrics: any | null;
    /** Whether the response was a cache hit. */
    public readonly cacheHit: boolean | null;

    constructor(options: {
        output: BaseModelOutput;
        parsedResponse?: T | null;
        history?: any[];
        toolCalls?: any[];
        metrics?: any | null;
        cacheHit?: boolean | null;
    }) {
        this.output = options.output;
        this.parsedResponse = options.parsedResponse || null;
        this.history = options.history || [];
        this.toolCalls = options.toolCalls || [];
        this.metrics = options.metrics || null;
        this.cacheHit = options.cacheHit || null;
    }
}