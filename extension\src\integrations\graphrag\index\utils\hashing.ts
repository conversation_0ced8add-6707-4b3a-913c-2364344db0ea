/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Hashing utilities.
 */

import { createHash } from 'crypto';

/**
 * Generate a SHA512 hash.
 * @param item - The object to hash
 * @param hashcode - The keys to include in the hash
 * @returns SHA512 hash string
 */
export function genSha512Hash(item: Record<string, any>, hashcode: Iterable<string>): string {
    const hashed = Array.from(hashcode).map(column => String(item[column])).join('');
    return createHash('sha512').update(hashed, 'utf8').digest('hex');
}