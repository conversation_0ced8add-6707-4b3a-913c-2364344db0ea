/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A package containing the 'Document' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Named } from './named'

/**
 * An interface for a document in the system.
 */
export interface Document extends Named {
    /** Type of the document. */
    type: string

    /** list of text units in the document. */
    text_unit_ids: string[]

    /** The raw text content of the document. */
    text: string

    /** A dictionary of structured attributes such as author, etc (optional). */
    attributes?: Record<string, any>
}

/**
 * Create a new document with default values.
 */
export function createDocument(
    id: string,
    title: string,
    options: {
        short_id?: string
        type?: string
        text?: string
        text_unit_ids?: string[]
        attributes?: Record<string, any>
    } = {}
): Document {
    return {
        id,
        title,
        short_id: options.short_id,
        type: options.type ?? "text",
        text_unit_ids: options.text_unit_ids ?? [],
        text: options.text ?? "",
        attributes: options.attributes
    }
}

/**
 * Create a new document from the dict data.
 */
export function createDocumentFromDict(
    d: Record<string, any>,
    options: {
        id_key?: string
        short_id_key?: string
        title_key?: string
        type_key?: string
        text_key?: string
        text_units_key?: string
        attributes_key?: string
    } = {}
): Document {
    const {
        id_key = "id",
        short_id_key = "human_readable_id",
        title_key = "title",
        type_key = "type",
        text_key = "text",
        text_units_key = "text_units",
        attributes_key = "attributes"
    } = options

    return {
        id: d[id_key],
        short_id: d[short_id_key],
        title: d[title_key],
        type: d[type_key] ?? "text",
        text: d[text_key],
        text_unit_ids: d[text_units_key] ?? [],
        attributes: d[attributes_key]
    }
}