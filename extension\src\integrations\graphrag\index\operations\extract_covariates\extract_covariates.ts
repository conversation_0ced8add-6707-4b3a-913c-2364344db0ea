/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the extract_covariates verb definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../data_model/types.js';
import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../callbacks/workflow_callbacks.js';
import { AsyncType } from '../../../config/enums.js';
import { LanguageModelConfig } from '../../../config/models/language_model_config.js';
import { ModelManager } from '../../../language_model/manager.js';
import { deriveFromRows } from '../../utils/derive_from_rows.js';
import { ClaimExtractor } from './claim_extractor.js';
import { Covariate, CovariateExtractionResult } from './typing.js';

const logger = console;

const DEFAULT_ENTITY_TYPES = ["organization", "person", "geo", "event"];

/**
 * Extract claims from a piece of text.
 */
export async function extractCovariates(
    input: DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    column: string,
    covariateType: string,
    strategy?: Record<string, any>,
    asyncMode: AsyncType = AsyncType.ASYNCIO,
    entityTypes?: string[],
    numThreads: number = 4
): Promise<DataFrame> {
    logger.debug("extract_covariates strategy=", strategy);

    const entityTypesToUse = entityTypes || DEFAULT_ENTITY_TYPES;
    const resolvedEntitiesMap: Record<string, string> = {};
    const strategyConfig = { ...strategy };

    async function runStrategy(row: Record<string, any>): Promise<any[]> {
        const text = row[column];
        const result = await runExtractClaims(
            text,
            entityTypesToUse,
            resolvedEntitiesMap,
            callbacks,
            cache,
            strategyConfig
        );

        return result.covariate_data.map(item =>
            createRowFromClaimData(row, item, covariateType)
        );
    }

    const results = await deriveFromRows(
        input,
        runStrategy,
        callbacks,
        numThreads,
        asyncMode,
        "extract covariates progress: "
    );

    // Flatten results
    const flatResults = results.flat().filter(item => item != null);

    // Create columns from first result or default
    const columns = flatResults.length > 0
        ? Object.keys(flatResults[0])
        : ['id', 'covariate_type', 'description'];

    return {
        columns: columns,
        data: flatResults
    };
}

/**
 * Create a row from the claim data and the input row.
 */
function createRowFromClaimData(
    row: Record<string, any>,
    covariateData: Covariate,
    covariateType: string
): Record<string, any> {
    return {
        ...row,
        ...covariateData,
        covariate_type: covariateType
    };
}

/**
 * Run the Claim extraction chain.
 */
async function runExtractClaims(
    input: string | Iterable<string>,
    entityTypes: string[],
    resolvedEntitiesMap: Record<string, string>,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    strategyConfig: Record<string, any>
): Promise<CovariateExtractionResult> {
    const llmConfig = strategyConfig.llm as LanguageModelConfig;
    const modelManager = ModelManager.getInstance();
    const llm = modelManager.getOrCreateChatModel(
        "extract_claims",
        llmConfig.type,
        llmConfig
    );

    const extractionPrompt = strategyConfig.extraction_prompt;
    const maxGleanings = strategyConfig.max_gleanings || 1;
    const tupleDelimiter = strategyConfig.tuple_delimiter;
    const recordDelimiter = strategyConfig.record_delimiter;
    const completionDelimiter = strategyConfig.completion_delimiter;

    const extractor = new ClaimExtractor(
        llm,
        extractionPrompt,
        undefined, // input_text_key
        undefined, // input_entity_spec_key
        undefined, // input_claim_description_key
        undefined, // input_resolved_entities_key
        undefined, // tuple_delimiter_key
        undefined, // record_delimiter_key
        undefined, // completion_delimiter_key
        maxGleanings,
        (e, s, d) => {
            logger.error("Claim Extraction Error", e, { stack: s, details: d });
        }
    );

    const claimDescription = strategyConfig.claim_description;
    if (!claimDescription) {
        throw new Error("claim_description is required for claim extraction");
    }

    const inputArray = typeof input === 'string' ? [input] : Array.from(input);

    const results = await extractor.call({
        input_text: inputArray,
        entity_specs: entityTypes,
        resolved_entities: resolvedEntitiesMap,
        claim_description: claimDescription
    }, {
        tuple_delimiter: tupleDelimiter,
        record_delimiter: recordDelimiter,
        completion_delimiter: completionDelimiter
    });

    const claimData = results.output;
    return {
        covariate_data: claimData.map(createCovariate)
    };
}

/**
 * Create a covariate from the item.
 */
function createCovariate(item: Record<string, any>): Covariate {
    return {
        subject_id: item.subject_id,
        object_id: item.object_id,
        type: item.type,
        status: item.status,
        start_date: item.start_date,
        end_date: item.end_date,
        description: item.description,
        source_text: item.source_text,
        record_id: item.record_id,
        id: item.id
    };
}