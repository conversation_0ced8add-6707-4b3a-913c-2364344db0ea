// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Language model configuration.
 */

import * as tiktoken from 'tiktoken';
import { languageModelDefaults } from '../defaults';
import { AsyncType, AuthType, ModelType } from '../enums';
import {
    ApiKeyMissingError,
    AzureApiBaseMissingError,
    AzureApiVersionMissingError,
    AzureDeploymentNameMissingError,
    ConflictingSettingsError,
} from '../errors';
import { ModelFactory } from '../../language_model/factory';

/**
 * Language model configuration.
 */
export class LanguageModelConfig {
    /**
     * The API key to use for the LLM service.
     */
    api_key?: string | null = languageModelDefaults.api_key;

    /**
     * Validate the API key.
     *
     * API Key is required when using OpenAI API
     * or when using Azure API with API Key authentication.
     * For the time being, this check is extra verbose for clarity.
     * It will also raise an exception if an API Key is provided
     * when one is not expected such as the case of using Azure
     * Managed Identity.
     */
    private _validate_api_key(): void {
        if (this.auth_type === AuthType.APIKey && (
            this.api_key === null || this.api_key === undefined || this.api_key.trim() === ""
        )) {
            throw new ApiKeyMissingError(
                this.type,
                this.auth_type
            );
        }

        if ((this.auth_type === AuthType.AzureManagedIdentity) && (
            this.api_key !== null && this.api_key !== undefined && this.api_key.trim() !== ""
        )) {
            const msg = "API Key should not be provided when using Azure Managed Identity. Please rerun `graphrag init` and remove the api_key when using Azure Managed Identity.";
            throw new ConflictingSettingsError(msg);
        }
    }

    /**
     * The authentication type.
     */
    auth_type: AuthType = languageModelDefaults.auth_type;

    /**
     * Validate the authentication type.
     *
     * auth_type must be api_key when using OpenAI and
     * can be either api_key or azure_managed_identity when using AOI.
     */
    private _validate_auth_type(): void {
        if (this.auth_type === AuthType.AzureManagedIdentity && (
            this.type === ModelType.OpenAIChat || this.type === ModelType.OpenAIEmbedding
        )) {
            const msg = `auth_type of azure_managed_identity is not supported for model type ${this.type}. Please rerun \`graphrag init\` and set the auth_type to api_key.`;
            throw new ConflictingSettingsError(msg);
        }
    }

    /**
     * The type of LLM model to use.
     */
    type!: ModelType | string;

    /**
     * Validate the model type.
     */
    private _validate_type(): void {
        // Type should be contained by the registered models
        if (!ModelFactory.is_supported_model(this.type)) {
            const msg = `Model type ${this.type} is not recognized, must be one of ${[...ModelFactory.get_chat_models(), ...ModelFactory.get_embedding_models()]}.`;
            throw new Error(msg);
        }
    }

    /**
     * The LLM model to use.
     */
    model!: string;

    /**
     * The encoding model to use.
     */
    encoding_model: string = languageModelDefaults.encoding_model;

    /**
     * Validate the encoding model.
     */
    private _validate_encoding_model(): void {
        if (this.encoding_model.trim() === "") {
            this.encoding_model = tiktoken.encodingNameForModel(this.model as any);
        }
    }

    /**
     * The base URL for the LLM API.
     */
    api_base?: string | null = languageModelDefaults.api_base;

    /**
     * Validate the API base.
     *
     * Required when using AOI.
     */
    private _validate_api_base(): void {
        if ((
            this.type === ModelType.AzureOpenAIChat ||
            this.type === ModelType.AzureOpenAIEmbedding
        ) && (this.api_base === null || this.api_base === undefined || this.api_base.trim() === "")) {
            throw new AzureApiBaseMissingError(this.type);
        }
    }

    /**
     * The version of the LLM API to use.
     */
    api_version?: string | null = languageModelDefaults.api_version;

    /**
     * Validate the API version.
     *
     * Required when using AOI.
     */
    private _validate_api_version(): void {
        if ((
            this.type === ModelType.AzureOpenAIChat ||
            this.type === ModelType.AzureOpenAIEmbedding
        ) && (this.api_version === null || this.api_version === undefined || this.api_version.trim() === "")) {
            throw new AzureApiVersionMissingError(this.type);
        }
    }

    /**
     * The deployment name to use for the LLM service.
     */
    deployment_name?: string | null = languageModelDefaults.deployment_name;

    /**
     * Validate the deployment name.
     *
     * Required when using AOI.
     */
    private _validate_deployment_name(): void {
        if ((
            this.type === ModelType.AzureOpenAIChat ||
            this.type === ModelType.AzureOpenAIEmbedding
        ) && (this.deployment_name === null || this.deployment_name === undefined || this.deployment_name.trim() === "")) {
            throw new AzureDeploymentNameMissingError(this.type);
        }
    }

    /**
     * The organization to use for the LLM service.
     */
    organization?: string | null = languageModelDefaults.organization;

    /**
     * The proxy to use for the LLM service.
     */
    proxy?: string | null = languageModelDefaults.proxy;

    /**
     * Azure resource URI to use with managed identity for the llm connection.
     */
    audience?: string | null = languageModelDefaults.audience;

    /**
     * Whether the model supports JSON output mode.
     */
    model_supports_json?: boolean | null = languageModelDefaults.model_supports_json;

    /**
     * The request timeout to use.
     */
    request_timeout: number = languageModelDefaults.request_timeout;

    /**
     * The number of tokens per minute to use for the LLM service.
     */
    tokens_per_minute: number | "auto" | null = languageModelDefaults.tokens_per_minute;

    /**
     * Validate the tokens per minute.
     */
    private _validate_tokens_per_minute(): void {
        // If the value is a number, check if it is less than 1
        if (typeof this.tokens_per_minute === 'number' && this.tokens_per_minute < 1) {
            const msg = `Tokens per minute must be a non zero positive number, 'auto' or null. Suggested value: ${languageModelDefaults.tokens_per_minute}.`;
            throw new Error(msg);
        }
    }

    /**
     * The number of requests per minute to use for the LLM service.
     */
    requests_per_minute: number | "auto" | null = languageModelDefaults.requests_per_minute;

    /**
     * Validate the requests per minute.
     */
    private _validate_requests_per_minute(): void {
        // If the value is a number, check if it is less than 1
        if (typeof this.requests_per_minute === 'number' && this.requests_per_minute < 1) {
            const msg = `Requests per minute must be a non zero positive number, 'auto' or null. Suggested value: ${languageModelDefaults.requests_per_minute}.`;
            throw new Error(msg);
        }
    }

    /**
     * The retry strategy to use for the LLM service.
     */
    retry_strategy: string = languageModelDefaults.retry_strategy;

    /**
     * The maximum number of retries to use for the LLM service.
     */
    max_retries: number = languageModelDefaults.max_retries;

    /**
     * Validate the maximum retries.
     */
    private _validate_max_retries(): void {
        if (this.max_retries < 1) {
            const msg = `Maximum retries must be greater than or equal to 1. Suggested value: ${languageModelDefaults.max_retries}.`;
            throw new Error(msg);
        }
    }

    /**
     * The maximum retry wait to use for the LLM service.
     */
    max_retry_wait: number = languageModelDefaults.max_retry_wait;

    /**
     * Whether to use concurrent requests for the LLM service.
     */
    concurrent_requests: number = languageModelDefaults.concurrent_requests;

    /**
     * The async mode to use.
     */
    async_mode: AsyncType = languageModelDefaults.async_mode;

    /**
     * Static responses to use in mock mode.
     */
    responses?: (string | any)[] | null = languageModelDefaults.responses;

    /**
     * The maximum number of tokens to generate.
     */
    max_tokens?: number | null = languageModelDefaults.max_tokens;

    /**
     * The temperature to use for token generation.
     */
    temperature: number = languageModelDefaults.temperature;

    /**
     * The maximum number of tokens to consume. This includes reasoning tokens for the o* reasoning models.
     */
    max_completion_tokens?: number | null = languageModelDefaults.max_completion_tokens;

    /**
     * Level of effort OpenAI reasoning models should expend. Supported options are 'low', 'medium', 'high'; and OAI defaults to 'medium'.
     */
    reasoning_effort?: string | null = languageModelDefaults.reasoning_effort;

    /**
     * The top-p value to use for token generation.
     */
    top_p: number = languageModelDefaults.top_p;

    /**
     * The number of completions to generate.
     */
    n: number = languageModelDefaults.n;

    /**
     * The frequency penalty to use for token generation.
     */
    frequency_penalty: number = languageModelDefaults.frequency_penalty;

    /**
     * The presence penalty to use for token generation.
     */
    presence_penalty: number = languageModelDefaults.presence_penalty;

    /**
     * Validate the Azure settings.
     */
    private _validate_azure_settings(): void {
        this._validate_api_base();
        this._validate_api_version();
        this._validate_deployment_name();
    }

    /**
     * Validate the model configuration.
     */
    private _validate_model(): void {
        this._validate_type();
        this._validate_auth_type();
        this._validate_api_key();
        this._validate_tokens_per_minute();
        this._validate_requests_per_minute();
        this._validate_max_retries();
        this._validate_azure_settings();
        this._validate_encoding_model();
    }
}
