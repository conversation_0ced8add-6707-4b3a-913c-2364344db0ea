/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing embed_text, load_strategy and create_row_from_embedding_data methods definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../data_model/types.js';
import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../callbacks/workflow_callbacks.js';
import { createCollectionName } from '../../../config/embeddings.js';
import { BaseVectorStore, VectorStoreDocument } from '../../../vector_stores/base.js';
import { VectorStoreFactory } from '../../../vector_stores/factory.js';
import { TextEmbeddingStrategy } from './strategies/types.js';
import { mockRun, openaiRun } from './strategies/index.js';

const logger = console;

// Per Azure OpenAI Limits
const DEFAULT_EMBEDDING_BATCH_SIZE = 500;

/**
 * TextEmbedStrategyType enum definition.
 */
export enum TextEmbedStrategyType {
    openai = "openai",
    mock = "mock"
}

// TextEmbeddingStrategy is imported from strategies/types.js

/**
 * Embed a piece of text into a vector space.
 * @param input - Input DataFrame
 * @param callbacks - Workflow callbacks
 * @param cache - Pipeline cache
 * @param embedColumn - Column containing text to embed
 * @param strategy - Strategy configuration
 * @param embeddingName - Name of the embedding
 * @param idColumn - ID column name
 * @param titleColumn - Title column name
 * @returns Promise resolving to embeddings
 */
export async function embedText(
    input: DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    embedColumn: string,
    strategy: Record<string, any>,
    embeddingName: string,
    idColumn: string = "id",
    titleColumn?: string
): Promise<number[][]> {
    const vectorStoreConfig = strategy.vector_store;

    if (vectorStoreConfig) {
        const collectionName = getCollectionName(vectorStoreConfig, embeddingName);
        const vectorStore = createVectorStore(vectorStoreConfig, collectionName);
        const vectorStoreWorkflowConfig = vectorStoreConfig[embeddingName] || vectorStoreConfig;
        
        return await textEmbedWithVectorStore(
            input,
            callbacks,
            cache,
            embedColumn,
            strategy,
            vectorStore,
            vectorStoreWorkflowConfig,
            idColumn,
            titleColumn
        );
    }

    return await textEmbedInMemory(
        input,
        callbacks,
        cache,
        embedColumn,
        strategy
    );
}

/**
 * Embed text in memory without vector store.
 */
async function textEmbedInMemory(
    input: DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    embedColumn: string,
    strategy: Record<string, any>
): Promise<number[][]> {
    const strategyType = strategy.type;
    const strategyExec = loadStrategy(strategyType);
    const strategyConfig = { ...strategy };

    const texts = input.data.map(row => String(row[embedColumn]));
    const result = await strategyExec(texts, callbacks, cache, strategyConfig);

    return (result.embeddings || []).filter((emb): emb is number[] => emb !== null);
}

/**
 * Embed text with vector store.
 */
async function textEmbedWithVectorStore(
    input: DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    embedColumn: string,
    strategy: Record<string, any>,
    vectorStore: BaseVectorStore,
    vectorStoreConfig: Record<string, any>,
    idColumn: string = "id",
    titleColumn?: string
): Promise<number[][]> {
    const strategyType = strategy.type;
    const strategyExec = loadStrategy(strategyType);
    const strategyConfig = { ...strategy };

    // Get vector-storage configuration
    const insertBatchSize = vectorStoreConfig.batch_size || DEFAULT_EMBEDDING_BATCH_SIZE;
    const overwrite = vectorStoreConfig.overwrite !== false;

    // Validate columns
    if (!input.columns.includes(embedColumn)) {
        throw new Error(`Column ${embedColumn} not found in input dataframe with columns ${input.columns}`);
    }
    
    const title = titleColumn || embedColumn;
    if (!input.columns.includes(title)) {
        throw new Error(`Column ${title} not found in input dataframe with columns ${input.columns}`);
    }
    
    if (!input.columns.includes(idColumn)) {
        throw new Error(`Column ${idColumn} not found in input dataframe with columns ${input.columns}`);
    }

    let totalRows = 0;
    for (const row of input.data) {
        const value = row[embedColumn];
        if (Array.isArray(value)) {
            totalRows += value.length;
        } else {
            totalRows += 1;
        }
    }

    let i = 0;
    let startingIndex = 0;
    const allResults: number[][] = [];

    const numTotalBatches = Math.ceil(input.data.length / insertBatchSize);
    
    while (insertBatchSize * i < input.data.length) {
        logger.info(
            `uploading text embeddings batch ${i + 1}/${numTotalBatches} of size ${insertBatchSize} to vector store`
        );
        
        const batch = input.data.slice(insertBatchSize * i, insertBatchSize * (i + 1));
        const texts = batch.map(row => String(row[embedColumn]));
        const titles = batch.map(row => String(row[title]));
        const ids = batch.map(row => String(row[idColumn]));
        
        const result = await strategyExec(texts, callbacks, cache, strategyConfig);
        
        if (result.embeddings) {
            const embeddings = result.embeddings.filter(embedding => embedding !== null);
            allResults.push(...embeddings);
        }

        const vectors = result.embeddings || [];
        const documents: VectorStoreDocument[] = [];
        
        for (let j = 0; j < ids.length; j++) {
            const docId = ids[j];
            const docText = texts[j];
            const docTitle = titles[j];
            const docVector = vectors[j];
            
            if (docVector) {
                const document: VectorStoreDocument = {
                    id: docId,
                    text: docText,
                    vector: Array.isArray(docVector) ? docVector : Array.from(docVector),
                    attributes: { title: docTitle },
                };
                documents.push(document);
            }
        }

        await vectorStore.loadDocuments(documents, overwrite && i === 0);
        startingIndex += documents.length;
        i++;
    }

    return allResults;
}

/**
 * Create vector store instance.
 */
function createVectorStore(
    vectorStoreConfig: Record<string, any>,
    collectionName: string
): BaseVectorStore {
    const vectorStoreType = String(vectorStoreConfig.type);
    
    if (collectionName) {
        vectorStoreConfig.collection_name = collectionName;
    }

    const vectorStore = VectorStoreFactory.createVectorStore(vectorStoreType, vectorStoreConfig);
    
    vectorStore.connect(vectorStoreConfig);
    return vectorStore;
}

/**
 * Get collection name for vector store.
 */
function getCollectionName(vectorStoreConfig: Record<string, any>, embeddingName: string): string {
    const containerName = vectorStoreConfig.container_name || "default";
    const collectionName = createCollectionName(containerName, embeddingName);

    logger.info(
        `using vector store ${vectorStoreConfig.type} with container_name ${containerName} for embedding ${embeddingName}: ${collectionName}`
    );
    
    return collectionName;
}

/**
 * Load strategy method definition.
 * Matches the Python implementation exactly.
 */
export function loadStrategy(strategy: TextEmbedStrategyType): TextEmbeddingStrategy {
    switch (strategy) {
        case TextEmbedStrategyType.openai:
            // Python: from graphrag.index.operations.embed_text.strategies.openai import run as run_openai
            return openaiRun;
        case TextEmbedStrategyType.mock:
            // Python: from graphrag.index.operations.embed_text.strategies.mock import run as run_mock
            return mockRun;
        default:
            const msg = `Unknown strategy: ${strategy}`;
            throw new Error(msg);
    }
}