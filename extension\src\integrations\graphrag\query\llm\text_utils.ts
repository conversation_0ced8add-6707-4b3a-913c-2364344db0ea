// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Text Utilities for LLM.
 */

import { get_encoding } from 'tiktoken';
import { ENCODING_MODEL } from '../../config/defaults';

/**
 * Return the number of tokens in the given text.
 */
export function numTokens(text: string, tokenEncoder?: any): number {
  if (!tokenEncoder) {
    tokenEncoder = get_encoding(ENCODING_MODEL);
  }
  return tokenEncoder.encode(text).length;
}

/**
 * Batch data into tuples of length n. The last batch may be shorter.
 * 
 * Taken from <PERSON>'s cookbook: https://docs.python.org/3/library/itertools.html#itertools.batched
 */
export function* batched<T>(iterable: Iterable<T>, n: number): Generator<T[]> {
  // batched('ABCDEFG', 3) --> ABC DEF G
  if (n < 1) {
    throw new Error('n must be at least one');
  }
  
  const iterator = iterable[Symbol.iterator]();
  let batch: T[] = [];
  
  for (const item of iterator) {
    batch.push(item);
    if (batch.length === n) {
      yield batch;
      batch = [];
    }
  }
  
  if (batch.length > 0) {
    yield batch;
  }
}

/**
 * Chunk text by token length.
 */
export function* chunkText(
  text: string,
  maxTokens: number,
  tokenEncoder?: any
): Generator<string> {
  if (!tokenEncoder) {
    tokenEncoder = get_encoding(ENCODING_MODEL);
  }
  
  const tokens = tokenEncoder.encode(text);
  const chunkIterator = batched(tokens, maxTokens);
  
  for (const chunk of chunkIterator) {
    yield tokenEncoder.decode(chunk);
  }
}

/**
 * JSON cleaning and formatting utilities.
 */
export function tryParseJsonObject(input: string, verbose: boolean = true): [string, Record<string, any>] {
  // Sometimes, the LLM returns a json string with some extra description, this function will clean it up.
  
  let result: any = null;
  try {
    // Try parse first
    result = JSON.parse(input);
  } catch (error) {
    if (verbose) {
      console.warn('Error decoding faulty json, attempting repair');
    }
  }

  if (result) {
    return [input, result];
  }

  const pattern = /\{(.*)\}/s;
  const match = input.match(pattern);
  input = match ? `{${match[1]}}` : input;

  // Clean up json string.
  input = input
    .replace(/\{\{/g, '{')
    .replace(/\}\}/g, '}')
    .replace(/"\[\{/g, '[{')
    .replace(/\}\]"/g, '}]')
    .replace(/\\/g, ' ')
    .replace(/\\n/g, ' ')
    .replace(/\n/g, ' ')
    .replace(/\r/g, '')
    .trim();

  // Remove JSON Markdown Frame
  if (input.startsWith('```json')) {
    input = input.slice('```json'.length);
  }
  if (input.endsWith('```')) {
    input = input.slice(0, -'```'.length);
  }

  try {
    result = JSON.parse(input);
  } catch (error) {
    // Fixup potentially malformed json string using basic repair.
    input = repairJson(input);

    // Generate JSON-string output using best-attempt prompting & parsing techniques.
    try {
      result = JSON.parse(input);
    } catch (error) {
      if (verbose) {
        console.error('error loading json, json=', input, error);
      }
      return [input, {}];
    }
    
    if (typeof result !== 'object' || result === null || Array.isArray(result)) {
      if (verbose) {
        console.error('not expected dict type. type=', typeof result);
      }
      return [input, {}];
    }
    return [input, result];
  }
  
  return [input, result];
}

/**
 * Basic JSON repair function (simplified version of json_repair).
 */
function repairJson(jsonStr: string): string {
  // Basic JSON repair - add missing quotes, fix common issues
  let repaired = jsonStr;
  
  // Fix unquoted keys
  repaired = repaired.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');
  
  // Fix trailing commas
  repaired = repaired.replace(/,(\s*[}\]])/g, '$1');
  
  // Fix single quotes to double quotes
  repaired = repaired.replace(/'/g, '"');
  
  return repaired;
}