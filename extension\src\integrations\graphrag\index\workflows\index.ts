/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A package containing all built-in workflow definitions.
 */

import { PipelineFactory } from './factory';

// Import all workflow functions
import { runWorkflow as runCreateBaseTextUnits } from './create-base-text-units';
import { runWorkflow as runCreateCommunities } from './create-communities';
import { runWorkflow as runCreateCommunityReports } from './create-community-reports';
import { runWorkflow as runCreateCommunityReportsText } from './create-community-reports-text';
import { runWorkflow as runCreateFinalDocuments } from './create-final-documents';
import { runWorkflow as runCreateFinalTextUnits } from './create-final-text-units';
import { runWorkflow as runExtractCovariates } from './extract-covariates';
import { runWorkflow as runExtractGraph } from './extract-graph';
import { runWorkflow as runExtractGraphNlp } from './extract-graph-nlp';
import { runWorkflow as runFinalizeGraph } from './finalize-graph';
import { runWorkflow as runGenerateTextEmbeddings } from './generate-text-embeddings';
import { runWorkflow as runLoadInputDocuments } from './load-input-documents';
import { runWorkflow as runLoadUpdateDocuments } from './load-update-documents';
import { runWorkflow as runPruneGraph } from './prune-graph';
import { runWorkflow as runUpdateCleanState } from './update-clean-state';
import { runWorkflow as runUpdateCommunities } from './update-communities';
import { runWorkflow as runUpdateCommunityReports } from './update-community-reports';
import { runWorkflow as runUpdateCovariates } from './update-covariates';
import { runWorkflow as runUpdateEntitiesRelationships } from './update-entities-relationships';
import { runWorkflow as runUpdateFinalDocuments } from './update-final-documents';
import { runWorkflow as runUpdateTextEmbeddings } from './update-text-embeddings';
import { runWorkflow as runUpdateTextUnits } from './update-text-units';

// Register all built-in workflows at once
PipelineFactory.registerAll({
    "load_input_documents": runLoadInputDocuments,
    "load_update_documents": runLoadUpdateDocuments,
    "create_base_text_units": runCreateBaseTextUnits,
    "create_communities": runCreateCommunities,
    "create_community_reports_text": runCreateCommunityReportsText,
    "create_community_reports": runCreateCommunityReports,
    "extract_covariates": runExtractCovariates,
    "create_final_documents": runCreateFinalDocuments,
    "create_final_text_units": runCreateFinalTextUnits,
    "extract_graph_nlp": runExtractGraphNlp,
    "extract_graph": runExtractGraph,
    "finalize_graph": runFinalizeGraph,
    "generate_text_embeddings": runGenerateTextEmbeddings,
    "prune_graph": runPruneGraph,
    "update_final_documents": runUpdateFinalDocuments,
    "update_text_embeddings": runUpdateTextEmbeddings,
    "update_community_reports": runUpdateCommunityReports,
    "update_entities_relationships": runUpdateEntitiesRelationships,
    "update_communities": runUpdateCommunities,
    "update_covariates": runUpdateCovariates,
    "update_text_units": runUpdateTextUnits,
    "update_clean_state": runUpdateCleanState,
});

// Export factory and individual workflows
export { PipelineFactory };
export * from './factory';
export * from './load-input-documents';
export * from './create-base-text-units';
export * from './extract-graph';
export * from './create-communities';
export * from './generate-text-embeddings';