/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * CLI implementation of the prompt-tune subcommand.
 */

import * as pathModule from 'path';
import * as fs from 'fs';
import * as api from '../api';
import { ReportingType } from '../config/enums';
import { loadConfig } from '../config/load_config';
import {
    COMMUNITY_SUMMARIZATION_FILENAME,
} from '../prompt_tune/generator/community_report_summarization';
import {
    ENTITY_SUMMARIZATION_FILENAME,
} from '../prompt_tune/generator/entity_summarization_prompt';
import {
    EXTRACT_GRAPH_FILENAME,
} from '../prompt_tune/generator/extract_graph_prompt';
import { redact } from '../utils/cli';

const logger = console;

export interface PromptTuneOptions {
    root: string;
    config?: string;
    domain?: string;
    verbose: boolean;
    selection_method: api.DocSelectionType;
    limit: number;
    max_tokens: number;
    chunk_size: number;
    overlap: number;
    language?: string;
    discover_entity_types: boolean;
    output: string;
    n_subset_max: number;
    k: number;
    min_examples_required: number;
}

/**
 * Prompt tune the model.
 */
export async function prompt_tune(options: PromptTuneOptions): Promise<void> {
    const rootPath = pathModule.resolve(options.root);
    const graphConfig = loadConfig(rootPath, options.config);

    // Override chunking config in the configuration
    if (options.chunk_size !== graphConfig.chunks.size) {
        graphConfig.chunks.size = options.chunk_size;
    }

    if (options.overlap !== graphConfig.chunks.overlap) {
        graphConfig.chunks.overlap = options.overlap;
    }

    // Configure the root logger with the specified log level
    // Note: Logger initialization would be handled by the logging system

    // Log the configuration details
    if (graphConfig.reporting.type === ReportingType.FILE) {
        const logDir = pathModule.join(rootPath, graphConfig.reporting.baseDir || '');
        const logPath = pathModule.join(logDir, 'logs.txt');
        logger.info(`Logging enabled at ${logPath}`);
    } else {
        logger.info(`Logging not enabled for config ${redact(graphConfig)}`);
    }

    const prompts = await api.generateIndexingPrompts({
        config: graphConfig,
        chunkSize: options.chunk_size,
        overlap: options.overlap,
        limit: options.limit,
        selectionMethod: options.selection_method,
        domain: options.domain,
        language: options.language,
        maxTokens: options.max_tokens,
        discoverEntityTypes: options.discover_entity_types,
        minExamplesRequired: options.min_examples_required,
        nSubsetMax: options.n_subset_max,
        k: options.k,
    });

    const outputPath = pathModule.resolve(options.output);
    if (outputPath) {
        logger.info(`Writing prompts to ${outputPath}`);

        if (!fs.existsSync(outputPath)) {
            fs.mkdirSync(outputPath, { recursive: true });
        }

        const extractGraphPromptPath = pathModule.join(outputPath, EXTRACT_GRAPH_FILENAME);
        const entitySummarizationPromptPath = pathModule.join(outputPath, ENTITY_SUMMARIZATION_FILENAME);
        const communitySummarizationPromptPath = pathModule.join(outputPath, COMMUNITY_SUMMARIZATION_FILENAME);
        
        // Write files to output path
        fs.writeFileSync(extractGraphPromptPath, prompts[0], 'utf-8');
        fs.writeFileSync(entitySummarizationPromptPath, prompts[1], 'utf-8');
        fs.writeFileSync(communitySummarizationPromptPath, prompts[2], 'utf-8');
        
        logger.info(`Prompts written to ${outputPath}`);
    } else {
        logger.error("No output path provided. Skipping writing prompts.");
    }
}