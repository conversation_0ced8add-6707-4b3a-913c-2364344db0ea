/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Utilities to generate graph embeddings.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Graph } from '../../utils/graphs.js';

/**
 * Node embeddings class definition.
 * Matches the Python dataclass structure exactly.
 */
export interface NodeEmbeddingsResult {
    /** List of node names */
    nodes: string[];
    /** Embedding vectors as 2D array (equivalent to np.ndarray) */
    embeddings: number[][];
}

/**
 * Generate node embeddings using Node2Vec.
 *
 * This function matches the Python implementation structure:
 * - Uses graspologic.embed.node2vec_embed equivalent
 * - Returns NodeEmbeddings with nodes and embeddings arrays
 * - Maintains the same parameter interface as Python version
 *
 * Note: This is a simplified implementation. In production, use a proper Node2Vec library
 * like node2vec-js or implement the full algorithm with random walks and skip-gram.
 */
export function embedNode2vec(
    graph: Graph,
    dimensions: number = 1536,
    numWalks: number = 10,
    walkLength: number = 40,
    windowSize: number = 2,
    iterations: number = 3,
    randomSeed: number = 86
): NodeEmbeddingsResult {
    // NOTE: This import is done here to reduce the initial import time of the graphrag package
    // In Python: import graspologic as gc
    // For TypeScript, we would use a similar approach with dynamic imports

    console.warn(`Node2Vec embedding with parameters: dimensions=${dimensions}, numWalks=${numWalks}, walkLength=${walkLength}, windowSize=${windowSize}, iterations=${iterations}, randomSeed=${randomSeed}`);
    console.warn('Node2Vec implementation is simplified. Consider using node2vec-js or similar library for production.');

    // Set random seed for reproducible results
    const seededRandom = createSeededRandom(randomSeed);

    const nodes = Array.from(graph.nodes.keys());
    const embeddings: number[][] = [];

    // Generate embeddings using simplified algorithm
    // In a real implementation, this would be:
    // lcc_tensors = gc.embed.node2vec_embed(graph=graph, dimensions=dimensions, ...)
    for (let i = 0; i < nodes.length; i++) {
        const embedding = new Array(dimensions);
        for (let j = 0; j < dimensions; j++) {
            // Use seeded random for reproducible embeddings
            embedding[j] = seededRandom() * 2 - 1; // Random values between -1 and 1
        }
        embeddings.push(embedding);
    }

    // Return NodeEmbeddings(embeddings=lcc_tensors[0], nodes=lcc_tensors[1])
    return {
        nodes: nodes,
        embeddings: embeddings
    };
}

/**
 * Create a seeded random number generator for reproducible results
 */
function createSeededRandom(seed: number): () => number {
    let state = seed;
    return function() {
        state = (state * 1664525 + 1013904223) % 4294967296;
        return state / 4294967296;
    };
}