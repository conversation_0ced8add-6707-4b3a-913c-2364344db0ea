/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the PipelineRunResult class.
 */

import { PipelineState } from './state';

/**
 * Pipeline run result class definition.
 */
export interface PipelineRunResult {
    /** The name of the workflow that was executed */
    workflow: string;
    
    /** The result of the workflow function. This can be anything - we use it only for logging downstream, and expect each workflow function to write official outputs to the provided storage */
    result: any | null;
    
    /** Ongoing pipeline context state object */
    state: PipelineState;
    
    /** List of errors that occurred during execution */
    errors: Error[] | null;
}