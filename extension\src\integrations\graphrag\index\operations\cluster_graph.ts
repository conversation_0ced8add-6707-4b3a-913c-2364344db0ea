/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing cluster_graph, apply_clustering and run_layout methods definition.
 */

import { Graph } from '../utils/graphs';
import { stableLargestConnectedComponent } from '../utils/stable-lcc';

const logger = console;

// Type for communities: [level, cluster_id, parent_id, nodes]
export type Communities = Array<[number, number, number, string[]]>;

/**
 * Apply a hierarchical clustering algorithm to a graph.
 * @param graph - The graph to cluster
 * @param maxClusterSize - Maximum cluster size
 * @param useLcc - Whether to use largest connected component
 * @param seed - Random seed
 * @returns Communities array
 */
export function clusterGraph(
    graph: Graph,
    maxClusterSize: number,
    useLcc: boolean,
    seed?: number
): Communities {
    if (graph.nodes.size === 0) {
        logger.warn("Graph has no nodes");
        return [];
    }

    const [nodeIdToCommunityMap, parentMapping] = computeLeidenCommunities(
        graph,
        maxClusterSize,
        useLcc,
        seed
    );

    const levels = Object.keys(nodeIdToCommunityMap).map(Number).sort((a, b) => a - b);

    const clusters: Record<number, Record<number, string[]>> = {};
    for (const level of levels) {
        const result: Record<number, string[]> = {};
        clusters[level] = result;
        
        for (const [nodeId, rawCommunityId] of Object.entries(nodeIdToCommunityMap[level])) {
            const communityId = rawCommunityId;
            if (!(communityId in result)) {
                result[communityId] = [];
            }
            result[communityId].push(nodeId);
        }
    }

    const results: Communities = [];
    for (const level of levels) {
        for (const [clusterId, nodes] of Object.entries(clusters[level])) {
            const clusterIdNum = parseInt(clusterId, 10);
            results.push([level, clusterIdNum, parentMapping[clusterIdNum] || -1, nodes]);
        }
    }
    
    return results;
}

/**
 * Return Leiden root communities and their hierarchy mapping.
 * Note: This is a simplified implementation. In production, you would use a proper graph clustering library.
 */
function computeLeidenCommunities(
    graph: Graph,
    maxClusterSize: number,
    useLcc: boolean,
    seed?: number
): [Record<number, Record<string, number>>, Record<number, number>] {
    let workingGraph = graph;
    
    if (useLcc) {
        workingGraph = stableLargestConnectedComponent(graph);
    }

    // Simplified clustering implementation
    // In a real implementation, you would use hierarchical Leiden clustering
    const results: Record<number, Record<string, number>> = {};
    const hierarchy: Record<number, number> = {};

    // Create a simple single-level clustering
    const nodes = Array.from(workingGraph.nodes.keys());
    const level = 0;
    results[level] = {};
    
    // Simple clustering: assign nodes to clusters based on their connections
    let clusterId = 0;
    const visited = new Set<string>();
    
    for (const node of nodes) {
        if (!visited.has(node)) {
            results[level][node] = clusterId;
            visited.add(node);
            
            // Find connected nodes and add them to the same cluster (simplified)
            const connectedNodes = getConnectedNodes(workingGraph, node, maxClusterSize - 1);
            for (const connectedNode of connectedNodes) {
                if (!visited.has(connectedNode)) {
                    results[level][connectedNode] = clusterId;
                    visited.add(connectedNode);
                }
            }
            
            hierarchy[clusterId] = -1; // No parent for root level
            clusterId++;
        }
    }

    return [results, hierarchy];
}

/**
 * Get connected nodes (simplified implementation).
 */
function getConnectedNodes(graph: Graph, startNode: string, maxNodes: number): string[] {
    const connected: string[] = [];
    
    for (const [_, edge] of graph.edges) {
        if (connected.length >= maxNodes) break;
        
        if (edge.source === startNode && !connected.includes(edge.target)) {
            connected.push(edge.target);
        } else if (edge.target === startNode && !connected.includes(edge.source)) {
            connected.push(edge.source);
        }
    }
    
    return connected;
}