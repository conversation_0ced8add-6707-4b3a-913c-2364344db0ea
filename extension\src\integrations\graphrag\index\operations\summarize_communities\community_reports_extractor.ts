/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the CommunityReportsExtractor class definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { ChatModel } from '../../../query/llm/base.js';
import { numTokens } from '../../../query/llm/text_utils.js';
import { CommunityReport } from './typing.js';

const logger = console;

/**
 * Community reports extractor class.
 * Matches the Python CommunityReportsExtractor class exactly.
 */
export class CommunityReportsExtractor {
    private _llm: ChatModel;
    private _extraction_prompt: string;
    private _max_report_length: number;
    private _on_error: (error: Error, stack: string | null, details: any) => void;

    /**
     * Initialize the CommunityReportsExtractor.
     * Matches the Python __init__ method exactly.
     */
    constructor(
        llm: ChatModel,
        extraction_prompt: string,
        on_error: (error: Error, stack: string | null, details: any) => void,
        max_report_length: number = 1500
    ) {
        this._llm = llm;
        this._extraction_prompt = extraction_prompt;
        this._max_report_length = max_report_length;
        this._on_error = on_error;
    }

    /**
     * Extract a community report from a community context.
     * Matches the Python __call__ method exactly.
     */
    async call(
        community_id: string,
        community_context: string,
        level: number = 0
    ): Promise<CommunityReport | null> {
        try {
            // Python: response = await self._llm(
            //     messages=[
            //         {
            //             "role": "system",
            //             "content": self._extraction_prompt,
            //         },
            //         {
            //             "role": "user", 
            //             "content": community_context,
            //         },
            //     ],
            //     name="create_community_report",
            //     max_tokens=self._max_report_length,
            // )
            const response = await this._llm.call([
                {
                    role: "system",
                    content: this._extraction_prompt,
                },
                {
                    role: "user",
                    content: community_context,
                },
            ], {
                name: "create_community_report",
                max_tokens: this._max_report_length,
            });

            // Python: return CommunityReport(
            //     community_id=community_id,
            //     level=level,
            //     title=response.json.get("title", f"Community Report: {community_id}"),
            //     summary=response.json.get("summary", ""),
            //     full_content=response.json.get("summary", ""),
            //     full_content_json=response.json,
            //     rank=_get_level_rank(level),
            //     rank_explanation=response.json.get("rating_explanation", ""),
            //     findings=response.json.get("findings", []),
            // )
            const json_response = response.json || {};
            
            return {
                community_id: community_id,
                level: level,
                title: json_response.title || `Community Report: ${community_id}`,
                summary: json_response.summary || "",
                full_content: json_response.summary || "",
                full_content_json: json_response,
                rank: _get_level_rank(level),
                rank_explanation: json_response.rating_explanation || "",
                findings: json_response.findings || [],
            };

        } catch (error) {
            // Python: log.exception("Error extracting community report")
            // Python: self._on_error(e, traceback.format_exc(), None)
            // Python: return None
            const err = error instanceof Error ? error : new Error(String(error));
            logger.error("Error extracting community report", err);
            this._on_error(err, err.stack || null, null);
            return null;
        }
    }
}

/**
 * Get the rank for a given level.
 * Matches the Python _get_level_rank function exactly.
 */
function _get_level_rank(level: number): number {
    // Python: return 8.5 - level
    return 8.5 - level;
}
