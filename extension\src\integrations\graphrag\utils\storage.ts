// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Storage functions for the GraphRAG run module.
 */

import { PipelineStorage } from '../storage/pipeline-storage';

// Note: In a real implementation, you would need a DataFrame-like library
// For now, we'll use a generic Record type to represent tabular data
export type DataFrame = Record<string, any>[];

/**
 * Load a parquet from the storage instance.
 */
export async function loadTableFromStorage(name: string, storage: PipelineStorage): Promise<DataFrame> {
    const filename = `${name}.parquet`;
    if (!(await storage.has(filename))) {
        const msg = `Could not find ${filename} in storage!`;
        throw new Error(msg);
    }
    
    try {
        console.log(`reading table from storage: ${filename}`);
        const data = await storage.get(filename, { asBytes: true });
        
        // Note: In a real implementation, you would use a parquet library
        // For now, we'll assume the data is JSON-serialized
        if (typeof data === 'string') {
            return JSON.parse(data);
        } else if (data instanceof Buffer) {
            return JSON.parse(data.toString('utf-8'));
        } else {
            throw new Error('Unexpected data type from storage');
        }
    } catch (error) {
        console.error(`error loading table from storage: ${filename}`, error);
        throw error;
    }
}

/**
 * Write a table to storage.
 */
export async function writeTableToStorage(
    table: DataFrame,
    name: string,
    storage: PipelineStorage
): Promise<void> {
    // Note: In a real implementation, you would use a parquet library
    // For now, we'll serialize as JSON
    const serializedData = JSON.stringify(table);
    await storage.set(`${name}.parquet`, serializedData);
}

/**
 * Delete a table from storage.
 */
export async function deleteTableFromStorage(name: string, storage: PipelineStorage): Promise<void> {
    await storage.delete(`${name}.parquet`);
}

/**
 * Check if a table exists in storage.
 */
export async function storageHasTable(name: string, storage: PipelineStorage): Promise<boolean> {
    return await storage.has(`${name}.parquet`);
}