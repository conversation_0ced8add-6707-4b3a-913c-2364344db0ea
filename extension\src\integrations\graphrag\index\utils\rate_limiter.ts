/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Rate limiter utility.
 */

/**
 * The original TpmRpmLLMLimiter strategy did not account for minute-based rate limiting when scheduled.
 * 
 * The RateLimiter was introduced to ensure that the CommunityReportsExtractor could be scheduled 
 * to adhere to rate configurations on a per-minute basis.
 */
export class RateLimiter {
    private rate: number;
    private per: number;
    private allowance: number;
    private lastCheck: number;

    constructor(rate: number, per: number) {
        this.rate = rate;
        this.per = per;
        this.allowance = rate;
        this.lastCheck = Date.now() / 1000; // Convert to seconds
    }

    /**
     * Acquire a token from the rate limiter.
     */
    async acquire(): Promise<void> {
        const current = Date.now() / 1000; // Convert to seconds
        const elapsed = current - this.lastCheck;
        this.lastCheck = current;
        this.allowance += elapsed * (this.rate / this.per);

        if (this.allowance > this.rate) {
            this.allowance = this.rate;
        }

        if (this.allowance < 1.0) {
            const sleepTime = (1.0 - this.allowance) * (this.per / this.rate) * 1000; // Convert to milliseconds
            await new Promise(resolve => setTimeout(resolve, sleepTime));
            this.allowance = 0.0;
        } else {
            this.allowance -= 1.0;
        }
    }
}