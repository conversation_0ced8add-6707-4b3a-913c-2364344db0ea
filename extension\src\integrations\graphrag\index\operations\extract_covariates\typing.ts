/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'Covariate' and 'CovariateExtractionResult' models.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../callbacks/workflow_callbacks.js';

/**
 * Covariate class definition.
 * Matches the Python dataclass structure exactly.
 */
export interface Covariate {
    covariate_type?: string | null;
    subject_id?: string | null;
    object_id?: string | null;
    type?: string | null;
    status?: string | null;
    start_date?: string | null;
    end_date?: string | null;
    description?: string | null;
    source_text?: string[] | null;
    doc_id?: string | null;
    record_id?: number | null;
    id?: string | null;
}

/**
 * Covariate extraction result class definition.
 * Matches the Python dataclass structure exactly.
 */
export interface CovariateExtractionResult {
    covariate_data: Covariate[];
}

/**
 * Covariate extract strategy function type.
 * Matches the Python Callable type signature exactly.
 */
export type CovariateExtractStrategy = (
    input: Iterable<string>,
    entityTypes: string[],
    resolvedEntitiesMap: Record<string, string>,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    config: Record<string, any>
) => Promise<CovariateExtractionResult>;