/**
 * Copyright (c) 2025 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * FNLLM Cache provider.
 */

import { PipelineCache } from '../../../cache/pipeline-cache';

export class FNLLMCacheProvider {
    private _cache: PipelineCache;

    constructor(cache: Pipeline<PERSON>ache) {
        this._cache = cache;
    }

    async has(key: string): Promise<boolean> {
        return await this._cache.has(key);
    }

    async get(key: string): Promise<any | null> {
        return await this._cache.get(key);
    }

    async set(
        key: string, 
        value: any, 
        metadata?: Record<string, any>
    ): Promise<void> {
        await this._cache.set(key, value, metadata);
    }

    async remove(key: string): Promise<void> {
        await this._cache.delete(key);
    }

    async clear(): Promise<void> {
        await this._cache.clear();
    }

    child(key: string): FNLLMCacheProvider {
        const childCache = this._cache.child(key);
        return new FNLLMCacheProvider(childCache);
    }
}