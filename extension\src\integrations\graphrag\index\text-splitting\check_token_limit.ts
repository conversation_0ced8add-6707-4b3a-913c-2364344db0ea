/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Token limit method definition.
 */

import { TokenTextSplitter } from './text-splitting';

/**
 * Check token limit.
 * @param text - Text to check
 * @param maxToken - Maximum token limit
 * @returns 1 if within limit, 0 if exceeds limit
 */
export function checkTokenLimit(text: string, maxToken: number): number {
    const textSplitter = new TokenTextSplitter({ chunkSize: maxToken, chunkOverlap: 0 });
    const docs = textSplitter.splitText(text);
    
    if (docs.length > 1) {
        return 0;
    }
    return 1;
}