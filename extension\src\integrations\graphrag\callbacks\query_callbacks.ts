// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Query Callbacks.
 */

import { BaseLLMCallback } from './llm_callbacks';
import { SearchResult } from '../query/structured_search/base';

/**
 * Callbacks used during query execution.
 */
export interface QueryCallbacks extends BaseLLMCallback {
    /**
     * Handle when context data is constructed.
     */
    on_context(context: any): void;

    /**
     * Handle the start of map operation.
     */
    on_map_response_start(map_response_contexts: string[]): void;

    /**
     * Handle the end of map operation.
     */
    on_map_response_end(map_response_outputs: SearchResult[]): void;

    /**
     * Handle the start of reduce operation.
     */
    on_reduce_response_start(reduce_response_context: string | Record<string, any>): void;

    /**
     * Handle the end of reduce operation.
     */
    on_reduce_response_end(reduce_response_output: string): void;

    /**
     * Handle when a new token is generated.
     */
    on_llm_new_token(token: string): void;
}