// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Input loading module.
 */

import { NoopPipelineCache } from '../../cache/noop-pipeline-cache';
import { NoopWorkflowCallbacks } from '../../callbacks/noop-workflow-callbacks';
import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { createInput } from '../../index/input/factory';
import { run as runEmbedText } from '../../index/operations/embed_text/strategies/openai';
import { createBaseTextUnits } from '../../index/workflows/create_base_text_units';
import { LIMIT, N_SUBSET_MAX, K } from '../defaults';
import { DocSelectionType } from '../types';
import { createStorageFromConfig } from '../../utils/api';
import { DataFrame } from '../../data-model/types';

const logger = console;

/**
 * Sample text chunks from embeddings using k-means-like approach.
 * 
 * @param textChunks - DataFrame containing text chunks
 * @param embeddings - Array of embedding vectors
 * @param k - Number of samples to select
 * @returns Sampled DataFrame
 */
function sampleChunksFromEmbeddings(
    textChunks: DataFrame,
    embeddings: number[][],
    k: number = K
): DataFrame {
    if (embeddings.length === 0 || textChunks.data.length === 0) {
        return { columns: textChunks.columns, data: [] };
    }

    // Calculate center of embeddings
    const dimensions = embeddings[0].length;
    const center = new Array(dimensions).fill(0);
    
    for (const embedding of embeddings) {
        for (let i = 0; i < dimensions; i++) {
            center[i] += embedding[i];
        }
    }
    
    for (let i = 0; i < dimensions; i++) {
        center[i] /= embeddings.length;
    }

    // Calculate distances from center
    const distances = embeddings.map(embedding => {
        let distance = 0;
        for (let i = 0; i < dimensions; i++) {
            const diff = embedding[i] - center[i];
            distance += diff * diff;
        }
        return Math.sqrt(distance);
    });

    // Get indices of k nearest points
    const indexedDistances = distances.map((distance, index) => ({ distance, index }));
    indexedDistances.sort((a, b) => a.distance - b.distance);
    const nearestIndices = indexedDistances.slice(0, k).map(item => item.index);

    // Return sampled chunks
    const sampledData = nearestIndices.map(index => textChunks.data[index]);
    
    return {
        columns: textChunks.columns,
        data: sampledData
    };
}

/**
 * Load docs into chunks for generating prompts.
 * 
 * @param config - GraphRAG configuration
 * @param selectMethod - Method for selecting document chunks
 * @param limit - Maximum number of chunks to return
 * @param chunkSize - Size of each text chunk
 * @param overlap - Overlap between chunks
 * @param nSubsetMax - Maximum number of chunks to embed for AUTO selection
 * @param k - Number of chunks to select when using AUTO method
 * @returns Array of text chunks
 */
export async function loadDocsInChunks(options: {
    config: GraphRagConfig;
    selectMethod: DocSelectionType;
    limit: number;
    chunkSize: number;
    overlap: number;
    nSubsetMax?: number;
    k?: number;
}): Promise<string[]> {
    const {
        config,
        selectMethod,
        limit: inputLimit,
        chunkSize,
        overlap,
        nSubsetMax = N_SUBSET_MAX,
        k = K
    } = options;

    let limit = inputLimit;

    // Get embedding model settings
    const embeddingsLlmSettings = config.getLanguageModelConfig(
        config.embedText.modelId
    );

    // Create input storage and load dataset
    const inputStorage = createStorageFromConfig(config.input.storage);
    const dataset = await createInput(config.input, inputStorage);
    
    // Get chunk configuration
    const chunkConfig = config.chunks;
    
    // Create text units (chunks)
    const chunksDF = createBaseTextUnits(
        dataset,
        new NoopWorkflowCallbacks(),
        chunkConfig.groupByColumns,
        chunkSize,
        overlap,
        chunkConfig.encodingModel,
        chunkConfig.strategy,
        chunkConfig.prependMetadata,
        chunkConfig.chunkSizeIncludesMetadata
    );

    // Validate and adjust limit
    if (limit <= 0 || limit > chunksDF.data.length) {
        logger.warn(`Limit out of range, using default number of chunks: ${LIMIT}`);
        limit = LIMIT;
    }

    let finalChunksDF: DataFrame;

    // Apply selection method
    switch (selectMethod) {
        case DocSelectionType.TOP:
            finalChunksDF = {
                columns: chunksDF.columns,
                data: chunksDF.data.slice(0, limit)
            };
            break;

        case DocSelectionType.RANDOM:
            // Shuffle and take first 'limit' items
            const shuffledData = [...chunksDF.data];
            for (let i = shuffledData.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffledData[i], shuffledData[j]] = [shuffledData[j], shuffledData[i]];
            }
            finalChunksDF = {
                columns: chunksDF.columns,
                data: shuffledData.slice(0, limit)
            };
            break;

        case DocSelectionType.AUTO:
            if (!k || k <= 0) {
                throw new Error("k must be an integer > 0");
            }

            // Sample text chunks for embedding
            const maxSampleSize = Math.min(nSubsetMax, chunksDF.data.length);
            const sampledIndices = new Set<number>();
            
            while (sampledIndices.size < maxSampleSize) {
                sampledIndices.add(Math.floor(Math.random() * chunksDF.data.length));
            }
            
            const sampledTextChunks = Array.from(sampledIndices).map(index => {
                const row = chunksDF.data[index];
                const textColumnIndex = chunksDF.columns.indexOf('text');
                return textColumnIndex >= 0 ? row[textColumnIndex] : '';
            }).filter(text => text);

            // Generate embeddings
            const embeddingResults = await runEmbedText(
                sampledTextChunks,
                new NoopWorkflowCallbacks(),
                new NoopPipelineCache(),
                {
                    llm: embeddingsLlmSettings.modelDump(),
                    numThreads: embeddingsLlmSettings.concurrentRequests,
                    batchSize: config.embedText.batchSize,
                    batchMaxTokens: config.embedText.batchMaxTokens,
                }
            );

            if (!embeddingResults.embeddings) {
                throw new Error("Failed to generate embeddings");
            }

            // Sample chunks based on embeddings
            finalChunksDF = sampleChunksFromEmbeddings(chunksDF, embeddingResults.embeddings, k);
            break;

        default:
            throw new Error(`Unknown selection method: ${selectMethod}`);
    }

    // Extract text column and format for LaTeX compatibility
    const textColumnIndex = finalChunksDF.columns.indexOf('text');
    if (textColumnIndex < 0) {
        throw new Error("No 'text' column found in chunks DataFrame");
    }

    return finalChunksDF.data.map(row => {
        const text = row[textColumnIndex] || '';
        // Prevent str.format() function from breaking when parsing LaTeX from markdown files
        return text.replace(/\{/g, '{{').replace(/\}/g, '}}');
    });
}
