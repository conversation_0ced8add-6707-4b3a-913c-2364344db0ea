// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * DRIFT Context Builder implementation.
 */

import { DRIFTSearchConfig } from '../../../config/models/drift-search-config';
import { CommunityReport } from '../../../data_model/community-report';
import { Covariate } from '../../../data_model/covariate';
import { Entity } from '../../../data_model/entity';
import { Relationship } from '../../../data_model/relationship';
import { TextUnit } from '../../../data_model/text-unit';
import { ChatModel, EmbeddingModel } from '../../../language_model/protocol/base';
import { 
    DRIFT_LOCAL_SYSTEM_PROMPT,
    DRIFT_REDUCE_PROMPT 
} from '../../../prompts/query/drift-search-system-prompt';
import { EntityVectorStoreKey } from '../../context_builder/entity-extraction';
import { DRIFTContextBuilder } from '../base';
import { PrimerQueryProcessor } from './primer';
import { LocalSearchMixedContext } from '../local_search/mixed-context';
import { BaseVectorStore } from '../../../vector_stores/base';
import { Encoding } from 'tiktoken';

const logger = console;

export interface DRIFTSearchContextBuilderConfig {
    model: ChatModel;
    textEmbedder: EmbeddingModel;
    entities: Entity[];
    entityTextEmbeddings: BaseVectorStore;
    textUnits?: TextUnit[];
    reports?: CommunityReport[];
    relationships?: Relationship[];
    covariates?: Record<string, Covariate[]>;
    tokenEncoder?: Encoding;
    embeddingVectorstoreKey?: string;
    config?: DRIFTSearchConfig;
    localSystemPrompt?: string;
    localMixedContext?: LocalSearchMixedContext;
    reduceSystemPrompt?: string;
    responseType?: string;
}

export class DRIFTSearchContextBuilder implements DRIFTContextBuilder {
    public readonly config: DRIFTSearchConfig;
    public readonly model: ChatModel;
    public readonly textEmbedder: EmbeddingModel;
    public readonly tokenEncoder?: Encoding;
    public readonly localSystemPrompt: string;
    public readonly reduceSystemPrompt: string;
    public readonly entities: Entity[];
    public readonly entityTextEmbeddings: BaseVectorStore;
    public readonly reports?: CommunityReport[];
    public readonly textUnits?: TextUnit[];
    public readonly relationships?: Relationship[];
    public readonly covariates?: Record<string, Covariate[]>;
    public readonly embeddingVectorstoreKey: string;
    public readonly responseType?: string;
    public readonly localMixedContext: LocalSearchMixedContext;

    constructor(options: DRIFTSearchContextBuilderConfig) {
        this.config = options.config || new DRIFTSearchConfig();
        this.model = options.model;
        this.textEmbedder = options.textEmbedder;
        this.tokenEncoder = options.tokenEncoder;
        this.localSystemPrompt = options.localSystemPrompt || DRIFT_LOCAL_SYSTEM_PROMPT;
        this.reduceSystemPrompt = options.reduceSystemPrompt || DRIFT_REDUCE_PROMPT;

        this.entities = options.entities;
        this.entityTextEmbeddings = options.entityTextEmbeddings;
        this.reports = options.reports;
        this.textUnits = options.textUnits;
        this.relationships = options.relationships;
        this.covariates = options.covariates;
        this.embeddingVectorstoreKey = options.embeddingVectorstoreKey || EntityVectorStoreKey.ID;

        this.responseType = options.responseType;

        this.localMixedContext = options.localMixedContext || this.initLocalContextBuilder();
    }

    /**
     * Initialize the local search mixed context builder.
     */
    private initLocalContextBuilder(): LocalSearchMixedContext {
        return new LocalSearchMixedContext({
            communityReports: this.reports,
            textUnits: this.textUnits,
            entities: this.entities,
            relationships: this.relationships,
            covariates: this.covariates,
            entityTextEmbeddings: this.entityTextEmbeddings,
            embeddingVectorstoreKey: this.embeddingVectorstoreKey,
            textEmbedder: this.textEmbedder,
            tokenEncoder: this.tokenEncoder,
        });
    }

    /**
     * Convert a list of CommunityReport objects to a DataFrame-like structure.
     */
    public static convertReportsToDataFrame(reports: CommunityReport[]): Record<string, any>[] {
        const reportData = reports.map(report => ({
            ...report,
            id: report.id,
            title: report.title,
            community_id: report.communityId,
            summary: report.summary,
            full_content: report.fullContent,
            full_content_embedding: report.fullContentEmbedding,
            rank: report.rank,
            rank_explanation: report.rankExplanation,
            findings: report.findings,
            period: report.period,
            size: report.size
        }));

        const missingContentError = "Some reports are missing full content.";
        const missingEmbeddingError = "Some reports are missing full content embeddings. {missing} out of {total}";

        // Check for missing full content
        const missingContent = reportData.filter(report => !report.full_content);
        if (missingContent.length > 0) {
            throw new Error(missingContentError);
        }

        // Check for missing full content embeddings
        const missingEmbeddings = reportData.filter(report => !report.full_content_embedding);
        if (missingEmbeddings.length > 0) {
            throw new Error(
                missingEmbeddingError
                    .replace('{missing}', missingEmbeddings.length.toString())
                    .replace('{total}', reportData.length.toString())
            );
        }

        return reportData;
    }

    /**
     * Check if the embeddings are compatible.
     */
    public static checkQueryDocEncodings(queryEmbedding: any, embedding: any): boolean {
        return (
            queryEmbedding !== null &&
            queryEmbedding !== undefined &&
            embedding !== null &&
            embedding !== undefined &&
            typeof queryEmbedding === typeof embedding &&
            Array.isArray(queryEmbedding) &&
            Array.isArray(embedding) &&
            queryEmbedding.length === embedding.length &&
            typeof queryEmbedding[0] === typeof embedding[0]
        );
    }

    /**
     * Build DRIFT search context.
     */
    public async buildContext(query: string, kwargs?: Record<string, any>): Promise<[Record<string, any>[], Record<string, number>]> {
        if (!this.reports) {
            const missingReportsError = "No community reports available. Please provide a list of reports.";
            throw new Error(missingReportsError);
        }

        const queryProcessor = new PrimerQueryProcessor({
            chatModel: this.model,
            textEmbedder: this.textEmbedder,
            tokenEncoder: this.tokenEncoder,
            reports: this.reports,
        });

        const [queryEmbedding, tokenCt] = await queryProcessor.process(query);

        const reportData = DRIFTSearchContextBuilder.convertReportsToDataFrame(this.reports);

        // Check compatibility between query embedding and document embeddings
        if (!DRIFTSearchContextBuilder.checkQueryDocEncodings(
            queryEmbedding, 
            reportData[0]?.full_content_embedding
        )) {
            const errorMessage = 
                "Query and document embeddings are not compatible. " +
                "Please ensure that the embeddings are of the same type and length.";
            throw new Error(errorMessage);
        }

        // Vectorized cosine similarity computation
        const queryNorm = this.computeNorm(queryEmbedding);
        const documentNorms = reportData.map(report => 
            this.computeNorm(report.full_content_embedding)
        );
        
        const similarities = reportData.map((report, index) => {
            const dotProduct = this.computeDotProduct(report.full_content_embedding, queryEmbedding);
            return dotProduct / (documentNorms[index] * queryNorm);
        });

        // Add similarity scores to report data
        const reportsWithSimilarity = reportData.map((report, index) => ({
            ...report,
            similarity: similarities[index]
        }));

        // Sort by similarity and select top-k
        const topK = reportsWithSimilarity
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, this.config.driftKFollowups || 5)
            .map(report => ({
                short_id: report.id,
                community_id: report.community_id,
                full_content: report.full_content
            }));

        return [topK, tokenCt];
    }

    /**
     * Compute the norm of a vector.
     */
    private computeNorm(vector: number[]): number {
        return Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    }

    /**
     * Compute the dot product of two vectors.
     */
    private computeDotProduct(vector1: number[], vector2: number[]): number {
        return vector1.reduce((sum, val, index) => sum + val * vector2[index], 0);
    }
}
