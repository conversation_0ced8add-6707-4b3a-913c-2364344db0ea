// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the UMAP configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';

/**
 * Configuration section for UMAP.
 */
export interface UmapConfig {
  /**
   * A flag indicating whether to enable UMAP.
   */
  enabled: boolean;
}

/**
 * Create a UmapConfig with default values.
 */
export function createUmapConfig(config: Partial<UmapConfig> = {}): UmapConfig {
  return {
    enabled: config.enabled ?? graphragConfigDefaults.umap.enabled,
  };
}
