// Copyright (c) 2025 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A package containing a factory for supported llm types.
 */

import { ModelType } from '../config/enums';
import { ChatModel, EmbeddingModel } from './protocol/base';

// Note: These would be imported from actual provider implementations
// For now, we'll create placeholder interfaces
interface AzureOpenAIChatFNLLM extends ChatModel {}
interface AzureOpenAIEmbeddingFNLLM extends EmbeddingModel {}
interface OpenAIChatFNLLM extends ChatModel {}
interface OpenAIEmbeddingFNLLM extends EmbeddingModel {}

// Mock implementations for now
class MockAzureOpenAIChatFNLLM implements ChatModel {
    config: any;
    constructor(kwargs: any) { this.config = kwargs; }
    async achat(): Promise<any> { throw new Error('Not implemented'); }
    async *achatStream(): AsyncGenerator<string, void, unknown> { yield ''; }
    chat(): any { throw new Error('Not implemented'); }
    *chatStream(): Generator<string, void, unknown> { yield ''; }
}

class MockAzureOpenAIEmbeddingFNLLM implements EmbeddingModel {
    config: any;
    constructor(kwargs: any) { this.config = kwargs; }
    async aembedBatch(): Promise<number[][]> { return []; }
    async aembed(): Promise<number[]> { return []; }
    embedBatch(): number[][] { return []; }
    embed(): number[] { return []; }
}

class MockOpenAIChatFNLLM implements ChatModel {
    config: any;
    constructor(kwargs: any) { this.config = kwargs; }
    async achat(): Promise<any> { throw new Error('Not implemented'); }
    async *achatStream(): AsyncGenerator<string, void, unknown> { yield ''; }
    chat(): any { throw new Error('Not implemented'); }
    *chatStream(): Generator<string, void, unknown> { yield ''; }
}

class MockOpenAIEmbeddingFNLLM implements EmbeddingModel {
    config: any;
    constructor(kwargs: any) { this.config = kwargs; }
    async aembedBatch(): Promise<number[][]> { return []; }
    async aembed(): Promise<number[]> { return []; }
    embedBatch(): number[][] { return []; }
    embed(): number[] { return []; }
}

/**
 * A factory for creating Model instances.
 */
export class ModelFactory {
    private static chatRegistry: Map<string, (...args: any[]) => ChatModel> = new Map();
    private static embeddingRegistry: Map<string, (...args: any[]) => EmbeddingModel> = new Map();

    /**
     * Register a ChatModel implementation.
     */
    static registerChat(modelType: string, creator: (...args: any[]) => ChatModel): void {
        this.chatRegistry.set(modelType, creator);
    }

    /**
     * Register an EmbeddingModel implementation.
     */
    static registerEmbedding(modelType: string, creator: (...args: any[]) => EmbeddingModel): void {
        this.embeddingRegistry.set(modelType, creator);
    }

    /**
     * Create a ChatModel instance.
     * 
     * @param modelType - The type of ChatModel to create.
     * @param kwargs - Additional keyword arguments for the ChatModel constructor.
     * @returns A ChatModel instance.
     */
    static createChatModel(modelType: string, kwargs: Record<string, any> = {}): ChatModel {
        const creator = this.chatRegistry.get(modelType);
        if (!creator) {
            const msg = `ChatModel implementation '${modelType}' is not registered.`;
            throw new Error(msg);
        }
        return creator(kwargs);
    }

    /**
     * Create an EmbeddingModel instance.
     * 
     * @param modelType - The type of EmbeddingModel to create.
     * @param kwargs - Additional keyword arguments for the EmbeddingModel constructor.
     * @returns An EmbeddingModel instance.
     */
    static createEmbeddingModel(modelType: string, kwargs: Record<string, any> = {}): EmbeddingModel {
        const creator = this.embeddingRegistry.get(modelType);
        if (!creator) {
            const msg = `EmbeddingModel implementation '${modelType}' is not registered.`;
            throw new Error(msg);
        }
        return creator(kwargs);
    }

    /**
     * Get the registered ChatModel implementations.
     */
    static getChatModels(): string[] {
        return Array.from(this.chatRegistry.keys());
    }

    /**
     * Get the registered EmbeddingModel implementations.
     */
    static getEmbeddingModels(): string[] {
        return Array.from(this.embeddingRegistry.keys());
    }

    /**
     * Check if the given model type is supported for chat.
     */
    static isSupportedChatModel(modelType: string): boolean {
        return this.chatRegistry.has(modelType);
    }

    /**
     * Check if the given model type is supported for embedding.
     */
    static isSupportedEmbeddingModel(modelType: string): boolean {
        return this.embeddingRegistry.has(modelType);
    }

    /**
     * Check if the given model type is supported.
     */
    static isSupportedModel(modelType: string): boolean {
        return this.isSupportedChatModel(modelType) || this.isSupportedEmbeddingModel(modelType);
    }
}

// --- Register default implementations ---
ModelFactory.registerChat(
    ModelType.AZURE_OPENAI_CHAT,
    (kwargs: any) => new MockAzureOpenAIChatFNLLM(kwargs)
);

ModelFactory.registerChat(
    ModelType.OPENAI_CHAT,
    (kwargs: any) => new MockOpenAIChatFNLLM(kwargs)
);

ModelFactory.registerEmbedding(
    ModelType.AZURE_OPENAI_EMBEDDING,
    (kwargs: any) => new MockAzureOpenAIEmbeddingFNLLM(kwargs)
);

ModelFactory.registerEmbedding(
    ModelType.OPENAI_EMBEDDING,
    (kwargs: any) => new MockOpenAIEmbeddingFNLLM(kwargs)
);