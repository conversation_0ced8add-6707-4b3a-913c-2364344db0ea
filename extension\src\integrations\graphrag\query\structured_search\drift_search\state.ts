// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Manage the state of the DRIFT query, including a graph of actions.
 */

import { DriftAction } from './action';

const logger = console;

export interface GraphNode {
    id: number;
    action: DriftAction;
    metadata?: Record<string, any>;
}

export interface GraphEdge {
    source: number;
    target: number;
    weight: number;
}

export interface SerializedGraph {
    nodes: Array<Record<string, any>>;
    edges: GraphEdge[];
}

export class QueryState {
    private nodes: Map<DriftAction, GraphNode> = new Map();
    private edges: GraphEdge[] = [];
    private nextNodeId: number = 0;

    /**
     * Check if the graph has any nodes.
     */
    public hasGraph(): boolean {
        return this.nodes.size > 0;
    }

    /**
     * Add an action to the graph with optional metadata.
     */
    public addAction(action: DriftAction, metadata?: Record<string, any>): void {
        if (!this.nodes.has(action)) {
            const node: GraphNode = {
                id: this.nextNodeId++,
                action,
                metadata: metadata || {}
            };
            this.nodes.set(action, node);
        }
    }

    /**
     * Relate two actions in the graph.
     */
    public relateActions(parent: <PERSON>ift<PERSON><PERSON>, child: DriftAction, weight: number = 1.0): void {
        const parentNode = this.nodes.get(parent);
        const childNode = this.nodes.get(child);
        
        if (parentNode && childNode) {
            this.edges.push({
                source: parentNode.id,
                target: childNode.id,
                weight
            });
        }
    }

    /**
     * Add all follow-up actions and link them to the given action.
     */
    public addAllFollowUps(
        action: DriftAction,
        followUps: DriftAction[] | string[],
        weight: number = 1.0
    ): void {
        if (followUps.length === 0) {
            logger.warn(`No follow-up actions for action: ${action.query}`);
        }

        for (const followUp of followUps) {
            let followUpAction: DriftAction;
            
            if (typeof followUp === 'string') {
                followUpAction = new DriftAction({ query: followUp });
            } else if (followUp instanceof DriftAction) {
                followUpAction = followUp;
            } else {
                logger.warn(`Follow-up action is not a string, found type: ${typeof followUp}`);
                continue;
            }

            this.addAction(followUpAction);
            this.relateActions(action, followUpAction, weight);
        }
    }

    /**
     * Find all unanswered actions in the graph.
     */
    public findIncompleteActions(): DriftAction[] {
        return Array.from(this.nodes.keys()).filter(action => !action.isComplete);
    }

    /**
     * Rank all unanswered actions in the graph if scorer available.
     */
    public rankIncompleteActions(
        scorer?: (action: DriftAction) => number
    ): DriftAction[] {
        const unanswered = this.findIncompleteActions();
        
        if (scorer) {
            for (const action of unanswered) {
                action.computeScore(scorer);
            }
            return unanswered.sort((a, b) => {
                const scoreA = a.score !== null ? a.score : Number.NEGATIVE_INFINITY;
                const scoreB = b.score !== null ? b.score : Number.NEGATIVE_INFINITY;
                return scoreB - scoreA; // Descending order
            });
        }

        // Shuffle the list if no scorer
        const shuffled = [...unanswered];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    /**
     * Serialize the graph to a dictionary, including nodes and edges.
     */
    public serialize(includeContext: boolean = true): [Record<string, any>, Record<string, any>, string] | Record<string, any> {
        // Create serialized nodes
        const nodes = Array.from(this.nodes.values()).map(node => ({
            ...node.action.serialize(false), // includeFollowUps = false
            id: node.id,
            ...node.metadata
        }));

        // Serialize edges
        const edges = this.edges.map(edge => ({
            source: edge.source,
            target: edge.target,
            weight: edge.weight
        }));

        if (includeContext) {
            const contextData: Record<string, any> = {};
            for (const node of nodes) {
                if (node.metadata?.context_data && node.query) {
                    contextData[node.query] = node.metadata.context_data;
                }
            }

            const contextText = JSON.stringify(contextData);

            return [{ nodes, edges }, contextData, contextText];
        }

        return { nodes, edges };
    }

    /**
     * Deserialize the dictionary back to a graph.
     */
    public deserialize(data: Record<string, any>): void {
        this.nodes.clear();
        this.edges = [];
        this.nextNodeId = 0;
        
        const idToAction = new Map<number, DriftAction>();

        // Deserialize nodes
        for (const nodeData of data.nodes || []) {
            const nodeId = nodeData.id;
            delete nodeData.id;
            
            const action = DriftAction.deserialize(nodeData);
            this.addAction(action);
            idToAction.set(nodeId, action);
        }

        // Deserialize edges
        for (const edgeData of data.edges || []) {
            const sourceId = edgeData.source;
            const targetId = edgeData.target;
            const weight = edgeData.weight || 1.0;
            
            const sourceAction = idToAction.get(sourceId);
            const targetAction = idToAction.get(targetId);
            
            if (sourceAction && targetAction) {
                this.relateActions(sourceAction, targetAction, weight);
            }
        }
    }

    /**
     * Return the token count of all actions.
     */
    public actionTokenCount(): Record<string, number> {
        let llmCalls = 0;
        let promptTokens = 0;
        let outputTokens = 0;
        
        for (const node of this.nodes.values()) {
            const metadata = node.action.metadata;
            llmCalls += metadata.llm_calls || 0;
            promptTokens += metadata.prompt_tokens || 0;
            outputTokens += metadata.output_tokens || 0;
        }
        
        return {
            llm_calls: llmCalls,
            prompt_tokens: promptTokens,
            output_tokens: outputTokens,
        };
    }
}
