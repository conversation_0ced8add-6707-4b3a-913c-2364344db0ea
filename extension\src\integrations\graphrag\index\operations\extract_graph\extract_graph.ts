/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing entity_extract methods.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../data_model/types.js';
import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../callbacks/workflow_callbacks.js';
import { AsyncType } from '../../../config/enums.js';
import { deriveFromRows } from '../../utils/derive_from_rows.js';
import {
    Document,
    EntityExtractStrategy,
    ExtractEntityStrategyType,
    EntityExtractionResult
} from './typing.js';
import { runGraphIntelligence } from './graph_intelligence_strategy.js';

const logger = console;

const DEFAULT_ENTITY_TYPES = ["organization", "person", "geo", "event"];

/**
 * Extract a graph from a piece of text using a language model.
 * Matches the Python extract_graph function exactly.
 */
export async function extractGraph(
    text_units: DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    text_column: string,
    id_column: string,
    strategy?: Record<string, any> | null,
    async_mode: AsyncType = AsyncType.ASYNCIO,
    entity_types: string[] = DEFAULT_ENTITY_TYPES,
    num_threads: number = 4
): Promise<[DataFrame, DataFrame]> {
    logger.debug("entity_extract strategy=", strategy);

    if (entity_types === null || entity_types === undefined) {
        entity_types = DEFAULT_ENTITY_TYPES;
    }
    strategy = strategy || {};
    const strategy_exec = _load_strategy(
        strategy.type || ExtractEntityStrategyType.graph_intelligence
    );
    const strategy_config = { ...strategy };

    let num_started = 0;

    async function run_strategy(row: Record<string, any>): Promise<[any[], any[], any] | null> {
        const text = row[text_column];
        const id = row[id_column];

        const result = await strategy_exec(
            [{ text, id }],
            entity_types,
            cache,
            strategy_config
        );

        num_started += 1;
        return [result.entities, result.relationships, result.graph];
    }

    const results = await deriveFromRows(
        text_units,
        run_strategy,
        callbacks,
        num_threads,
        async_mode,
        "extract graph progress: "
    );

    const entity_dfs: any[] = [];
    const relationship_dfs: any[] = [];

    for (const result of results) {
        if (result) {
            entity_dfs.push(result[0]);
            relationship_dfs.push(result[1]);
        }
    }

    const entities = _merge_entities(entity_dfs);
    const relationships = _merge_relationships(relationship_dfs);

    return [entities, relationships];
}

/**
 * Load strategy method definition.
 * Matches the Python _load_strategy function exactly.
 */
function _load_strategy(strategy_type: ExtractEntityStrategyType): EntityExtractStrategy {
    switch (strategy_type) {
        case ExtractEntityStrategyType.graph_intelligence:
            // Python: from graphrag.index.operations.extract_graph.graph_intelligence_strategy import run_graph_intelligence
            return runGraphIntelligence;
        default:
            const msg = `Unknown strategy: ${strategy_type}`;
            throw new Error(msg);
    }
}

/**
 * Merge entities from multiple DataFrames.
 * Matches the Python _merge_entities function exactly.
 */
function _merge_entities(entity_dfs: any[]): DataFrame {
    if (entity_dfs.length === 0) {
        return {
            columns: ['title', 'type', 'description', 'text_unit_ids', 'frequency'],
            data: []
        };
    }

    // Combine all entities - Python: all_entities = pd.concat(entity_dfs, ignore_index=True)
    const all_entities: any[] = [];
    entity_dfs.forEach((entities: any[]) => {
        all_entities.push(...entities);
    });

    // Group by title and type
    const grouped_entities = new Map<string, any[]>();
    all_entities.forEach((entity: any) => {
        const key = `${entity.title}|${entity.type}`;
        if (!grouped_entities.has(key)) {
            grouped_entities.set(key, []);
        }
        grouped_entities.get(key)!.push(entity);
    });

    // Aggregate grouped entities - Python: .groupby(["title", "type"], sort=False).agg(...)
    const merged_data = Array.from(grouped_entities.entries()).map(([key, entities]: [string, any[]]) => {
        const [title, type] = key.split('|');
        const descriptions: string[] = [];
        const text_unit_ids: string[] = [];

        entities.forEach((entity: any) => {
            if (entity.description) {
                descriptions.push(entity.description);
            }
            if (entity.source_id) {
                text_unit_ids.push(entity.source_id);
            }
        });

        return {
            title,
            type,
            description: descriptions,
            text_unit_ids: text_unit_ids,
            frequency: text_unit_ids.length
        };
    });

    return {
        columns: ['title', 'type', 'description', 'text_unit_ids', 'frequency'],
        data: merged_data
    };
}

/**
 * Merge relationships from multiple DataFrames.
 * Matches the Python _merge_relationships function exactly.
 */
function _merge_relationships(relationship_dfs: any[]): DataFrame {
    if (relationship_dfs.length === 0) {
        return {
            columns: ['source', 'target', 'description', 'text_unit_ids', 'weight'],
            data: []
        };
    }

    // Combine all relationships - Python: all_relationships = pd.concat(relationship_dfs, ignore_index=True)
    const all_relationships: any[] = [];
    relationship_dfs.forEach((relationships: any[]) => {
        all_relationships.push(...relationships);
    });

    // Group by source and target
    const grouped_relationships = new Map<string, any[]>();
    all_relationships.forEach((relationship: any) => {
        const key = `${relationship.source}|${relationship.target}`;
        if (!grouped_relationships.has(key)) {
            grouped_relationships.set(key, []);
        }
        grouped_relationships.get(key)!.push(relationship);
    });

    // Aggregate grouped relationships
    const merged_data = Array.from(grouped_relationships.entries()).map(([key, relationships]: [string, any[]]) => {
        const [source, target] = key.split('|');
        const descriptions: string[] = [];
        const textUnitIds: string[] = [];
        let totalWeight = 0;
        
        relationships.forEach(relationship => {
            if (relationship.description) {
                descriptions.push(relationship.description);
            }
            if (relationship.source_id) {
                textUnitIds.push(relationship.source_id);
            }
            if (relationship.weight) {
                totalWeight += relationship.weight;
            }
        });

        return {
            source,
            target,
            description: descriptions,
            text_unit_ids: textUnitIds,
            weight: totalWeight
        };
    });

    return {
        columns: ['source', 'target', 'description', 'text_unit_ids', 'weight'],
        data: merged_data
    };
}