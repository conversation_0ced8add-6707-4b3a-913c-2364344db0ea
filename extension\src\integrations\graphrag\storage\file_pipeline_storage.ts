/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A module containing 'FilePipelineStorage' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import * as fs from 'fs/promises'
import * as path from 'path'
import { PipelineStorage, getTimestampFormattedWithLocalTz } from './pipeline-storage'

/**
 * File storage class definition.
 */
export class FilePipelineStorage extends PipelineStorage {
    private _rootDir: string
    private _encoding: string

    constructor(rootDir: string = "", encoding: string = "utf-8") {
        super()
        this._rootDir = rootDir
        this._encoding = encoding
        
        // Ensure directory exists
        this._ensureDirectory(this._rootDir)
    }

    private async _ensureDirectory(dir: string): Promise<void> {
        try {
            await fs.mkdir(dir, { recursive: true })
        } catch (error) {
            // Directory might already exist, ignore error
        }
    }

    /**
     * Find files in the storage using a file pattern, as well as a custom filter function.
     */
    async* find(
        filePattern: RegExp,
        baseDir?: string,
        fileFilter?: Record<string, any>,
        maxCount: number = -1
    ): AsyncIterableIterator<[string, Record<string, any>]> {
        const itemFilter = (item: Record<string, any>): boolean => {
            if (!fileFilter) return true
            return Object.entries(fileFilter).every(([key, value]) => {
                const regex = new RegExp(value)
                return regex.test(item[key])
            })
        }

        const searchPath = path.join(this._rootDir, baseDir || "")
        console.log(`Searching ${searchPath} for files matching ${filePattern.source}`)
        
        const allFiles = await this._getAllFiles(searchPath)
        let numLoaded = 0
        let numFiltered = 0
        const numTotal = allFiles.length

        for (const file of allFiles) {
            const match = filePattern.exec(file)
            if (match && match.groups) {
                const group = match.groups
                if (itemFilter(group)) {
                    let filename = file.replace(this._rootDir, "")
                    if (filename.startsWith(path.sep)) {
                        filename = filename.substring(1)
                    }
                    yield [filename, group]
                    numLoaded++
                    if (maxCount > 0 && numLoaded >= maxCount) {
                        break
                    }
                } else {
                    numFiltered++
                }
            } else {
                numFiltered++
            }
            
            console.debug(`Files loaded: ${numLoaded}, filtered: ${numFiltered}, total: ${numTotal}`)
        }
    }

    private async _getAllFiles(dir: string): Promise<string[]> {
        const files: string[] = []
        
        try {
            const entries = await fs.readdir(dir, { withFileTypes: true })
            
            for (const entry of entries) {
                const fullPath = path.join(dir, entry.name)
                if (entry.isDirectory()) {
                    const subFiles = await this._getAllFiles(fullPath)
                    files.push(...subFiles)
                } else {
                    files.push(fullPath)
                }
            }
        } catch (error) {
            // Directory might not exist, return empty array
        }
        
        return files
    }

    /**
     * Get method definition.
     */
    async get(
        key: string, 
        asBytes: boolean = false, 
        encoding?: string
    ): Promise<any> {
        const filePath = this._joinPath(this._rootDir, key)

        if (await this.has(key)) {
            return await this._readFile(filePath, asBytes, encoding)
        }
        
        // Check if key is a direct file path
        try {
            await fs.access(key)
            return await this._readFile(key, asBytes, encoding)
        } catch {
            return null
        }
    }

    private async _readFile(
        filePath: string,
        asBytes: boolean = false,
        encoding?: string
    ): Promise<any> {
        const options: any = {}
        if (!asBytes) {
            options.encoding = encoding || this._encoding
        }

        return await fs.readFile(filePath, options)
    }

    /**
     * Set method definition.
     */
    async set(
        key: string, 
        value: any, 
        encoding?: string
    ): Promise<void> {
        const filePath = this._joinPath(this._rootDir, key)
        
        // Ensure parent directory exists
        await this._ensureDirectory(path.dirname(filePath))
        
        const options: any = {}
        if (typeof value === 'string') {
            options.encoding = encoding || this._encoding
        }

        await fs.writeFile(filePath, value, options)
    }

    /**
     * Has method definition.
     */
    async has(key: string): Promise<boolean> {
        try {
            await fs.access(this._joinPath(this._rootDir, key))
            return true
        } catch {
            return false
        }
    }

    /**
     * Delete method definition.
     */
    async delete(key: string): Promise<void> {
        if (await this.has(key)) {
            await fs.unlink(this._joinPath(this._rootDir, key))
        }
    }

    /**
     * Clear method definition.
     */
    async clear(): Promise<void> {
        try {
            const entries = await fs.readdir(this._rootDir, { withFileTypes: true })
            
            for (const entry of entries) {
                const fullPath = path.join(this._rootDir, entry.name)
                if (entry.isDirectory()) {
                    await fs.rmdir(fullPath, { recursive: true })
                } else {
                    await fs.unlink(fullPath)
                }
            }
        } catch (error) {
            // Directory might not exist, ignore error
        }
    }

    /**
     * Create a child storage instance.
     */
    child(name?: string): PipelineStorage {
        if (!name) {
            return this
        }
        return new FilePipelineStorage(path.join(this._rootDir, name))
    }

    /**
     * Return the keys in the storage.
     */
    keys(): string[] {
        try {
            const entries = require('fs').readdirSync(this._rootDir, { withFileTypes: true })
            return entries
                .filter((entry: any) => entry.isFile())
                .map((entry: any) => entry.name)
        } catch {
            return []
        }
    }

    /**
     * Get the creation date of a file.
     */
    async getCreationDate(key: string): Promise<string> {
        const filePath = this._joinPath(this._rootDir, key)
        
        try {
            const stats = await fs.stat(filePath)
            const creationTime = new Date(stats.birthtime)
            return getTimestampFormattedWithLocalTz(creationTime)
        } catch (error) {
            throw new Error(`Failed to get creation date for ${key}: ${error}`)
        }
    }

    private _joinPath(filePath: string, fileName: string): string {
        return path.join(filePath, fileName)
    }
}

/**
 * Create a file based storage.
 */
export function createFileStorage(kwargs: Record<string, any>): PipelineStorage {
    const baseDir = kwargs.base_dir
    console.log(`Creating file storage at ${baseDir}`)
    return new FilePipelineStorage(baseDir)
}