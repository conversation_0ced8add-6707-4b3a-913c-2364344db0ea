/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'GraphExtractionResult' and 'GraphExtractor' models.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Graph } from '../../utils/graphs.js';
import { ErrorHandlerFn } from '../../typing/error_handler.js';
import { ChatModel } from '../../../language_model/protocol/base.js';
import { cleanStr } from '../../utils/string.js';

const DEFAULT_TUPLE_DELIMITER = "<|>";
const DEFAULT_RECORD_DELIMITER = "##";
const DEFAULT_COMPLETION_DELIMITER = "<|COMPLETE|>";
const DEFAULT_ENTITY_TYPES = ["organization", "person", "geo", "event"];

const logger = console;

/**
 * Unipartite graph extraction result class definition.
 * Matches the Python dataclass structure exactly.
 */
export interface GraphExtractionResult {
    output: Graph;
    source_docs: Record<any, any>;
}

/**
 * Unipartite graph extractor class definition.
 * Matches the Python class structure exactly.
 */
export class GraphExtractor {
    private _model: ChatModel;
    private _join_descriptions: boolean;
    private _tuple_delimiter_key: string;
    private _record_delimiter_key: string;
    private _entity_types_key: string;
    private _input_text_key: string;
    private _completion_delimiter_key: string;
    private _entity_name_key: string;
    private _input_descriptions_key: string;
    private _extraction_prompt: string;
    private _summarization_prompt: string;
    private _max_gleanings: number;
    private _on_error: ErrorHandlerFn;

    constructor(
        model_invoker: ChatModel,
        tuple_delimiter_key?: string | null,
        record_delimiter_key?: string | null,
        input_text_key?: string | null,
        entity_types_key?: string | null,
        completion_delimiter_key?: string | null,
        prompt?: string | null,
        join_descriptions: boolean = true,
        max_gleanings?: number | null,
        on_error?: ErrorHandlerFn | null
    ) {
        // Python: Init method definition
        // TODO: streamline construction
        this._model = model_invoker;
        this._join_descriptions = join_descriptions;
        this._input_text_key = input_text_key || "input_text";
        this._tuple_delimiter_key = tuple_delimiter_key || "tuple_delimiter";
        this._record_delimiter_key = record_delimiter_key || "record_delimiter";
        this._completion_delimiter_key = completion_delimiter_key || "completion_delimiter";
        this._entity_types_key = entity_types_key || "entity_types";
        this._extraction_prompt = prompt || "GRAPH_EXTRACTION_PROMPT"; // TODO: Import actual prompt
        this._max_gleanings = max_gleanings !== null && max_gleanings !== undefined ? max_gleanings : 1; // TODO: Use actual default
        this._on_error = on_error || ((e: Error | null, s: string | null, d: any) => {});
        
        // Initialize other properties
        this._entity_name_key = "entity_name";
        this._input_descriptions_key = "input_descriptions";
        this._summarization_prompt = "SUMMARIZATION_PROMPT"; // TODO: Import actual prompt
    }

    /**
     * Call method definition.
     * Matches the Python __call__ method exactly.
     */
    async call(
        texts: string[],
        prompt_variables?: Record<string, any> | null
    ): Promise<GraphExtractionResult> {
        if (prompt_variables === null || prompt_variables === undefined) {
            prompt_variables = {};
        }

        const all_records: Record<number, string> = {};
        const source_doc_map: Record<number, string> = {};

        // Wire defaults into the prompt variables
        prompt_variables = {
            ...prompt_variables,
            [this._tuple_delimiter_key]: prompt_variables[this._tuple_delimiter_key] || DEFAULT_TUPLE_DELIMITER,
            [this._record_delimiter_key]: prompt_variables[this._record_delimiter_key] || DEFAULT_RECORD_DELIMITER,
            [this._completion_delimiter_key]: prompt_variables[this._completion_delimiter_key] || DEFAULT_COMPLETION_DELIMITER,
            [this._entity_types_key]: (prompt_variables[this._entity_types_key] || DEFAULT_ENTITY_TYPES).join(","),
        };

        for (let doc_index = 0; doc_index < texts.length; doc_index++) {
            const text = texts[doc_index];
            try {
                // Invoke the entity extraction
                const result = await this._process_document(text, prompt_variables);
                source_doc_map[doc_index] = text;
                all_records[doc_index] = result;
            } catch (e) {
                const error = e instanceof Error ? e : new Error(String(e));
                logger.error("error extracting graph", error);
                this._on_error(
                    error,
                    error.stack || '',
                    {
                        doc_index: doc_index,
                        text: text,
                    }
                );
            }
        }

        const output = await this._process_results(
            all_records,
            prompt_variables[this._tuple_delimiter_key] || DEFAULT_TUPLE_DELIMITER,
            prompt_variables[this._record_delimiter_key] || DEFAULT_RECORD_DELIMITER
        );

        return {
            output: output,
            source_docs: source_doc_map,
        };
    }

    /**
     * Process a single document.
     * Matches the Python _process_document method exactly.
     */
    private async _process_document(
        text: string,
        prompt_variables: Record<string, string>
    ): Promise<string> {
        const response = await this._model.achat(
            this._extraction_prompt.replace(/\{([^}]+)\}/g, (match, key) => {
                if (key === this._input_text_key) {
                    return text;
                }
                return prompt_variables[key] || match;
            })
        );
        
        let results = response.output?.content || "";

        // Python: if gleanings are specified, enter a loop to extract more entities
        if (this._max_gleanings > 0) {
            for (let i = 0; i < this._max_gleanings; i++) {
                const continuation_response = await this._model.achat(
                    "CONTINUE_PROMPT", // TODO: Import actual prompt
                    response.history || []
                );
                results += continuation_response.output?.content || "";

                // if this is the final glean, don't bother updating the continuation flag
                if (i >= this._max_gleanings - 1) {
                    break;
                }

                const loop_response = await this._model.achat(
                    "LOOP_PROMPT", // TODO: Import actual prompt
                    continuation_response.history || []
                );
                
                if (loop_response.output?.content !== "Y") {
                    break;
                }
            }
        }

        return results;
    }

    /**
     * Parse the result string to create an undirected unipartite graph.
     * Matches the Python _process_results method exactly.
     */
    private async _process_results(
        results: Record<number, string>,
        tuple_delimiter: string,
        record_delimiter: string
    ): Promise<Graph> {
        /**
         * Args:
         *     - results - dict of results from the extraction chain
         *     - tuple_delimiter - delimiter between tuples in an output record, default is '<|>'
         *     - record_delimiter - delimiter between records, default is '##'
         * Returns:
         *     - output - unipartite graph in graphML format
         */
        const graph: Graph = {
            nodes: new Map(),
            edges: new Map()
        };
        
        for (const [source_doc_id, extracted_data] of Object.entries(results)) {
            const source_doc_id_num = parseInt(source_doc_id);
            const records = extracted_data.split(record_delimiter).map(r => r.trim());

            for (let record of records) {
                record = record.replace(/^\(|\)$/g, "").trim();
                const record_attributes = record.split(tuple_delimiter);

                if (record_attributes[0] === '"entity"' && record_attributes.length >= 4) {
                    // add this record as a node in the G
                    const entity_name = cleanStr(record_attributes[1].toUpperCase());
                    const entity_type = cleanStr(record_attributes[2].toUpperCase());
                    const entity_description = cleanStr(record_attributes[3]);

                    if (graph.nodes.has(entity_name)) {
                        const node = graph.nodes.get(entity_name)!;
                        if (this._join_descriptions) {
                            const existing_descriptions = _unpack_descriptions(node);
                            const unique_descriptions = Array.from(new Set([
                                ...existing_descriptions,
                                entity_description
                            ]));
                            node.description = unique_descriptions.join("\n");
                        } else {
                            if (entity_description.length > (node.description || "").length) {
                                node.description = entity_description;
                            }
                        }
                        const existing_source_ids = _unpack_source_ids(node);
                        const unique_source_ids = Array.from(new Set([
                            ...existing_source_ids,
                            String(source_doc_id_num)
                        ]));
                        node.source_id = unique_source_ids.join(", ");
                        node.type = entity_type !== "" ? entity_type : node.type;
                    } else {
                        graph.nodes.set(entity_name, {
                            type: entity_type,
                            description: entity_description,
                            source_id: String(source_doc_id_num),
                        });
                    }
                }

                if (record_attributes[0] === '"relationship"' && record_attributes.length >= 5) {
                    // add this record as edge
                    const source = cleanStr(record_attributes[1].toUpperCase());
                    const target = cleanStr(record_attributes[2].toUpperCase());
                    let edge_description = cleanStr(record_attributes[3]);
                    let edge_source_id = cleanStr(String(source_doc_id_num));
                    let weight: number;
                    try {
                        weight = parseFloat(record_attributes[record_attributes.length - 1]);
                    } catch {
                        weight = 1.0;
                    }

                    if (!graph.nodes.has(source)) {
                        graph.nodes.set(source, {
                            type: "",
                            description: "",
                            source_id: edge_source_id,
                        });
                    }
                    if (!graph.nodes.has(target)) {
                        graph.nodes.set(target, {
                            type: "",
                            description: "",
                            source_id: edge_source_id,
                        });
                    }

                    const edge_key = `${source}|${target}`;
                    if (graph.edges.has(edge_key)) {
                        const edge_data = graph.edges.get(edge_key)!;
                        if (edge_data.data) {
                            weight += edge_data.weight || 0;
                            if (this._join_descriptions) {
                                const existing_descriptions = _unpack_descriptions(edge_data.data);
                                const unique_descriptions = Array.from(new Set([
                                    ...existing_descriptions,
                                    edge_description
                                ]));
                                edge_description = unique_descriptions.join("\n");
                            }
                            const existing_source_ids = _unpack_source_ids(edge_data.data);
                            const unique_source_ids = Array.from(new Set([
                                ...existing_source_ids,
                                String(source_doc_id_num)
                            ]));
                            edge_source_id = unique_source_ids.join(", ");
                        }
                    }

                    graph.edges.set(edge_key, {
                        source,
                        target,
                        weight: weight,
                        data: {
                            description: edge_description,
                            source_id: edge_source_id,
                        }
                    });
                }
            }
        }

        return graph;
    }
}

/**
 * Unpack descriptions from data.
 * Matches the Python _unpack_descriptions function exactly.
 */
function _unpack_descriptions(data: Record<string, any>): string[] {
    const value = data.description;
    return value == null ? [] : value.split("\n");
}

/**
 * Unpack source IDs from data.
 * Matches the Python _unpack_source_ids function exactly.
 */
function _unpack_source_ids(data: Record<string, any>): string[] {
    const value = data.source_id;
    return value == null ? [] : value.split(", ");
}
