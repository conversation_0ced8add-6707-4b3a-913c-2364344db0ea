/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Collection of graph utility functions.
 */

import { DataFrame } from '../../data-model/types';
import { ModularityMetric } from '../../config/enums';

const logger = console;

// Simple graph representation for TypeScript
export interface Graph {
    nodes: Map<string, any>;
    edges: Map<string, { source: string; target: string; weight?: number; data?: any }>;
}

/**
 * Calculate modularity of the graph based on the modularity metric type.
 * Note: This is a simplified implementation. In production, you would use a proper graph library.
 */
export function calculateModularity(
    graph: Graph,
    maxClusterSize: number = 10,
    randomSeed: number = 0xDEADBEEF,
    useRootModularity: boolean = true,
    modularityMetric: ModularityMetric = ModularityMetric.WeightedComponents
): number {
    switch (modularityMetric) {
        case ModularityMetric.Graph:
            logger.info("Calculating graph modularity");
            return calculateGraphModularity(graph, maxClusterSize, randomSeed, useRootModularity);
        case ModularityMetric.LCC:
            logger.info("Calculating LCC modularity");
            return calculateLccModularity(graph, maxClusterSize, randomSeed, useRootModularity);
        case ModularityMetric.WeightedComponents:
            logger.info("Calculating weighted-components modularity");
            return calculateWeightedModularity(graph, maxClusterSize, randomSeed, useRootModularity);
        default:
            throw new Error(`Unknown modularity metric type: ${modularityMetric}`);
    }
}

/**
 * Calculate modularity of the whole graph.
 */
function calculateGraphModularity(
    graph: Graph,
    maxClusterSize: number,
    randomSeed: number,
    useRootModularity: boolean
): number {
    // Simplified modularity calculation
    // In a real implementation, you would use hierarchical Leiden clustering
    return 0.5; // Placeholder value
}

/**
 * Calculate modularity of the largest connected component of the graph.
 */
function calculateLccModularity(
    graph: Graph,
    maxClusterSize: number,
    randomSeed: number,
    useRootModularity: boolean
): number {
    // Find largest connected component and calculate its modularity
    // Simplified implementation
    return 0.4; // Placeholder value
}

/**
 * Calculate weighted modularity of all connected components.
 */
function calculateWeightedModularity(
    graph: Graph,
    maxClusterSize: number,
    randomSeed: number,
    useRootModularity: boolean,
    minConnectedComponentSize: number = 10
): number {
    // Calculate weighted modularity across components
    // Simplified implementation
    return 0.6; // Placeholder value
}

/**
 * Calculate pointwise mutual information (PMI) edge weights.
 */
export function calculatePmiEdgeWeights(
    nodesDF: DataFrame,
    edgesDF: DataFrame,
    nodeNameCol: string = "title",
    nodeFreqCol: string = "frequency",
    edgeWeightCol: string = "weight",
    edgeSourceCol: string = "source",
    edgeTargetCol: string = "target"
): DataFrame {
    // Create a copy of nodes data
    const nodeData = nodesDF.data.map(row => ({
        [nodeNameCol]: row[nodeNameCol],
        [nodeFreqCol]: row[nodeFreqCol]
    }));

    const totalEdgeWeights = edgesDF.data.reduce((sum, row) => sum + (row[edgeWeightCol] || 0), 0);
    const totalFreqOccurrences = nodeData.reduce((sum, row) => sum + (row[nodeFreqCol] || 0), 0);

    // Calculate proportional occurrences
    const nodeProportions = new Map<string, number>();
    nodeData.forEach(row => {
        const propOccurrence = (row[nodeFreqCol] || 0) / totalFreqOccurrences;
        nodeProportions.set(row[nodeNameCol], propOccurrence);
    });

    // Calculate PMI weights
    const newEdgeData = edgesDF.data.map(row => {
        const propWeight = (row[edgeWeightCol] || 0) / totalEdgeWeights;
        const sourceProp = nodeProportions.get(row[edgeSourceCol]) || 0;
        const targetProp = nodeProportions.get(row[edgeTargetCol]) || 0;
        
        const pmiWeight = propWeight * Math.log2(propWeight / (sourceProp * targetProp));
        
        return {
            ...row,
            [edgeWeightCol]: pmiWeight
        };
    });

    return {
        columns: edgesDF.columns,
        data: newEdgeData
    };
}

/**
 * Calculate reciprocal rank fusion (RRF) edge weights.
 */
export function calculateRrfEdgeWeights(
    nodesDF: DataFrame,
    edgesDF: DataFrame,
    nodeNameCol: string = "title",
    nodeFreqCol: string = "freq",
    edgeWeightCol: string = "weight",
    edgeSourceCol: string = "source",
    edgeTargetCol: string = "target",
    rrfSmoothingFactor: number = 60
): DataFrame {
    // First calculate PMI weights
    let result = calculatePmiEdgeWeights(
        nodesDF, edgesDF, nodeNameCol, nodeFreqCol, 
        edgeWeightCol, edgeSourceCol, edgeTargetCol
    );

    // Calculate ranks
    const weights = result.data.map(row => row[edgeWeightCol] || 0);
    const sortedIndices = weights
        .map((weight, index) => ({ weight, index }))
        .sort((a, b) => b.weight - a.weight)
        .map((item, rank) => ({ index: item.index, rank: rank + 1 }));

    const ranks = new Array(weights.length);
    sortedIndices.forEach(item => {
        ranks[item.index] = item.rank;
    });

    // Apply RRF formula
    result.data = result.data.map((row, index) => {
        const pmiRank = ranks[index];
        const rawWeightRank = ranks[index]; // Simplified - same as PMI rank
        const rrfWeight = (1 / (rrfSmoothingFactor + pmiRank)) + 
                         (1 / (rrfSmoothingFactor + rawWeightRank));
        
        return {
            ...row,
            [edgeWeightCol]: rrfWeight
        };
    });

    return result;
}

/**
 * Get upper threshold by standard deviation.
 */
export function getUpperThresholdByStd(data: number[], stdTrim: number): number {
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    const std = Math.sqrt(variance);
    return mean + stdTrim * std;
}