/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A module containing config enums.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * The cache configuration type for the pipeline.
 */
export enum CacheType {
    /** The file cache configuration type. */
    FILE = "file",
    /** The memory cache configuration type. */
    MEMORY = "memory",
    /** The none cache configuration type. */
    NONE = "none",
    /** The blob cache configuration type. */
    BLOB = "blob",
    /** The cosmosdb cache configuration type */
    COSMOSDB = "cosmosdb"
}

/**
 * The input file type for the pipeline.
 */
export enum InputFileType {
    /** The CSV input type. */
    CSV = "csv",
    /** The text input type. */
    TEXT = "text",
    /** The JSON input type. */
    JSON = "json"
}

/**
 * The output type for the pipeline.
 */
export enum StorageType {
    /** The file output type. */
    FILE = "file",
    /** The memory output type. */
    MEMORY = "memory",
    /** The blob output type. */
    BLOB = "blob",
    /** The cosmosdb output type */
    COSMOSDB = "cosmosdb"
}

/**
 * The reporting configuration type for the pipeline.
 */
export enum ReportingType {
    /** The file reporting configuration type. */
    FILE = "file",
    /** The blob reporting configuration type. */
    BLOB = "blob"
}

/**
 * LLMType enum class definition.
 */
export enum ModelType {
    // Embeddings
    OPENAI_EMBEDDING = "openai_embedding",
    AZURE_OPENAI_EMBEDDING = "azure_openai_embedding",

    // Chat Completion
    OPENAI_CHAT = "openai_chat",
    AZURE_OPENAI_CHAT = "azure_openai_chat",

    // Debug
    MOCK_CHAT = "mock_chat",
    MOCK_EMBEDDING = "mock_embedding"
}

/**
 * AuthType enum class definition.
 */
export enum AuthType {
    API_KEY = "api_key",
    AZURE_MANAGED_IDENTITY = "azure_managed_identity"
}

/**
 * Enum for the type of async to use.
 */
export enum AsyncType {
    ASYNCIO = "asyncio",
    THREADED = "threaded"
}

/**
 * ChunkStrategy class definition.
 */
export enum ChunkStrategyType {
    TOKENS = "tokens",
    SENTENCE = "sentence"
}

/**
 * The type of search to run.
 */
export enum SearchMethod {
    LOCAL = "local",
    GLOBAL = "global",
    DRIFT = "drift",
    BASIC = "basic"
}

/**
 * Enum for the type of indexing to perform.
 */
export enum IndexingMethod {
    /** Traditional GraphRAG indexing, with all graph construction and summarization performed by a language model. */
    STANDARD = "standard",
    /** Fast indexing, using NLP for graph construction and language model for summarization. */
    FAST = "fast",
    /** Incremental update with standard indexing. */
    STANDARD_UPDATE = "standard-update",
    /** Incremental update with fast indexing. */
    FAST_UPDATE = "fast-update"
}

/**
 * Enum for the noun phrase extractor options.
 */
export enum NounPhraseExtractorType {
    /** Standard extractor using regex. Fastest, but limited to English. */
    REGEX_ENGLISH = "regex_english",
    /** Noun phrase extractor based on dependency parsing and NER using SpaCy. */
    SYNTACTIC = "syntactic_parser",
    /** Noun phrase extractor combining CFG-based noun-chunk extraction and NER. */
    CFG = "cfg"
}

/**
 * Enum for the modularity metric to use.
 */
export enum ModularityMetric {
    /** Graph modularity metric. */
    GRAPH = "graph",
    LCC = "lcc",
    /** Weighted components modularity metric. */
    WEIGHTED_COMPONENTS = "weighted_components"
}