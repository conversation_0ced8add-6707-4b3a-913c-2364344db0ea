// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * CLI implementation of the index subcommand.
 */

import * as path from 'path';
import * as process from 'process';
import * as api from '../api';
import { CacheType, IndexingMethod, ReportingType } from '../config/enums';
import { loadConfig } from '../config/load_config';
import { validateConfigNames } from '../index/validate_config';
import { redact } from '../utils/cli';
import { initLoggers } from '../logger/standard_logging';

// Create logger
const logger = console; // Simplified logging for TypeScript

/**
 * Register signal handlers for graceful shutdown
 */
function _register_signal_handlers(): void {
    const handleSignal = (signal: string) => {
        logger.debug(`Received signal ${signal}, exiting...`);
        // In Node.js, we don't have asyncio.all_tasks(), but we can handle cleanup differently
        logger.debug("Handling graceful shutdown...");
        process.exit(0);
    };

    // Register signal handlers for SIGINT and SIGHUP
    process.on('SIGINT', () => handleSignal('SIGINT'));
    
    if (process.platform !== 'win32') {
        process.on('SIGHUP', () => handleSignal('SIGHUP'));
    }
}

/**
 * CLI interface for index command
 */
export interface IndexCliOptions {
    root_dir: string;
    method: IndexingMethod;
    verbose: boolean;
    memprofile: boolean;
    cache: boolean;
    config_filepath?: string;
    dry_run: boolean;
    skip_validation: boolean;
    output_dir?: string;
}

/**
 * Run the indexing pipeline with the given config
 */
export async function index_cli(options: IndexCliOptions): Promise<void> {
    const cli_overrides: Record<string, any> = {};

    if (options.output_dir) {
        cli_overrides["output.base_dir"] = options.output_dir;
        cli_overrides["reporting.base_dir"] = options.output_dir;
        cli_overrides["update_index_output.base_dir"] = options.output_dir;
    }

    const config = await loadConfig(options.root_dir, options.config_filepath, cli_overrides);

    await _run_index({
        config,
        method: options.method,
        is_update_run: false,
        verbose: options.verbose,
        memprofile: options.memprofile,
        cache: options.cache,
        dry_run: options.dry_run,
        skip_validation: options.skip_validation,
    });
}

/**
 * CLI interface for update command
 */
export interface UpdateCliOptions {
    root_dir: string;
    method: IndexingMethod;
    verbose: boolean;
    memprofile: boolean;
    cache: boolean;
    config_filepath?: string;
    skip_validation: boolean;
    output_dir?: string;
}

/**
 * Run the update pipeline with the given config
 */
export async function update_cli(options: UpdateCliOptions): Promise<void> {
    const cli_overrides: Record<string, any> = {};

    if (options.output_dir) {
        cli_overrides["output.base_dir"] = options.output_dir;
        cli_overrides["reporting.base_dir"] = options.output_dir;
        cli_overrides["update_index_output.base_dir"] = options.output_dir;
    }

    const config = await loadConfig(options.root_dir, options.config_filepath, cli_overrides);

    await _run_index({
        config,
        method: options.method,
        is_update_run: true,
        verbose: options.verbose,
        memprofile: options.memprofile,
        cache: options.cache,
        dry_run: false,
        skip_validation: options.skip_validation,
    });
}

/**
 * Internal interface for run index options
 */
interface RunIndexOptions {
    config: any;
    method: IndexingMethod;
    is_update_run: boolean;
    verbose: boolean;
    memprofile: boolean;
    cache: boolean;
    dry_run: boolean;
    skip_validation: boolean;
}

/**
 * Internal function to run the indexing pipeline
 */
async function _run_index(options: RunIndexOptions): Promise<void> {
    const {
        config,
        method,
        is_update_run,
        verbose,
        memprofile,
        cache,
        dry_run,
        skip_validation,
    } = options;

    // Initialize loggers and reporting config
    await initLoggers({
        config,
        rootDir: config.root_dir || undefined,
        verbose,
    });

    if (!cache) {
        config.cache.type = CacheType.NONE;
    }

    // Log the configuration details
    if (config.reporting.type === ReportingType.FILE) {
        const logDir = path.join(config.root_dir || "", config.reporting.base_dir || "");
        const logPath = path.join(logDir, "logs.txt");
        logger.info(`Logging enabled at ${logPath}`);
    } else {
        logger.info(`Logging not enabled for config ${redact(config)}`);
    }

    if (!skip_validation) {
        validateConfigNames(config);
    }

    logger.info(`Starting pipeline run. Dry run: ${dry_run}`);
    logger.info(`Using configuration: ${redact(config)}`);

    if (dry_run) {
        logger.info("Dry run complete, exiting...");
        process.exit(0);
    }

    _register_signal_handlers();

    try {
        const outputs = await api.buildIndex({
            config,
            method,
            isUpdateRun: is_update_run,
            memoryProfile: memprofile,
        });

        const encounteredErrors = outputs.some(
            output => output.errors && output.errors.length > 0
        );

        if (encounteredErrors) {
            logger.error("Errors occurred during the pipeline run, see logs for more details.");
            process.exit(1);
        } else {
            logger.info("All workflows completed successfully.");
            process.exit(0);
        }
    } catch (error) {
        logger.error("Pipeline execution failed:", error);
        process.exit(1);
    }
}
