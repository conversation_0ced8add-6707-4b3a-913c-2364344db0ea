// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the reporting configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';
import { ReportingType } from '../enums.js';

/**
 * The configuration section for Reporting.
 */
export interface ReportingConfig {
  /**
   * The reporting type to use.
   */
  type: ReportingType;

  /**
   * The base directory for reporting.
   */
  baseDir: string;

  /**
   * The reporting connection string to use.
   */
  connectionString?: string;

  /**
   * The reporting container name to use.
   */
  containerName?: string;

  /**
   * The storage account blob url to use.
   */
  storageAccountBlobUrl?: string;
}

/**
 * Create a ReportingConfig with default values.
 */
export function createReportingConfig(config: Partial<ReportingConfig> = {}): ReportingConfig {
  return {
    type: config.type ?? graphragConfigDefaults.reporting.type,
    baseDir: config.baseDir ?? graphragConfigDefaults.reporting.baseDir,
    connectionString: config.connectionString ?? graphragConfigDefaults.reporting.connectionString,
    containerName: config.containerName ?? graphragConfigDefaults.reporting.containerName,
    storageAccountBlobUrl: config.storageAccountBlobUrl ?? graphragConfigDefaults.reporting.storageAccountBlobUrl,
  };
}
