// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the storage configuration.
 */

import { resolve } from 'path';
import { graphragConfigDefaults } from '../defaults.js';
import { StorageType } from '../enums.js';

/**
 * The configuration section for storage.
 */
export interface StorageConfig {
  /**
   * The storage type to use.
   */
  type: StorageType;

  /**
   * The base directory for the output.
   */
  baseDir: string;

  /**
   * The storage connection string to use.
   */
  connectionString?: string;

  /**
   * The storage container name to use.
   */
  containerName?: string;

  /**
   * The storage account blob url to use.
   */
  storageAccountBlobUrl?: string;

  /**
   * The cosmosdb account url to use.
   */
  cosmosdbAccountUrl?: string;
}

/**
 * Validate the base directory for filesystem paths when using local storage.
 */
export function validateBaseDir(baseDir: string, storageType: StorageType): string {
  if (storageType !== StorageType.File) {
    return baseDir;
  }
  return resolve(baseDir);
}

/**
 * Create a StorageConfig with default values.
 */
export function createStorageConfig(config: Partial<StorageConfig> = {}): StorageConfig {
  const type = config.type ?? graphragConfigDefaults.storage.type;
  const baseDir = config.baseDir ?? graphragConfigDefaults.storage.baseDir;
  
  return {
    type,
    baseDir: validateBaseDir(baseDir, type),
    connectionString: config.connectionString ?? graphragConfigDefaults.storage.connectionString,
    containerName: config.containerName ?? graphragConfigDefaults.storage.containerName,
    storageAccountBlobUrl: config.storageAccountBlobUrl ?? graphragConfigDefaults.storage.storageAccountBlobUrl,
    cosmosdbAccountUrl: config.cosmosdbAccountUrl ?? graphragConfigDefaults.storage.cosmosdbAccountUrl,
  };
}
