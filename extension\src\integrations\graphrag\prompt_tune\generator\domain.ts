// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Domain generation for GraphRAG prompts.
 */

import { ChatModel } from '../../language_model/protocol/base';
import { GENERATE_DOMAIN_PROMPT } from '../prompt/domain';

/**
 * Generate an LLM persona to use for GraphRAG prompts.
 * 
 * @param model - The LLM to use for generation
 * @param docs - The domain to generate a persona for
 * @returns The generated domain prompt response
 */
export async function generateDomain(model: ChatModel, docs: string | string[]): Promise<string> {
    const docsStr = Array.isArray(docs) ? docs.join(' ') : docs;
    const domainPrompt = GENERATE_DOMAIN_PROMPT.replace('{input_text}', docsStr);

    const response = await model.achat(domainPrompt);

    return response.output.content || '';
}
