/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing embed_graph and run_embeddings methods definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Graph } from '../../utils/graphs.js';
import { EmbedGraphConfig } from '../../../config/models/embed_graph_config.js';
import { stableLargestConnectedComponent } from '../../utils/stable_lcc.js';
import { embedNode2vec } from './embed_node2vec.js';
import { NodeEmbeddings } from './typing.js';

/**
 * Embed a graph into a vector space using node2vec.
 * The graph is expected to be in Graph format. The operation outputs a mapping between node name and vector.
 * 
 * ## Usage
 * ```yaml
 * dimensions: 1536 # Optional, The number of dimensions to use for the embedding, default: 1536
 * num_walks: 10 # Optional, The number of walks to use for the embedding, default: 10
 * walk_length: 40 # Optional, The walk length to use for the embedding, default: 40
 * window_size: 2 # Optional, The window size to use for the embedding, default: 2
 * iterations: 3 # Optional, The number of iterations to use for the embedding, default: 3
 * random_seed: 86 # Optional, The random seed to use for the embedding, default: 86
 * ```
 */
export function embedGraph(
    graph: Graph,
    config: EmbedGraphConfig
): NodeEmbeddings {
    let workingGraph = graph;

    if (config.useLcc) {
        workingGraph = stableLargestConnectedComponent(graph);
    }

    // Create graph embedding using node2vec
    const embeddings = embedNode2vec(
        workingGraph,
        config.dimensions,
        config.numWalks,
        config.walkLength,
        config.windowSize,
        config.iterations,
        config.randomSeed
    );

    // Create pairs of nodes and embeddings, then sort by node name
    // This matches the Python version: zip(embeddings.nodes, embeddings.embeddings.tolist(), strict=True)
    const pairs: Array<[string, number[]]> = embeddings.nodes.map((node, index) => [
        node,
        embeddings.embeddings[index]
    ]);

    // Sort pairs by node name (key=lambda x: x[0])
    const sortedPairs = pairs.sort((a, b) => a[0].localeCompare(b[0]));

    // Convert to dictionary format: dict(sorted_pairs)
    const result: NodeEmbeddings = {};
    for (const [node, embedding] of sortedPairs) {
        result[node] = embedding;
    }

    return result;
}