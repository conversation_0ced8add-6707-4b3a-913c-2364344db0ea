/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A package containing the 'Relationship' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Identified } from './identified'

/**
 * A relationship between two entities. This is a generic relationship, and can be used to represent any type of relationship between any two entities.
 */
export interface Relationship extends Identified {
    /** The source entity name. */
    source: string

    /** The target entity name. */
    target: string

    /** The edge weight. */
    weight?: number

    /** A description of the relationship (optional). */
    description?: string

    /** The semantic embedding for the relationship description (optional). */
    description_embedding?: number[]

    /** List of text unit IDs in which the relationship appears (optional). */
    text_unit_ids?: string[]

    /** Rank of the relationship, used for sorting (optional). Higher rank indicates more important relationship. This can be based on centrality or other metrics. */
    rank?: number

    /** Additional attributes associated with the relationship (optional). To be included in the search prompt */
    attributes?: Record<string, any>
}

/**
 * Create a new relationship from the dict data.
 */
export function createRelationshipFromDict(
    d: Record<string, any>,
    options: {
        id_key?: string
        short_id_key?: string
        source_key?: string
        target_key?: string
        description_key?: string
        rank_key?: string
        weight_key?: string
        text_unit_ids_key?: string
        attributes_key?: string
    } = {}
): Relationship {
    const {
        id_key = "id",
        short_id_key = "human_readable_id",
        source_key = "source",
        target_key = "target",
        description_key = "description",
        rank_key = "rank",
        weight_key = "weight",
        text_unit_ids_key = "text_unit_ids",
        attributes_key = "attributes"
    } = options

    return {
        id: d[id_key],
        short_id: d[short_id_key],
        source: d[source_key],
        target: d[target_key],
        rank: d[rank_key] ?? 1,
        description: d[description_key],
        weight: d[weight_key] ?? 1.0,
        text_unit_ids: d[text_unit_ids_key],
        attributes: d[attributes_key]
    }
}