// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Context builders for text units.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../../data_model/types.js';
import * as schemas from '../../../../data_model/schemas.js';
import { buildMixedContext } from '../build_mixed_context.js';
import { prepTextUnits } from './prep_text_units.js';
import { sortContext } from './sort_context.js';
import { numTokens } from '../../../../query/llm/text_utils.js';

/**
 * Helper function to select specific columns from DataFrame.
 */
function selectColumns(df: DataFrame, columns: string[]): DataFrame {
  const data = df.data.map(row => {
    const newRow: Record<string, any> = {};
    columns.forEach(col => {
      newRow[col] = row[col];
    });
    return newRow;
  });
  return { columns, data };
}

/**
 * Helper function to explode a column containing arrays.
 */
function explodeColumn(df: DataFrame, column: string): DataFrame {
  const exploded_data: Record<string, any>[] = [];

  df.data.forEach(row => {
    const values = row[column];
    if (Array.isArray(values)) {
      values.forEach(value => {
        exploded_data.push({
          ...row,
          [column]: value
        });
      });
    } else {
      exploded_data.push(row);
    }
  });

  return { columns: df.columns, data: exploded_data };
}

/**
 * Helper function to merge DataFrames.
 */
function mergeDataFrames(
  left: DataFrame,
  right: DataFrame,
  on: string | string[],
  how: 'left' | 'right' | 'inner' | 'outer' = 'inner'
): DataFrame {
  const onColumns = Array.isArray(on) ? on : [on];
  const allColumns = new Set([...left.columns, ...right.columns]);
  const columns = Array.from(allColumns);

  const merged_data: Record<string, any>[] = [];

  left.data.forEach(leftRow => {
    const matches = right.data.filter(rightRow => {
      return onColumns.every(col => leftRow[col] === rightRow[col]);
    });

    if (matches.length > 0) {
      matches.forEach(rightRow => {
        const mergedRow: Record<string, any> = { ...leftRow, ...rightRow };
        merged_data.push(mergedRow);
      });
    } else if (how === 'left' || how === 'outer') {
      const mergedRow: Record<string, any> = { ...leftRow };
      columns.forEach(col => {
        if (!(col in mergedRow)) {
          mergedRow[col] = null;
        }
      });
      merged_data.push(mergedRow);
    }
  });

  return { columns, data: merged_data };
}

/**
 * Helper function to group by columns and aggregate.
 */
function groupByAndAggregate(
  df: DataFrame,
  groupBy: string | string[],
  agg: Record<string, string>
): DataFrame {
  const groupColumns = Array.isArray(groupBy) ? groupBy : [groupBy];
  const groups = new Map<string, Record<string, any>[]>();

  df.data.forEach(row => {
    const key = groupColumns.map(col => row[col]).join('|');
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key)!.push(row);
  });

  const result_data: Record<string, any>[] = [];
  groups.forEach((rows, key) => {
    const first_row = rows[0];
    const result_row: Record<string, any> = {};

    groupColumns.forEach(col => {
      result_row[col] = first_row[col];
    });

    Object.entries(agg).forEach(([column, operation]) => {
      if (operation === 'collect_list') {
        result_row[column] = rows.map(row => row[column]);
      } else if (operation === 'sum') {
        result_row[column] = rows.reduce((sum, row) => sum + (row[column] || 0), 0);
      } else if (operation === 'first') {
        result_row[column] = first_row[column];
      }
    });

    result_data.push(result_row);
  });

  const result_columns = [...groupColumns, ...Object.keys(agg)];
  return { columns: result_columns, data: result_data };
}

/**
 * Helper function to filter DataFrame rows.
 */
function filterDataFrame(df: DataFrame, predicate: (row: Record<string, any>) => boolean): DataFrame {
  const filtered_data = df.data.filter(predicate);
  return { columns: df.columns, data: filtered_data };
}

/**
 * Helper function to concatenate DataFrames.
 */
function concatDataFrames(dfs: DataFrame[]): DataFrame {
  if (dfs.length === 0) {
    return { columns: [], data: [] };
  }

  const all_columns = new Set<string>();
  dfs.forEach(df => df.columns.forEach(col => all_columns.add(col)));
  const columns = Array.from(all_columns);

  const data: Record<string, any>[] = [];
  dfs.forEach(df => {
    df.data.forEach(row => {
      const new_row: Record<string, any> = {};
      columns.forEach(col => {
        new_row[col] = row[col] !== undefined ? row[col] : null;
      });
      data.push(new_row);
    });
  });

  return { columns, data };
}

/**
 * Prep context data for community report generation using text unit data.
 * Matches the Python build_local_context function exactly.
 *
 * Community membership has columns [COMMUNITY_ID, COMMUNITY_LEVEL, ENTITY_IDS, RELATIONSHIP_IDS, TEXT_UNIT_IDS]
 */
export function build_local_context(
  community_membership_df: DataFrame,
  text_units_df: DataFrame,
  node_df: DataFrame,
  max_context_tokens: number = 16000
): DataFrame {
  // Python: prepped_text_units_df = prep_text_units(text_units_df, node_df)
  let prepped_text_units_df = prepTextUnits(text_units_df, node_df);

  // Python: prepped_text_units_df = prepped_text_units_df.rename(columns={schemas.ID: schemas.TEXT_UNIT_IDS})
  const renamed_data = prepped_text_units_df.data.map(row => ({
    ...row,
    [schemas.TEXT_UNIT_IDS]: row[schemas.ID],
  }));
  prepped_text_units_df = {
    columns: prepped_text_units_df.columns.map(col => col === schemas.ID ? schemas.TEXT_UNIT_IDS : col),
    data: renamed_data
  };

  // Python: context_df = community_membership_df[[schemas.COMMUNITY_ID, schemas.COMMUNITY_LEVEL, schemas.TEXT_UNIT_IDS]]
  let context_df = selectColumns(community_membership_df, [
    schemas.COMMUNITY_ID,
    schemas.COMMUNITY_LEVEL,
    schemas.TEXT_UNIT_IDS
  ]);

  // Python: context_df = context_df.explode(schemas.TEXT_UNIT_IDS)
  context_df = explodeColumn(context_df, schemas.TEXT_UNIT_IDS);

  // Python: context_df = context_df.merge(prepped_text_units_df, on=[schemas.TEXT_UNIT_IDS, schemas.COMMUNITY_ID], how="left")
  context_df = mergeDataFrames(
    context_df,
    prepped_text_units_df,
    [schemas.TEXT_UNIT_IDS, schemas.COMMUNITY_ID],
    'left'
  );

  // Python: context_df[schemas.ALL_CONTEXT] = context_df[schemas.ALL_DETAILS].apply(lambda x: {...})
  context_df.data.forEach(row => {
    const all_details = row[schemas.ALL_DETAILS];
    row[schemas.ALL_CONTEXT] = {
      id: all_details[schemas.SHORT_ID],
      text: all_details[schemas.TEXT],
      entity_degree: all_details[schemas.ENTITY_DEGREE],
    };
  });

  // Python: context_df = context_df.groupby([schemas.COMMUNITY_ID, schemas.COMMUNITY_LEVEL]).agg({schemas.ALL_CONTEXT: list}).reset_index()
  context_df = groupByAndAggregate(
    context_df,
    [schemas.COMMUNITY_ID, schemas.COMMUNITY_LEVEL],
    { [schemas.ALL_CONTEXT]: 'collect_list' }
  );

  // Python: context_df[schemas.CONTEXT_STRING] = context_df[schemas.ALL_CONTEXT].apply(sort_context)
  context_df.data.forEach(row => {
    row[schemas.CONTEXT_STRING] = sortContext(row[schemas.ALL_CONTEXT]);
  });

  // Python: context_df[schemas.CONTEXT_SIZE] = context_df[schemas.CONTEXT_STRING].apply(num_tokens)
  context_df.data.forEach(row => {
    row[schemas.CONTEXT_SIZE] = numTokens(row[schemas.CONTEXT_STRING]);
  });

  // Python: context_df[schemas.CONTEXT_EXCEED_FLAG] = (context_df[schemas.CONTEXT_SIZE] > max_context_tokens)
  context_df.data.forEach(row => {
    row[schemas.CONTEXT_EXCEED_FLAG] = row[schemas.CONTEXT_SIZE] > max_context_tokens;
  });

  // Update columns
  const final_columns = [...context_df.columns];
  if (!final_columns.includes(schemas.CONTEXT_STRING)) {
    final_columns.push(schemas.CONTEXT_STRING);
  }
  if (!final_columns.includes(schemas.CONTEXT_SIZE)) {
    final_columns.push(schemas.CONTEXT_SIZE);
  }
  if (!final_columns.includes(schemas.CONTEXT_EXCEED_FLAG)) {
    final_columns.push(schemas.CONTEXT_EXCEED_FLAG);
  }

  return { columns: final_columns, data: context_df.data };
}
}

/**
 * Prep context for each community in a given level.
 * Matches the Python build_level_context function exactly.
 */
export function build_level_context(
  report_df: DataFrame | undefined,
  community_hierarchy_df: DataFrame,
  local_context_df: DataFrame,
  level: number,
  max_context_tokens: number = 16000
): DataFrame {
  // Python: if report_df is None or report_df.empty:
  if (!report_df || report_df.data.length === 0) {
    // No report to substitute with, just trim the local context
    const level_context_df = filterDataFrame(local_context_df,
      row => row[schemas.COMMUNITY_LEVEL] === level
    );

    const valid_context_df = filterDataFrame(level_context_df,
      row => !row[schemas.CONTEXT_EXCEED_FLAG]
    );
    const invalid_context_df = filterDataFrame(level_context_df,
      row => row[schemas.CONTEXT_EXCEED_FLAG]
    );

    if (invalid_context_df.data.length === 0) {
      return valid_context_df;
    }

    // Python: invalid_context_df[schemas.CONTEXT_STRING] = invalid_context_df[schemas.ALL_CONTEXT].apply(...)
    const updated_invalid_data = invalid_context_df.data.map(row => ({
      ...row,
      [schemas.CONTEXT_STRING]: sortContext(row[schemas.ALL_CONTEXT], undefined, max_context_tokens),
    }));

    updated_invalid_data.forEach(row => {
      row[schemas.CONTEXT_SIZE] = numTokens(row[schemas.CONTEXT_STRING]);
      row[schemas.CONTEXT_EXCEED_FLAG] = false;
    });

    const updated_invalid_df = { columns: invalid_context_df.columns, data: updated_invalid_data };

    return concatDataFrames([valid_context_df, updated_invalid_df]);
  }

  let levelContextDf = localContextDf.filter(
    row => row[schemas.COMMUNITY_LEVEL] === level
  );

  // Exclude those that already have reports
  levelContextDf = levelContextDf.antijoin(
    reportDf.select([schemas.COMMUNITY_ID]),
    schemas.COMMUNITY_ID
  );

  const validContextDf = levelContextDf.filter(
    row => row[schemas.CONTEXT_EXCEED_FLAG] === false
  );
  const invalidContextDf = levelContextDf.filter(
    row => row[schemas.CONTEXT_EXCEED_FLAG] === true
  );

  if (invalidContextDf.isEmpty()) {
    return validContextDf;
  }

  // Try to substitute with sub-community reports
  const subReportDf = reportDf
    .filter(row => row[schemas.COMMUNITY_LEVEL] === level + 1)
    .drop([schemas.COMMUNITY_LEVEL]);

  let subContextDf = localContextDf.filter(
    row => row[schemas.COMMUNITY_LEVEL] === level + 1
  );
  
  subContextDf = subContextDf.merge(subReportDf, schemas.COMMUNITY_ID, 'left');
  subContextDf = subContextDf.rename({
    [schemas.COMMUNITY_ID]: schemas.SUB_COMMUNITY
  });

  // Collect all sub communities' contexts for each community
  let communityDf = communityHierarchyDf
    .filter(row => row[schemas.COMMUNITY_LEVEL] === level)
    .drop([schemas.COMMUNITY_LEVEL]);

  communityDf = communityDf.merge(
    invalidContextDf.select([schemas.COMMUNITY_ID]),
    schemas.COMMUNITY_ID,
    'inner'
  );

  communityDf = communityDf.merge(
    subContextDf.select([
      schemas.SUB_COMMUNITY,
      schemas.FULL_CONTENT,
      schemas.ALL_CONTEXT,
      schemas.CONTEXT_SIZE,
    ]),
    schemas.SUB_COMMUNITY,
    'left'
  );

  communityDf = communityDf.withColumn(
    schemas.ALL_CONTEXT,
    communityDf.toRecords().map(row => ({
      [schemas.SUB_COMMUNITY]: row[schemas.SUB_COMMUNITY],
      [schemas.ALL_CONTEXT]: row[schemas.ALL_CONTEXT],
      [schemas.FULL_CONTENT]: row[schemas.FULL_CONTENT],
      [schemas.CONTEXT_SIZE]: row[schemas.CONTEXT_SIZE],
    }))
  );

  communityDf = communityDf
    .groupBy(schemas.COMMUNITY_ID)
    .agg({ [schemas.ALL_CONTEXT]: 'collect_list' });

  communityDf = communityDf
    .withColumn(
      schemas.CONTEXT_STRING,
      communityDf.getColumn(schemas.ALL_CONTEXT).map(x =>
        buildMixedContext(x, maxContextTokens)
      )
    )
    .withColumn(
      schemas.CONTEXT_SIZE,
      communityDf.getColumn(schemas.CONTEXT_STRING).map(x => numTokens(x))
    )
    .withColumn(schemas.CONTEXT_EXCEED_FLAG, false)
    .withColumn(schemas.COMMUNITY_LEVEL, level);

  // Handle any remaining invalid records
  const remainingDf = invalidContextDf
    .antijoin(communityDf.select([schemas.COMMUNITY_ID]), schemas.COMMUNITY_ID)
    .withColumn(
      schemas.CONTEXT_STRING,
      invalidContextDf.getColumn(schemas.ALL_CONTEXT).map(x =>
        sortContext(x, maxContextTokens)
      )
    )
    .withColumn(
      schemas.CONTEXT_SIZE,
      invalidContextDf.getColumn(schemas.CONTEXT_STRING).map(x => numTokens(x))
    )
    .withColumn(schemas.CONTEXT_EXCEED_FLAG, false);

  return DataFrame.concat([validContextDf, communityDf, remainingDf]);
}
