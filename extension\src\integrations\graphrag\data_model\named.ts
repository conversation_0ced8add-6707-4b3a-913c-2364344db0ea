/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A package containing the 'Named' interface.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Identified } from './identified'

/**
 * An interface for an item with a name/title.
 */
export interface Named extends Identified {
    /** The name/title of the item. */
    title: string
}