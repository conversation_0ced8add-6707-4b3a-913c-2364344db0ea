// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Comprehensive test file for GraphRAG index/input conversion from Python to TypeScript.
 * This file validates that all converted TypeScript code maintains functional parity
 * with the original Python implementation.
 */

import { DataFrame } from '../../data_model/types.js';
import { InputConfig, createInputConfig } from '../../config/models/input_config.js';
import { InputFileType } from '../../config/enums.js';
import { createStorageConfig } from '../../config/models/storage_config.js';
import { PipelineStorage } from '../../storage/pipeline_storage.js';
import { createInput } from './factory.js';
import { loadCsv } from './csv.js';
import { loadJson } from './json.js';
import { loadText } from './text.js';
import { loadFiles, processDataColumns } from './util.js';

/**
 * Test results interface.
 */
interface TestResult {
    name: string;
    passed: boolean;
    error?: string;
    details?: string;
}

/**
 * Mock PipelineStorage for testing.
 */
class MockPipelineStorage extends PipelineStorage {
    private files: Map<string, { content: string | Uint8Array; creationDate: string }> = new Map();

    constructor() {
        super();
        // Add some test files
        this.files.set('test.csv', {
            content: 'id,name,text\n1,Test1,"Hello world"\n2,Test2,"Another text"',
            creationDate: '2024-01-01T00:00:00Z'
        });
        this.files.set('test.json', {
            content: '[{"id": 1, "name": "Test1", "text": "Hello world"}, {"id": 2, "name": "Test2", "text": "Another text"}]',
            creationDate: '2024-01-01T00:00:00Z'
        });
        this.files.set('test.txt', {
            content: 'This is a test text file content.',
            creationDate: '2024-01-01T00:00:00Z'
        });
    }

    async *find(
        filePattern: RegExp,
        baseDir?: string,
        fileFilter?: Record<string, any>,
        maxCount?: number
    ): AsyncIterableIterator<[string, Record<string, any>]> {
        for (const [path] of this.files) {
            if (filePattern.test(path)) {
                yield [path, {}];
            }
        }
    }

    async get(key: string, asBytes?: boolean, encoding?: string): Promise<any> {
        const file = this.files.get(key);
        if (!file) {
            throw new Error(`File not found: ${key}`);
        }

        if (asBytes) {
            if (typeof file.content === 'string') {
                return new TextEncoder().encode(file.content);
            }
            return file.content;
        }

        if (typeof file.content === 'string') {
            return file.content;
        }
        return new TextDecoder(encoding || 'utf-8').decode(file.content);
    }

    async set(key: string, value: any, encoding?: string): Promise<void> {
        this.files.set(key, {
            content: value,
            creationDate: new Date().toISOString()
        });
    }

    async has(key: string): Promise<boolean> {
        return this.files.has(key);
    }

    async delete(key: string): Promise<void> {
        this.files.delete(key);
    }

    async clear(): Promise<void> {
        this.files.clear();
    }

    keys(): string[] {
        return Array.from(this.files.keys());
    }

    child(name: string): PipelineStorage {
        return this;
    }

    async getCreationDate(key: string): Promise<string> {
        const file = this.files.get(key);
        return file?.creationDate || new Date().toISOString();
    }
}

/**
 * Test runner class.
 */
class InputConversionTester {
    private results: TestResult[] = [];
    private storage: MockPipelineStorage;

    constructor() {
        this.storage = new MockPipelineStorage();
    }

    /**
     * Add a test result.
     */
    private addResult(name: string, passed: boolean, error?: string, details?: string): void {
        this.results.push({ name, passed, error, details });
    }

    /**
     * Test CSV loading functionality.
     */
    async testCsvLoading(): Promise<void> {
        try {
            const config = createInputConfig({
                fileType: InputFileType.CSV,
                filePattern: '.*\\.csv$',
                encoding: 'utf-8'
            });

            const result = await loadCsv(config, this.storage);
            
            const passed = result.data.length === 2 &&
                          result.columns.includes('id') &&
                          result.columns.includes('name') &&
                          result.columns.includes('text') &&
                          result.data[0].name === 'Test1';

            this.addResult(
                'CSV Loading',
                passed,
                undefined,
                `Loaded ${result.data.length} rows with columns: ${result.columns.join(', ')}`
            );
        } catch (error) {
            this.addResult('CSV Loading', false, error.message);
        }
    }

    /**
     * Test JSON loading functionality.
     */
    async testJsonLoading(): Promise<void> {
        try {
            const config = createInputConfig({
                fileType: InputFileType.JSON,
                filePattern: '.*\\.json$',
                encoding: 'utf-8'
            });

            const result = await loadJson(config, this.storage);
            
            const passed = result.data.length === 2 &&
                          result.columns.includes('id') &&
                          result.columns.includes('name') &&
                          result.data[0].name === 'Test1';

            this.addResult(
                'JSON Loading',
                passed,
                undefined,
                `Loaded ${result.data.length} rows with columns: ${result.columns.join(', ')}`
            );
        } catch (error) {
            this.addResult('JSON Loading', false, error.message);
        }
    }

    /**
     * Test text loading functionality.
     */
    async testTextLoading(): Promise<void> {
        try {
            const config = createInputConfig({
                fileType: InputFileType.TEXT,
                filePattern: '.*\\.txt$',
                encoding: 'utf-8'
            });

            const result = await loadText(config, this.storage);
            
            const passed = result.data.length === 1 &&
                          result.columns.includes('text') &&
                          result.columns.includes('id') &&
                          result.columns.includes('title') &&
                          result.data[0].text.includes('test text file');

            this.addResult(
                'Text Loading',
                passed,
                undefined,
                `Loaded ${result.data.length} rows with columns: ${result.columns.join(', ')}`
            );
        } catch (error) {
            this.addResult('Text Loading', false, error.message);
        }
    }

    /**
     * Test factory functionality.
     */
    async testFactory(): Promise<void> {
        try {
            const config = createInputConfig({
                fileType: InputFileType.CSV,
                filePattern: '.*\\.csv$',
                encoding: 'utf-8'
            });

            const result = await createInput(config, this.storage);
            
            const passed = result.data.length > 0 &&
                          result.columns.length > 0;

            this.addResult(
                'Factory Function',
                passed,
                undefined,
                `Factory created input with ${result.data.length} rows`
            );
        } catch (error) {
            this.addResult('Factory Function', false, error.message);
        }
    }

    /**
     * Test utility functions.
     */
    async testUtilityFunctions(): Promise<void> {
        try {
            // Test processDataColumns
            const testData: DataFrame = {
                columns: ['name', 'content'],
                data: [
                    { name: 'test1', content: 'content1' },
                    { name: 'test2', content: 'content2' }
                ]
            };

            const config = createInputConfig({
                textColumn: 'content',
                titleColumn: 'name'
            });

            const result = processDataColumns(testData, config, 'test.csv');
            
            const passed = result.columns.includes('id') &&
                          result.columns.includes('text') &&
                          result.columns.includes('title') &&
                          result.data[0].text === 'content1';

            this.addResult(
                'Utility Functions',
                passed,
                undefined,
                `Processed data with columns: ${result.columns.join(', ')}`
            );
        } catch (error) {
            this.addResult('Utility Functions', false, error.message);
        }
    }

    /**
     * Run all tests and return results.
     */
    async runAllTests(): Promise<TestResult[]> {
        console.log('🚀 Starting comprehensive GraphRAG index/input conversion tests...\n');

        await this.testCsvLoading();
        await this.testJsonLoading();
        await this.testTextLoading();
        await this.testFactory();
        await this.testUtilityFunctions();

        return this.results;
    }

    /**
     * Print test results.
     */
    printResults(): void {
        const passed = this.results.filter(r => r.passed).length;
        const total = this.results.length;

        console.log('\n📊 Test Results Summary:');
        console.log('========================');

        for (const result of this.results) {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.name}`);
            
            if (result.details) {
                console.log(`   Details: ${result.details}`);
            }
            
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        }

        console.log('\n📈 Overall Results:');
        console.log(`   Passed: ${passed}/${total} (${Math.round(passed/total*100)}%)`);
        
        if (passed === total) {
            console.log('🎉 All tests passed! TypeScript conversion is successful.');
        } else {
            console.log('⚠️  Some tests failed. Please review the conversion.');
        }
    }
}

/**
 * Run tests if this file is executed directly.
 */
async function runTests(): Promise<void> {
    const tester = new InputConversionTester();
    
    try {
        await tester.runAllTests();
        tester.printResults();
    } catch (error) {
        console.error('Test execution failed:', error);
    }
}

// Export for use in other modules
export { InputConversionTester, runTests };

// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}
