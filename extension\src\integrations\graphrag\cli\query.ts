/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * CLI implementation of the query subcommand.
 */

import * as path from 'path';

import * as api from '../api';
import { NoopQueryCallbacks } from '../callbacks/noop_query_callbacks';
import { loadConfig } from '../config/load_config';
import { GraphRagConfig } from '../config/models/graph_rag_config';
import { createStorageFromConfig } from '../utils/api';
import { loadTableFromStorage, storageHasTable } from '../utils/storage';

const logger = console;

export type DataFrame = Record<string, any>[];

export interface GlobalSearchOptions {
    config_filepath?: string;
    data_dir?: string;
    root_dir: string;
    community_level?: number;
    dynamic_community_selection: boolean;
    response_type: string;
    streaming: boolean;
    query: string;
    verbose: boolean;
}

export interface LocalSearchOptions {
    config_filepath?: string;
    data_dir?: string;
    root_dir: string;
    community_level: number;
    response_type: string;
    streaming: boolean;
    query: string;
    verbose: boolean;
}

export interface DriftSearchOptions {
    configFilepath?: string;
    dataDir?: string;
    rootDir: string;
    communityLevel: number;
    responseType: string;
    streaming: boolean;
    query: string;
    verbose: boolean;
}

export interface BasicSearchOptions {
    configFilepath?: string;
    dataDir?: string;
    rootDir: string;
    streaming: boolean;
    query: string;
    verbose: boolean;
}

export async function run_global_search(options: GlobalSearchOptions): Promise<[any, any]> {
    const root = path.resolve(options.root_dir);
    const cli_overrides: Record<string, any> = {};
    if (options.data_dir) {
        cli_overrides["output.base_dir"] = options.data_dir;
    }
    const config = loadConfig(root, options.config_filepath, cli_overrides);

    const dataframe_dict = await _resolve_output_files({
        config,
        output_list: ["entities", "communities", "community_reports"],
        optional_list: [],
    });

    // Call the Multi-Index Global Search API
    if (dataframe_dict["multi-index"]) {
        const final_entities_list = dataframe_dict["entities"];
        const final_communities_list = dataframe_dict["communities"];
        const final_community_reports_list = dataframe_dict["community_reports"];
        const index_names = dataframe_dict["index_names"];

        logger.info(`Running multi-index global search on indexes: ${dataframe_dict["index_names"]}`);

        const [response, context_data] = await api.multiIndexGlobalSearch({
            config,
            entities: final_entities_list,
            communities: final_communities_list,
            communityReports: final_community_reports_list,
            communityLevel: options.community_level || 2,
            dynamicCommunitySelection: options.dynamic_community_selection,
            responseType: options.response_type,
            query: options.query,
            verbose: options.verbose,
        });
        
        logger.info(`Query Response:\n${response}`);
        return [response, context_data];
    }

    // Otherwise, call the Single-Index Global Search API
    const final_entities: DataFrame = dataframe_dict["entities"];
    const final_communities: DataFrame = dataframe_dict["communities"];
    const final_community_reports: DataFrame = dataframe_dict["community_reports"];

    if (options.streaming) {
        let full_response = "";
        let context_data = {};

        const callbacks = new NoopQueryCallbacks();
        callbacks.on_context = (context: any) => {
            context_data = context;
        };

        const stream_generator = api.globalSearchStreaming({
            config,
            entities: final_entities,
            communities: final_communities,
            communityReports: final_community_reports,
            communityLevel: options.community_level,
            dynamicCommunitySelection: options.dynamic_community_selection,
            responseType: options.response_type,
            query: options.query,
            callbacks: [callbacks],
            verbose: options.verbose,
        });

        for await (const stream_chunk of stream_generator) {
            full_response += stream_chunk;
            console.log(stream_chunk);
        }
        console.log();
        return [full_response, context_data];
    }

    // Not streaming
    const [response, context_data] = await api.globalSearch({
        config,
        entities: final_entities,
        communities: final_communities,
        communityReports: final_community_reports,
        communityLevel: options.community_level,
        dynamicCommunitySelection: options.dynamic_community_selection,
        responseType: options.response_type,
        query: options.query,
        verbose: options.verbose,
    });

    logger.info(`Global Search Response:\n${response}`);
    return [response, context_data];
}

export async function run_local_search(options: LocalSearchOptions): Promise<[any, any]> {
    const root = path.resolve(options.root_dir);
    const cli_overrides: Record<string, any> = {};
    if (options.data_dir) {
        cli_overrides["output.base_dir"] = options.data_dir;
    }
    const config = loadConfig(root, options.config_filepath, cli_overrides);

    const dataframeDict = await resolveOutputFiles({
        config,
        outputList: ["communities", "community_reports", "text_units", "relationships", "entities"],
        optionalList: ["covariates"],
    });

    // Call the Multi-Index Local Search API
    if (dataframeDict["multi-index"]) {
        const finalEntitiesList = dataframeDict["entities"];
        const finalCommunitiesList = dataframeDict["communities"];
        const finalCommunityReportsList = dataframeDict["community_reports"];
        const finalTextUnitsList = dataframeDict["text_units"];
        const finalRelationshipsList = dataframeDict["relationships"];
        const indexNames = dataframeDict["index_names"];

        logger.info(`Running multi-index local search on indexes: ${dataframeDict["index_names"]}`);

        // If any covariates tables are missing from any index, set the covariates list to null
        const finalCovariatesList = dataframeDict["covariates"].length !== dataframeDict["num_indexes"] 
            ? null 
            : dataframeDict["covariates"];

        const [response, contextData] = await api.multiIndexLocalSearch({
            config,
            entitiesList: finalEntitiesList,
            communitiesList: finalCommunitiesList,
            communityReportsList: finalCommunityReportsList,
            textUnitsList: finalTextUnitsList,
            relationshipsList: finalRelationshipsList,
            covariatesList: finalCovariatesList,
            indexNames,
            communityLevel: options.communityLevel,
            responseType: options.responseType,
            streaming: options.streaming,
            query: options.query,
            verbose: options.verbose,
        });
        
        logger.info(`Local Search Response:\n${response}`);
        return [response, contextData];
    }

    // Otherwise, call the Single-Index Local Search API
    const finalCommunities: DataFrame = dataframeDict["communities"];
    const finalCommunityReports: DataFrame = dataframeDict["community_reports"];
    const finalTextUnits: DataFrame = dataframeDict["text_units"];
    const finalRelationships: DataFrame = dataframeDict["relationships"];
    const finalEntities: DataFrame = dataframeDict["entities"];
    const finalCovariates: DataFrame | null = dataframeDict["covariates"];

    if (options.streaming) {
        let fullResponse = "";
        let contextData = {};

        const callbacks = new NoopQueryCallbacks();
        callbacks.onContext = (context: any) => {
            contextData = context;
        };

        const streamGenerator = api.localSearchStreaming({
            config,
            entities: finalEntities,
            communities: finalCommunities,
            communityReports: finalCommunityReports,
            textUnits: finalTextUnits,
            relationships: finalRelationships,
            covariates: finalCovariates,
            communityLevel: options.communityLevel,
            responseType: options.responseType,
            query: options.query,
            callbacks: [callbacks],
            verbose: options.verbose,
        });

        for await (const streamChunk of streamGenerator) {
            fullResponse += streamChunk;
            process.stdout.write(streamChunk);
        }
        console.log();
        return [fullResponse, contextData];
    }

    // Not streaming
    const [response, contextData] = await api.localSearch({
        config,
        entities: finalEntities,
        communities: finalCommunities,
        communityReports: finalCommunityReports,
        textUnits: finalTextUnits,
        relationships: finalRelationships,
        covariates: finalCovariates,
        communityLevel: options.communityLevel,
        responseType: options.responseType,
        query: options.query,
        verbose: options.verbose,
    });
    
    logger.info(`Local Search Response:\n${response}`);
    return [response, contextData];
}

export async function runDriftSearch(options: DriftSearchOptions): Promise<[string, any]> {
    const root = path.resolve(options.rootDir);
    const cliOverrides: Record<string, any> = {};
    if (options.dataDir) {
        cliOverrides["output.base_dir"] = options.dataDir;
    }
    const config = loadConfig(root, options.configFilepath, cliOverrides);

    const dataframeDict = await _resolve_output_files({
        config,
        output_list: ["communities", "community_reports", "text_units", "relationships", "entities"],
    });

    // Call the Multi-Index Drift Search API
    if (dataframeDict["multi-index"]) {
        const finalEntitiesList = dataframeDict["entities"];
        const finalCommunitiesList = dataframeDict["communities"];
        const finalCommunityReportsList = dataframeDict["community_reports"];
        const finalTextUnitsList = dataframeDict["text_units"];
        const finalRelationshipsList = dataframeDict["relationships"];
        const indexNames = dataframeDict["index_names"];

        logger.info(`Running multi-index drift search on indexes: ${dataframeDict["index_names"]}`);

        const [response, contextData] = await api.multiIndexDriftSearch({
            config,
            entitiesList: finalEntitiesList,
            communitiesList: finalCommunitiesList,
            communityReportsList: finalCommunityReportsList,
            textUnitsList: finalTextUnitsList,
            relationshipsList: finalRelationshipsList,
            indexNames,
            communityLevel: options.communityLevel,
            responseType: options.responseType,
            streaming: options.streaming,
            query: options.query,
            verbose: options.verbose,
        });
        
        logger.info(`DRIFT Search Response:\n${response}`);
        return [response, contextData];
    }

    // Otherwise, call the Single-Index Drift Search API
    const finalCommunities: DataFrame = dataframeDict["communities"];
    const finalCommunityReports: DataFrame = dataframeDict["community_reports"];
    const finalTextUnits: DataFrame = dataframeDict["text_units"];
    const finalRelationships: DataFrame = dataframeDict["relationships"];
    const finalEntities: DataFrame = dataframeDict["entities"];

    if (options.streaming) {
        let fullResponse = "";
        let contextData = {};

        const callbacks = new NoopQueryCallbacks();
        callbacks.onContext = (context: any) => {
            contextData = context;
        };

        const streamGenerator = api.driftSearchStreaming({
            config,
            entities: finalEntities,
            communities: finalCommunities,
            communityReports: finalCommunityReports,
            textUnits: finalTextUnits,
            relationships: finalRelationships,
            communityLevel: options.communityLevel,
            responseType: options.responseType,
            query: options.query,
            callbacks: [callbacks],
            verbose: options.verbose,
        });

        for await (const streamChunk of streamGenerator) {
            fullResponse += streamChunk;
            process.stdout.write(streamChunk);
        }
        console.log();
        return [fullResponse, contextData];
    }

    // Not streaming
    const [response, contextData] = await api.driftSearch({
        config,
        entities: finalEntities,
        communities: finalCommunities,
        communityReports: finalCommunityReports,
        textUnits: finalTextUnits,
        relationships: finalRelationships,
        communityLevel: options.communityLevel,
        responseType: options.responseType,
        query: options.query,
        verbose: options.verbose,
    });
    
    logger.info(`DRIFT Search Response:\n${response}`);
    return [response, contextData];
}

export async function runBasicSearch(options: BasicSearchOptions): Promise<[string, any]> {
    const root = path.resolve(options.rootDir);
    const cliOverrides: Record<string, any> = {};
    if (options.dataDir) {
        cliOverrides["output.base_dir"] = options.dataDir;
    }
    const config = loadConfig(root, options.configFilepath, cliOverrides);

    const dataframeDict = await _resolve_output_files({
        config,
        output_list: ["text_units"],
    });

    // Call the Multi-Index Basic Search API
    if (dataframeDict["multi-index"]) {
        const finalTextUnitsList = dataframeDict["text_units"];
        const indexNames = dataframeDict["index_names"];

        logger.info(`Running multi-index basic search on indexes: ${dataframeDict["index_names"]}`);

        const [response, contextData] = await api.multiIndexBasicSearch({
            config,
            textUnitsList: finalTextUnitsList,
            indexNames,
            streaming: options.streaming,
            query: options.query,
            verbose: options.verbose,
        });
        
        logger.info(`Basic Search Response:\n${response}`);
        return [response, contextData];
    }

    // Otherwise, call the Single-Index Basic Search API
    const finalTextUnits: DataFrame = dataframeDict["text_units"];

    if (options.streaming) {
        let fullResponse = "";
        let contextData = {};

        const callbacks = new NoopQueryCallbacks();
        callbacks.onContext = (context: any) => {
            contextData = context;
        };

        const streamGenerator = api.basicSearchStreaming({
            config,
            textUnits: finalTextUnits,
            query: options.query,
            callbacks: [callbacks],
            verbose: options.verbose,
        });

        for await (const streamChunk of streamGenerator) {
            fullResponse += streamChunk;
            process.stdout.write(streamChunk);
        }
        console.log();
        return [fullResponse, contextData];
    }

    // Not streaming
    const [response, contextData] = await api.basicSearch({
        config,
        textUnits: finalTextUnits,
        query: options.query,
        verbose: options.verbose,
    });
    
    logger.info(`Basic Search Response:\n${response}`);
    return [response, contextData];
}

interface ResolveOutputFilesOptions {
    config: GraphRagConfig;
    output_list: string[];
    optional_list?: string[];
}

async function _resolve_output_files(options: ResolveOutputFilesOptions): Promise<Record<string, any>> {
    const { config, output_list, optional_list = [] } = options;
    const dataframeDict: Record<string, any> = {};

    // Loading output files for multi-index search
    if (config.outputs) {
        dataframeDict["multi-index"] = true;
        dataframeDict["num_indexes"] = Object.keys(config.outputs).length;
        dataframeDict["index_names"] = Object.keys(config.outputs);
        
        for (const output of Object.values(config.outputs)) {
            const storageObj = createStorageFromConfig(output);
            
            for (const name of output_list) {
                if (!dataframeDict[name]) {
                    dataframeDict[name] = [];
                }
                const dfValue = await loadTableFromStorage(name, storageObj);
                dataframeDict[name].push(dfValue);
            }

            // For optional output files, do not append if the dataframe does not exist
            for (const optionalFile of optional_list) {
                if (!dataframeDict[optionalFile]) {
                    dataframeDict[optionalFile] = [];
                }
                const fileExists = await storageHasTable(optionalFile, storageObj);
                if (fileExists) {
                    const dfValue = await loadTableFromStorage(optionalFile, storageObj);
                    dataframeDict[optionalFile].push(dfValue);
                }
            }
        }
        return dataframeDict;
    }

    // Loading output files for single-index search
    dataframeDict["multi-index"] = false;
    const storageObj = createStorageFromConfig(config.output);
    
    for (const name of output_list) {
        const dfValue = await loadTableFromStorage(name, storageObj);
        dataframeDict[name] = dfValue;
    }

    // For optional output files, set the dict entry to null instead of erroring out if it does not exist
    for (const optionalFile of optional_list) {
        const fileExists = await storageHasTable(optionalFile, storageObj);
        if (fileExists) {
            const dfValue = await loadTableFromStorage(optionalFile, storageObj);
            dataframeDict[optionalFile] = dfValue;
        } else {
            dataframeDict[optionalFile] = null;
        }
    }
    
    return dataframeDict;
}