/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Graph extraction using NLP.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../data_model/types.js';
import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { NoopPipelineCache } from '../../../cache/noop_pipeline_cache.js';
import { AsyncType } from '../../../config/enums.js';
import { BaseNounPhraseExtractor } from './np_extractors/base.js';
import { deriveFromRows } from '../../utils/derive_from_rows.js';
import { calculatePmiEdgeWeights } from '../../utils/graphs.js';
import { genSha512Hash } from '../../utils/hashing.js';



/**
 * Build a noun graph from text units.
 * @param textUnitDF - DataFrame with text units
 * @param textAnalyzer - Noun phrase extractor
 * @param normalizeEdgeWeights - Whether to normalize edge weights using PMI
 * @param numThreads - Number of threads for parallel processing
 * @param cache - Pipeline cache
 * @returns Tuple of [nodes DataFrame, edges DataFrame]
 */
export async function buildNounGraph(
    textUnitDF: DataFrame,
    textAnalyzer: BaseNounPhraseExtractor,
    normalizeEdgeWeights: boolean,
    numThreads: number = 4,
    cache?: PipelineCache
): Promise<[DataFrame, DataFrame]> {
    // Select relevant columns
    const textUnits: DataFrame = {
        columns: ['id', 'text'],
        data: textUnitDF.data.map(row => ({
            id: row.id,
            text: row.text
        }))
    };

    const nodesDF = await extractNodes(
        textUnits,
        textAnalyzer,
        numThreads,
        cache
    );
    
    const edgesDF = extractEdges(nodesDF, normalizeEdgeWeights);
    
    return [nodesDF, edgesDF];
}

/**
 * Extract initial nodes and edges from text units.
 * Input: text unit df with schema [id, text, document_id]
 * Returns a dataframe with schema [id, title, frequency, text_unit_ids].
 */
async function extractNodes(
    textUnitDF: DataFrame,
    textAnalyzer: BaseNounPhraseExtractor,
    numThreads: number = 4,
    cache?: PipelineCache
): Promise<DataFrame> {
    const cacheInstance = cache || new NoopPipelineCache();
    const childCache = cacheInstance.child("extract_noun_phrases");

    async function extract(row: Record<string, any>): Promise<string[]> {
        const text = row.text;
        const attrs = { text: text, analyzer: textAnalyzer.toString() };
        const key = genSha512Hash(attrs, Object.keys(attrs));
        
        let result = await childCache.get(key);
        if (!result) {
            result = textAnalyzer.extract(text);
            await childCache.set(key, result);
        }
        return result;
    }

    // Add noun_phrases column to textUnitDF (mimicking pandas assignment)
    const textUnitWithPhrases = await deriveFromRows(
        textUnitDF,
        extract,
        undefined,
        numThreads,
        AsyncType.THREADED,
        "extract noun phrases progress: "
    );

    // Create a new DataFrame with noun_phrases column
    const textUnitDFWithPhrases: DataFrame = {
        columns: [...textUnitDF.columns, 'noun_phrases'],
        data: textUnitDF.data.map((row, index) => ({
            ...row,
            noun_phrases: textUnitWithPhrases[index]
        }))
    };

    // Explode noun_phrases (equivalent to pandas explode)
    const explodedData: Array<{ title: string; text_unit_id: string }> = [];
    textUnitDFWithPhrases.data.forEach(row => {
        const phrases = row.noun_phrases;
        if (phrases && Array.isArray(phrases)) {
            phrases.forEach((phrase: string) => {
                explodedData.push({
                    title: phrase,
                    text_unit_id: row.id
                });
            });
        }
    });

    // Group by title and aggregate text_unit_ids (equivalent to pandas groupby)
    const groupedMap = new Map<string, string[]>();
    explodedData.forEach(item => {
        if (!groupedMap.has(item.title)) {
            groupedMap.set(item.title, []);
        }
        groupedMap.get(item.title)!.push(item.text_unit_id);
    });

    // Create final nodes dataframe with proper column selection
    const nodesData = Array.from(groupedMap.entries()).map(([title, textUnitIds]) => ({
        title: title,
        frequency: textUnitIds.length,
        text_unit_ids: textUnitIds
    }));

    return {
        columns: ['title', 'frequency', 'text_unit_ids'],
        data: nodesData
    };
}

/**
 * Extract edges from nodes.
 * Nodes appear in the same text unit are connected.
 * Input: nodes_df with schema [title, frequency, text_unit_ids]
 * Returns: edges_df with schema [source, target, weight, text_unit_ids]
 *
 * This function replicates the exact logic from the Python version:
 * 1. Explode text_unit_ids to create one row per text_unit_id
 * 2. Group by text_unit_id and aggregate titles (only if > 1 title)
 * 3. Generate all combinations of title pairs for each text_unit_id
 * 4. Normalize source/target order (min as source, max as target)
 * 5. Group by (source, target) and count occurrences
 * 6. Optionally apply PMI normalization
 */
function extractEdges(
    nodesDF: DataFrame,
    normalizeEdgeWeights: boolean = true
): DataFrame {
    // Step 1: Explode text_unit_ids (equivalent to pandas explode)
    const explodedData: Array<{ title: string; text_unit_id: string }> = [];
    nodesDF.data.forEach(row => {
        if (Array.isArray(row.text_unit_ids)) {
            row.text_unit_ids.forEach((textUnitId: string) => {
                explodedData.push({
                    title: row.title,
                    text_unit_id: textUnitId
                });
            });
        }
    });

    // Step 2: Group by text_unit_id and aggregate titles (equivalent to pandas groupby)
    const textUnitGroups = new Map<string, string[]>();
    explodedData.forEach(item => {
        if (!textUnitGroups.has(item.text_unit_id)) {
            textUnitGroups.set(item.text_unit_id, []);
        }
        textUnitGroups.get(item.text_unit_id)!.push(item.title);
    });

    // Filter to only keep text units with more than 1 title (equivalent to dropna after lambda filter)
    const validTextUnits = Array.from(textUnitGroups.entries())
        .filter(([_, titles]) => titles.length > 1)
        .map(([textUnitId, titles]) => ({ text_unit_id: textUnitId, titles }));

    // Step 3: Generate all combinations of title pairs (equivalent to itertools.combinations)
    const allEdges: Array<{ source: string; target: string; text_unit_id: string }> = [];

    validTextUnits.forEach(({ text_unit_id, titles }) => {
        // Generate all combinations of pairs
        for (let i = 0; i < titles.length; i++) {
            for (let j = i + 1; j < titles.length; j++) {
                allEdges.push({
                    source: titles[i],
                    target: titles[j],
                    text_unit_id: text_unit_id
                });
            }
        }
    });

    // Step 4: Normalize source/target order (min as source, max as target)
    const normalizedEdges = allEdges.map(edge => ({
        source: edge.source < edge.target ? edge.source : edge.target,
        target: edge.source < edge.target ? edge.target : edge.source,
        text_unit_id: edge.text_unit_id
    }));

    // Filter out edges with null/undefined source or target
    const validEdges = normalizedEdges.filter(edge =>
        edge.source != null && edge.target != null &&
        edge.source !== undefined && edge.target !== undefined
    );

    // Step 5: Group by (source, target) and aggregate text_unit_ids
    const edgeGroups = new Map<string, string[]>();
    validEdges.forEach(edge => {
        const key = `${edge.source}|${edge.target}`;
        if (!edgeGroups.has(key)) {
            edgeGroups.set(key, []);
        }
        edgeGroups.get(key)!.push(edge.text_unit_id);
    });

    // Create final edges dataframe
    let edgesData = Array.from(edgeGroups.entries()).map(([key, textUnitIds]) => {
        const [source, target] = key.split('|');
        return {
            source: source,
            target: target,
            weight: textUnitIds.length,
            text_unit_ids: textUnitIds
        };
    });

    let edgesDF: DataFrame = {
        columns: ['source', 'target', 'weight', 'text_unit_ids'],
        data: edgesData
    };

    // Step 6: Optionally apply PMI normalization
    if (normalizeEdgeWeights) {
        // Use PMI weight instead of raw weight
        edgesDF = calculatePmiEdgeWeights(nodesDF, edgesDF);
    }

    return edgesDF;
}