// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the extract claims configuration.
 */

import { readFileSync } from 'fs';
import { join } from 'path';
import { graphragConfigDefaults } from '../defaults.js';
import { LanguageModelConfig } from './language-model-config.js';

/**
 * Configuration section for claim extraction.
 */
export interface ClaimExtractionConfig {
  /**
   * Whether claim extraction is enabled.
   */
  enabled: boolean;

  /**
   * The model ID to use for claim extraction.
   */
  modelId: string;

  /**
   * The claim extraction prompt to use.
   */
  prompt?: string;

  /**
   * The claim description to use.
   */
  description: string;

  /**
   * The maximum number of entity gleanings to use.
   */
  maxGleanings: number;

  /**
   * The override strategy to use.
   */
  strategy?: Record<string, any>;
}

/**
 * Create a ClaimExtractionConfig with default values.
 */
export function createClaimExtractionConfig(config: Partial<ClaimExtractionConfig> = {}): ClaimExtractionConfig {
  return {
    enabled: config.enabled ?? graphragConfigDefaults.extractClaims.enabled,
    modelId: config.modelId ?? graphragConfigDefaults.extractClaims.modelId,
    prompt: config.prompt ?? graphragConfigDefaults.extractClaims.prompt,
    description: config.description ?? graphragConfigDefaults.extractClaims.description,
    maxGleanings: config.maxGleanings ?? graphragConfigDefaults.extractClaims.maxGleanings,
    strategy: config.strategy ?? graphragConfigDefaults.extractClaims.strategy,
  };
}

/**
 * Get the resolved claim extraction strategy.
 */
export function getResolvedClaimExtractionStrategy(
  config: ClaimExtractionConfig,
  rootDir: string,
  modelConfig: LanguageModelConfig
): Record<string, any> {
  return config.strategy || {
    llm: modelConfig,
    extractionPrompt: config.prompt
      ? readFileSync(join(rootDir, config.prompt), 'utf-8')
      : undefined,
    claimDescription: config.description,
    maxGleanings: config.maxGleanings,
  };
}
