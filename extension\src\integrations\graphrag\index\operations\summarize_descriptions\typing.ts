/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'SummarizedDescriptionResult' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineCache } from '../../../cache/pipeline_cache.js';

export type StrategyConfig = Record<string, any>;

/**
 * Entity summarization result class definition.
 * Matches the Python SummarizedDescriptionResult dataclass exactly.
 */
export interface SummarizedDescriptionResult {
    id: string | [string, string];
    description: string;
}

/**
 * Summarization strategy function type.
 * Matches the Python SummarizationStrategy type exactly.
 */
export type SummarizationStrategy = (
    id: string | [string, string],
    descriptions: string[],
    cache: PipelineCache,
    config: StrategyConfig
) => Promise<SummarizedDescriptionResult>;

/**
 * Description summarize row interface.
 * Matches the Python DescriptionSummarizeRow NamedTuple exactly.
 */
export interface DescriptionSummarizeRow {
    graph: any;
}

/**
 * SummarizeStrategyType enum definition.
 * Matches the Python SummarizeStrategyType enum exactly.
 */
export enum SummarizeStrategyType {
    graph_intelligence = "graph_intelligence"
}