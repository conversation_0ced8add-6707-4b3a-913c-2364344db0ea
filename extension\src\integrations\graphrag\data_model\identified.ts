/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A package containing the 'Identified' interface.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * An interface for an item with an ID.
 */
export interface Identified {
    /** The ID of the item. */
    id: string

    /** Human readable ID used to refer to this community in prompts or texts displayed to users, such as in a report text (optional). */
    short_id?: string
}