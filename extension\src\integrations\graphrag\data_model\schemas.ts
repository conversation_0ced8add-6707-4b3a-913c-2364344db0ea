/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * Common field name definitions for data frames.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

// Common fields
export const ID = "id"
export const SHORT_ID = "human_readable_id"
export const TITLE = "title"
export const DESCRIPTION = "description"
export const TYPE = "type"

// POST-PREP NODE TABLE SCHEMA
export const NODE_DEGREE = "degree"
export const NODE_FREQUENCY = "frequency"
export const NODE_DETAILS = "node_details"
export const NODE_X = "x"
export const NODE_Y = "y"

// POST-PREP EDGE TABLE SCHEMA
export const EDGE_SOURCE = "source"
export const EDGE_TARGET = "target"
export const EDGE_DEGREE = "combined_degree"
export const EDGE_DETAILS = "edge_details"
export const EDGE_WEIGHT = "weight"

// POST-PREP CLAIM TABLE SCHEMA
export const CLAIM_SUBJECT = "subject_id"
export const CLAIM_STATUS = "status"
export const CLAIM_DETAILS = "claim_details"

// COMMUNITY HIERARCHY TABLE SCHEMA
export const SUB_COMMUNITY = "sub_community"

// COMMUNITY CONTEXT TABLE SCHEMA
export const ALL_CONTEXT = "all_context"
export const CONTEXT_STRING = "context_string"
export const CONTEXT_SIZE = "context_size"
export const CONTEXT_EXCEED_FLAG = "context_exceed_limit"

// COMMUNITY REPORT TABLE SCHEMA
export const COMMUNITY_ID = "community"
export const COMMUNITY_LEVEL = "level"
export const COMMUNITY_PARENT = "parent"
export const COMMUNITY_CHILDREN = "children"
export const SUMMARY = "summary"
export const FINDINGS = "findings"
export const RATING = "rank"
export const EXPLANATION = "rating_explanation"
export const FULL_CONTENT = "full_content"
export const FULL_CONTENT_JSON = "full_content_json"

export const ENTITY_IDS = "entity_ids"
export const RELATIONSHIP_IDS = "relationship_ids"
export const TEXT_UNIT_IDS = "text_unit_ids"
export const COVARIATE_IDS = "covariate_ids"
export const DOCUMENT_IDS = "document_ids"

export const PERIOD = "period"
export const SIZE = "size"

// text units
export const ENTITY_DEGREE = "entity_degree"
export const ALL_DETAILS = "all_details"
export const TEXT = "text"
export const N_TOKENS = "n_tokens"

export const CREATION_DATE = "creation_date"
export const METADATA = "metadata"

// Final column definitions for data model outputs
export const ENTITIES_FINAL_COLUMNS = [
    ID,
    SHORT_ID,
    TITLE,
    TYPE,
    DESCRIPTION,
    TEXT_UNIT_IDS,
    NODE_FREQUENCY,
    NODE_DEGREE,
    NODE_X,
    NODE_Y,
] as const

export const RELATIONSHIPS_FINAL_COLUMNS = [
    ID,
    SHORT_ID,
    EDGE_SOURCE,
    EDGE_TARGET,
    DESCRIPTION,
    EDGE_WEIGHT,
    EDGE_DEGREE,
    TEXT_UNIT_IDS,
] as const

export const COMMUNITIES_FINAL_COLUMNS = [
    ID,
    SHORT_ID,
    COMMUNITY_ID,
    COMMUNITY_LEVEL,
    COMMUNITY_PARENT,
    COMMUNITY_CHILDREN,
    TITLE,
    ENTITY_IDS,
    RELATIONSHIP_IDS,
    TEXT_UNIT_IDS,
    PERIOD,
    SIZE,
] as const

export const COMMUNITY_REPORTS_FINAL_COLUMNS = [
    ID,
    SHORT_ID,
    COMMUNITY_ID,
    COMMUNITY_LEVEL,
    COMMUNITY_PARENT,
    COMMUNITY_CHILDREN,
    TITLE,
    SUMMARY,
    FULL_CONTENT,
    RATING,
    EXPLANATION,
    FINDINGS,
    FULL_CONTENT_JSON,
    PERIOD,
    SIZE,
] as const

export const COVARIATES_FINAL_COLUMNS = [
    ID,
    SHORT_ID,
    "covariate_type",
    TYPE,
    DESCRIPTION,
    "subject_id",
    "object_id",
    "status",
    "start_date",
    "end_date",
    "source_text",
    "text_unit_id",
] as const

export const TEXT_UNITS_FINAL_COLUMNS = [
    ID,
    SHORT_ID,
    TEXT,
    N_TOKENS,
    DOCUMENT_IDS,
    ENTITY_IDS,
    RELATIONSHIP_IDS,
    COVARIATE_IDS,
] as const

export const DOCUMENTS_FINAL_COLUMNS = [
    ID,
    SHORT_ID,
    TITLE,
    TEXT,
    TEXT_UNIT_IDS,
    CREATION_DATE,
    METADATA,
] as const

// Type definitions for column arrays
export type EntitiesColumns = typeof ENTITIES_FINAL_COLUMNS[number]
export type RelationshipsColumns = typeof RELATIONSHIPS_FINAL_COLUMNS[number]
export type CommunitiesColumns = typeof COMMUNITIES_FINAL_COLUMNS[number]
export type CommunityReportsColumns = typeof COMMUNITY_REPORTS_FINAL_COLUMNS[number]
export type CovariatesColumns = typeof COVARIATES_FINAL_COLUMNS[number]
export type TextUnitsColumns = typeof TEXT_UNITS_FINAL_COLUMNS[number]
export type DocumentsColumns = typeof DOCUMENTS_FINAL_COLUMNS[number]