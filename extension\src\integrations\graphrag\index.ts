/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * The GraphRAG package.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

// Core data models
export * from './data_model'

// Configuration
export * from './config/enums'
export * from './config/errors'
export * from './config/defaults'

// Storage interfaces
// export * from './storage' // Will be implemented later

// Vector stores
// export * from './vector_stores' // Will be implemented later

// Query interfaces
// export * from './query' // Will be implemented later

// API interfaces
// export * from './api' // Will be implemented later

// Initialize console logging (simplified for TypeScript)
console.log('[GraphRAG] Package initialized')