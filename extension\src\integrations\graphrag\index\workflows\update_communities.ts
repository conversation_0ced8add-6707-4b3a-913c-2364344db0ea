/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { getUpdateStorages } from '../run/utils';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { updateAndMergeCommunities } from '../update/communities';
import { PipelineStorage } from '../../storage/pipeline-storage';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * Update the communities from a incremental index run.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: update_communities");
    
    const { outputStorage, previousStorage, deltaStorage } = getUpdateStorages(
        config, 
        context.state["update_timestamp"]
    );

    const communityIdMapping = await updateCommunities(
        previousStorage, 
        deltaStorage, 
        outputStorage
    );

    context.state["incremental_update_community_id_mapping"] = communityIdMapping;

    logger.info("Workflow completed: update_communities");
    return { result: null };
}

/**
 * Update the communities output.
 */
async function updateCommunities(
    previousStorage: PipelineStorage,
    deltaStorage: PipelineStorage,
    outputStorage: PipelineStorage,
): Promise<Record<string, any>> {
    const oldCommunities = await loadTableFromStorage("communities", previousStorage);
    const deltaCommunities = await loadTableFromStorage("communities", deltaStorage);
    
    const { mergedCommunities, communityIdMapping } = updateAndMergeCommunities(
        oldCommunities, 
        deltaCommunities
    );

    await writeTableToStorage(mergedCommunities, "communities", outputStorage);

    return communityIdMapping;
}