// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Entity summarization prompt generation module.
 */

import * as fs from 'fs';
import * as path from 'path';
import { ENTITY_SUMMARIZATION_PROMPT } from '../template/entity_summarization';

export const ENTITY_SUMMARIZATION_FILENAME = 'summarize_descriptions.txt';

/**
 * Create a prompt for entity summarization.
 * 
 * @param persona - The persona to use for the entity summarization prompt
 * @param language - The language to use for the entity summarization prompt
 * @param outputPath - The path to write the prompt to (optional)
 * @returns The generated prompt
 */
export function createEntitySummarizationPrompt(
    persona: string,
    language: string,
    outputPath?: string
): string {
    const prompt = ENTITY_SUMMARIZATION_PROMPT
        .replace('{persona}', persona)
        .replace('{language}', language);

    if (outputPath) {
        // Ensure directory exists
        fs.mkdirSync(outputPath, { recursive: true });

        const fullPath = path.join(outputPath, ENTITY_SUMMARIZATION_FILENAME);
        
        // Write file to output path
        fs.writeFileSync(fullPath, prompt, { encoding: 'utf-8' });
    }

    return prompt;
}

/**
 * Create entity summarization prompt asynchronously.
 */
export async function createEntitySummarizationPromptAsync(
    persona: string,
    language: string,
    outputPath?: string
): Promise<string> {
    const prompt = ENTITY_SUMMARIZATION_PROMPT
        .replace('{persona}', persona)
        .replace('{language}', language);

    if (outputPath) {
        // Ensure directory exists
        await fs.promises.mkdir(outputPath, { recursive: true });

        const fullPath = path.join(outputPath, ENTITY_SUMMARIZATION_FILENAME);
        
        // Write file to output path
        await fs.promises.writeFile(fullPath, prompt, { encoding: 'utf-8' });
    }

    return prompt;
}

/**
 * Validate entity summarization prompt parameters.
 */
export function validateEntitySummarizationParams(
    persona: string,
    language: string
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!persona || persona.trim() === '') {
        errors.push('Persona cannot be empty');
    }

    if (!language || language.trim() === '') {
        errors.push('Language cannot be empty');
    }

    if (persona.length > 5000) {
        errors.push('Persona is too long (max 5000 characters)');
    }

    if (language.length > 100) {
        errors.push('Language specification is too long (max 100 characters)');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Create entity summarization prompt with validation.
 */
export function createEntitySummarizationPromptSafe(
    persona: string,
    language: string,
    outputPath?: string
): { prompt?: string; success: boolean; errors: string[] } {
    const validation = validateEntitySummarizationParams(persona, language);
    
    if (!validation.isValid) {
        return {
            success: false,
            errors: validation.errors
        };
    }

    try {
        const prompt = createEntitySummarizationPrompt(persona, language, outputPath);
        return {
            prompt,
            success: true,
            errors: []
        };
    } catch (error) {
        return {
            success: false,
            errors: [`Failed to create prompt: ${error instanceof Error ? error.message : String(error)}`]
        };
    }
}
