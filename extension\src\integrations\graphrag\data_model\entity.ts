/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A package containing the 'Entity' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Named } from './named'

/**
 * An interface for an entity in the system.
 */
export interface Entity extends Named {
    /** Type of the entity (can be any string, optional). */
    type?: string

    /** Description of the entity (optional). */
    description?: string

    /** The semantic (i.e. text) embedding of the entity (optional). */
    description_embedding?: number[]

    /** The semantic (i.e. text) embedding of the entity (optional). */
    name_embedding?: number[]

    /** The community IDs of the entity (optional). */
    community_ids?: string[]

    /** List of text unit IDs in which the entity appears (optional). */
    text_unit_ids?: string[]

    /** Rank of the entity, used for sorting (optional). Higher rank indicates more important entity. This can be based on centrality or other metrics. */
    rank?: number

    /** Additional attributes associated with the entity (optional), e.g. start time, end time, etc. To be included in the search prompt. */
    attributes?: Record<string, any>
}

/**
 * Create a new entity from the dict data.
 */
export function createEntityFromDict(
    d: Record<string, any>,
    options: {
        id_key?: string
        short_id_key?: string
        title_key?: string
        type_key?: string
        description_key?: string
        description_embedding_key?: string
        name_embedding_key?: string
        community_key?: string
        text_unit_ids_key?: string
        rank_key?: string
        attributes_key?: string
    } = {}
): Entity {
    const {
        id_key = "id",
        short_id_key = "human_readable_id",
        title_key = "title",
        type_key = "type",
        description_key = "description",
        description_embedding_key = "description_embedding",
        name_embedding_key = "name_embedding",
        community_key = "community",
        text_unit_ids_key = "text_unit_ids",
        rank_key = "degree",
        attributes_key = "attributes"
    } = options

    return {
        id: d[id_key],
        title: d[title_key],
        short_id: d[short_id_key],
        type: d[type_key],
        description: d[description_key],
        name_embedding: d[name_embedding_key],
        description_embedding: d[description_embedding_key],
        community_ids: d[community_key],
        rank: d[rank_key] ?? 1,
        text_unit_ids: d[text_unit_ids_key],
        attributes: d[attributes_key]
    }
}