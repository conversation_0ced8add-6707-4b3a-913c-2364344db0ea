/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Dataframe operations and utils for Incremental Indexing.
 */

import { DataFrame } from '../../data-model/types';
import { 
    COMMUNITIES_FINAL_COLUMNS, 
    COMMUNITY_REPORTS_FINAL_COLUMNS 
} from '../../data-model/schemas';

/**
 * Update and merge communities.
 * @param oldCommunities - The old communities
 * @param deltaCommunities - The delta communities
 * @returns Tuple of [updated communities, community id mapping]
 */
export function updateAndMergeCommunities(
    oldCommunities: DataFrame,
    deltaCommunities: DataFrame
): [DataFrame, Record<number, number>] {
    // Ensure required columns exist
    const ensureColumns = (df: DataFrame, columns: string[]) => {
        const newData = df.data.map(row => {
            const newRow = { ...row };
            columns.forEach(col => {
                if (!(col in newRow)) {
                    newRow[col] = null;
                }
            });
            return newRow;
        });
        
        const allColumns = new Set([...df.columns, ...columns]);
        return {
            columns: Array.from(allColumns),
            data: newData
        };
    };

    const oldCommunitiesWithCols = ensureColumns(oldCommunities, ['size', 'period']);
    const deltaCommunitiesWithCols = ensureColumns(deltaCommunities, ['size', 'period']);

    // Find max community ID from old communities
    const oldMaxCommunityId = Math.max(
        ...oldCommunitiesWithCols.data
            .map(row => row.community)
            .filter(id => id != null)
            .map(id => parseInt(String(id), 10))
    );

    // Create community ID mapping
    const communityIdMapping: Record<number, number> = { [-1]: -1 };
    deltaCommunitiesWithCols.data.forEach(row => {
        if (row.community != null) {
            const oldId = parseInt(String(row.community), 10);
            communityIdMapping[oldId] = oldId + oldMaxCommunityId + 1;
        }
    });

    // Update delta communities with new IDs
    const updatedDeltaData = deltaCommunitiesWithCols.data.map(row => ({
        ...row,
        community: row.community != null ? 
            communityIdMapping[parseInt(String(row.community), 10)] || row.community : 
            row.community,
        parent: row.parent != null ? 
            communityIdMapping[parseInt(String(row.parent), 10)] || row.parent : 
            row.parent
    }));

    // Convert old communities to int
    const updatedOldData = oldCommunitiesWithCols.data.map(row => ({
        ...row,
        community: row.community != null ? parseInt(String(row.community), 10) : row.community
    }));

    // Merge communities
    const mergedData = [...updatedOldData, ...updatedDeltaData];

    // Update titles and human readable IDs
    const finalData = mergedData.map(row => ({
        ...row,
        title: `Community ${row.community}`,
        human_readable_id: row.community
    }));

    // Filter to final columns
    const finalColumns = COMMUNITIES_FINAL_COLUMNS;
    const filteredData = finalData.map(row => {
        const newRow: Record<string, any> = {};
        finalColumns.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });

    return [
        {
            columns: finalColumns,
            data: filteredData
        },
        communityIdMapping
    ];
}

/**
 * Update and merge community reports.
 * @param oldCommunityReports - The old community reports
 * @param deltaCommunityReports - The delta community reports
 * @param communityIdMapping - The community id mapping
 * @returns Updated community reports
 */
export function updateAndMergeCommunityReports(
    oldCommunityReports: DataFrame,
    deltaCommunityReports: DataFrame,
    communityIdMapping: Record<number, number>
): DataFrame {
    // Ensure required columns exist
    const ensureColumns = (df: DataFrame, columns: string[]) => {
        const newData = df.data.map(row => {
            const newRow = { ...row };
            columns.forEach(col => {
                if (!(col in newRow)) {
                    newRow[col] = null;
                }
            });
            return newRow;
        });
        
        const allColumns = new Set([...df.columns, ...columns]);
        return {
            columns: Array.from(allColumns),
            data: newData
        };
    };

    const oldReportsWithCols = ensureColumns(oldCommunityReports, ['size', 'period']);
    const deltaReportsWithCols = ensureColumns(deltaCommunityReports, ['size', 'period']);

    // Update delta community reports with new IDs
    const updatedDeltaData = deltaReportsWithCols.data.map(row => ({
        ...row,
        community: row.community != null ? 
            communityIdMapping[parseInt(String(row.community), 10)] || row.community : 
            row.community,
        parent: row.parent != null ? 
            communityIdMapping[parseInt(String(row.parent), 10)] || row.parent : 
            row.parent
    }));

    // Convert old community reports to int
    const updatedOldData = oldReportsWithCols.data.map(row => ({
        ...row,
        community: row.community != null ? parseInt(String(row.community), 10) : row.community
    }));

    // Merge community reports
    const mergedData = [...updatedOldData, ...updatedDeltaData];

    // Update human readable IDs
    const finalData = mergedData.map(row => ({
        ...row,
        community: row.community != null ? parseInt(String(row.community), 10) : row.community,
        human_readable_id: row.community
    }));

    // Filter to final columns
    const finalColumns = COMMUNITY_REPORTS_FINAL_COLUMNS;
    const filteredData = finalData.map(row => {
        const newRow: Record<string, any> = {};
        finalColumns.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });

    return {
        columns: finalColumns,
        data: filteredData
    };
}