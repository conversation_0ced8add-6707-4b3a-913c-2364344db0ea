// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Util functions to tag noun phrases for filtering.
 */

/**
 * Check if list of tokens forms a compound noun phrase.
 */
export function isCompound(tokens: string[]): boolean {
  return tokens.some(token => {
    const trimmed = token.trim();
    return trimmed.includes('-') && 
           trimmed.length > 1 && 
           trimmed.split('-').length > 1;
  });
}

/**
 * Check if all tokens have valid length.
 */
export function hasValidTokenLength(tokens: string[], maxLength: number): boolean {
  return tokens.every(token => token.length <= maxLength);
}

/**
 * Check if the entity is valid.
 */
export function isValidEntity(entity: [string, string], tokens: string[]): boolean {
  const [, entityType] = entity;
  
  return (
    (!['CARDINAL', 'ORDINAL'].includes(entityType) && tokens.length > 0) ||
    (['CARDINAL', 'ORDINAL'].includes(entityType) && 
     (tokens.length > 1 || isCompound(tokens)))
  );
}
