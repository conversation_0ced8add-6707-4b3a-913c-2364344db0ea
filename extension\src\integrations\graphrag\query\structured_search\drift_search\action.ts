// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * DRIFT Search Query Action.
 */

import { tryParseJsonObject } from '../../llm/text-utils.js';

/**
 * Represent an action containing a query, answer, score, and follow-up actions.
 * 
 * This class encapsulates action strings produced by the LLM in a structured way.
 */
export class DriftAction {
  public query: string;
  public answer: string | null = null;
  public score: number | null = null;
  public followUps: DriftAction[] = [];
  public metadata: Record<string, any> = {
    llmCalls: 0,
    promptTokens: 0,
    outputTokens: 0,
  };

  /**
   * Initialize the DriftAction with a query, optional answer, and follow-up actions.
   */
  constructor(
    query: string,
    answer?: string | null,
    followUps?: DriftAction[] | null
  ) {
    this.query = query;
    this.answer = answer ?? null;
    this.followUps = followUps ?? [];
  }

  /**
   * Check if the action is complete (i.e., an answer is available).
   */
  get isComplete(): boolean {
    return this.answer !== null;
  }

  /**
   * Execute an asynchronous search using the search engine, and update the action with the results.
   * 
   * If a scorer is provided, compute the score for the action.
   */
  async search(searchEngine: any, globalQuery: string, scorer?: any): Promise<DriftAction> {
    if (this.isComplete) {
      console.warn('Action already complete. Skipping search.');
      return this;
    }

    const searchResult = await searchEngine.search({
      driftQuery: globalQuery,
      query: this.query
    });

    // Do not launch exception as it will roll up with other steps
    // Instead return an empty response and let score -inf handle it
    const [, response] = tryParseJsonObject(searchResult.response, false);

    this.answer = response.response || null;
    this.score = parseFloat(response.score || '-Infinity');
    this.metadata.contextData = searchResult.contextData;

    if (this.answer === null) {
      console.warn(`No answer found for query: ${this.query}`);
    }

    this.metadata.llmCalls += 1;
    this.metadata.promptTokens += searchResult.promptTokens;
    this.metadata.outputTokens += searchResult.outputTokens;

    this.followUps = response.followUpQueries || [];
    if (!this.followUps || this.followUps.length === 0) {
      console.warn(`No follow-up actions found for response: ${JSON.stringify(response)}`);
    }

    if (scorer) {
      this.computeScore(scorer);
    }

    return this;
  }

  /**
   * Compute the score for the action using the provided scorer.
   */
  computeScore(scorer: any): void {
    const score = scorer.computeScore(this.query, this.answer);
    this.score = score !== null ? score : -Infinity;
  }

  /**
   * Serialize the action to a dictionary.
   */
  serialize(includeFollowUps: boolean = true): Record<string, any> {
    const data: Record<string, any> = {
      query: this.query,
      answer: this.answer,
      score: this.score,
      metadata: this.metadata,
    };
    
    if (includeFollowUps) {
      data.followUps = this.followUps.map(action => action.serialize());
    }
    
    return data;
  }

  /**
   * Deserialize the action from a dictionary.
   */
  static deserialize(data: Record<string, any>): DriftAction {
    // Ensure 'query' exists in the data, raise an error if missing
    const query = data.query;
    if (query === undefined || query === null) {
      throw new Error("Missing 'query' key in serialized data");
    }

    // Initialize the DriftAction
    const action = new DriftAction(query);
    action.answer = data.answer || null;
    action.score = data.score || null;
    action.metadata = data.metadata || {};

    action.followUps = (data.followUpQueries || []).map((fuData: any) =>
      DriftAction.deserialize(fuData)
    );
    
    return action;
  }

  /**
   * Create a DriftAction from a DRIFTPrimer response.
   */
  static fromPrimerResponse(
    query: string,
    response: string | Record<string, any> | Record<string, any>[]
  ): DriftAction {
    if (typeof response === 'object' && !Array.isArray(response)) {
      const action = new DriftAction(
        query,
        response.intermediateAnswer,
        response.followUpQueries
      );
      action.score = response.score || null;
      return action;
    }

    // If response is a string, attempt to parse as JSON
    if (typeof response === 'string') {
      try {
        const parsedResponse = JSON.parse(response);
        if (typeof parsedResponse === 'object' && !Array.isArray(parsedResponse)) {
          return DriftAction.fromPrimerResponse(query, parsedResponse);
        }
        throw new Error('Parsed response must be a dictionary.');
      } catch (e) {
        throw new Error(`Failed to parse response string: ${e}. Parsed response must be a dictionary.`);
      }
    }

    throw new Error(`Unsupported response type: ${typeof response}. Expected a dictionary or JSON string.`);
  }

  /**
   * Allow DriftAction objects to be hashable for use in networkx.MultiDiGraph.
   * 
   * Assumes queries are unique.
   */
  hash(): number {
    // Simple hash function for strings
    let hash = 0;
    for (let i = 0; i < this.query.length; i++) {
      const char = this.query.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash;
  }

  /**
   * Check equality based on the query string.
   */
  equals(other: any): boolean {
    if (!(other instanceof DriftAction)) {
      return false;
    }
    return this.query === other.query;
  }
}
