// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Sort context by degree in descending order.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../../data_model/types.js';
import * as schemas from '../../../../data_model/schemas.js';
import { numTokens } from '../../../../query/llm/text_utils.js';

/**
 * Sort context by degree in descending order, optimizing for performance.
 * Matches the Python sort_context function exactly.
 */
export function sort_context(
  local_context: Record<string, any>[],
  sub_community_reports?: Record<string, any>[],
  max_context_tokens?: number,
  node_name_column: string = schemas.TITLE,
  node_details_column: string = schemas.NODE_DETAILS,
  edge_id_column: string = schemas.SHORT_ID,
  edge_details_column: string = schemas.EDGE_DETAILS,
  edge_degree_column: string = schemas.EDGE_DEGREE,
  edge_source_column: string = schemas.EDGE_SOURCE,
  edge_target_column: string = schemas.EDGE_TARGET,
  claim_details_column: string = schemas.CLAIM_DETAILS
): string {
  /**
   * Concatenate structured data into a context string.
   * Matches the Python _get_context_string function exactly.
   */
  function _get_context_string(
    entities: Record<string, any>[],
    edges: Record<string, any>[],
    claims: Record<string, any>[],
    sub_community_reports?: Record<string, any>[]
  ): string {
    const contexts: string[] = [];

    if (sub_community_reports && sub_community_reports.length > 0) {
      // Python: report_df = pd.DataFrame(sub_community_reports)
      // contexts.append(f"----Reports-----\n{report_df.to_csv(index=False, sep=',')}")
      const report_data = sub_community_reports.map(report => {
        return Object.keys(report).map(key => report[key]).join(',');
      }).join('\n');
      const headers = sub_community_reports.length > 0 ? Object.keys(sub_community_reports[0]).join(',') : '';
      contexts.push(`----Reports-----\n${headers}\n${report_data}`);
    }

    const sections = [
      ['Entities', entities],
      ['Claims', claims],
      ['Relationships', edges],
    ] as const;

    for (const [label, data] of sections) {
      if (data && data.length > 0) {
        // Python: data_df = pd.DataFrame(data)
        // contexts.append(f"-----{label}-----\n{data_df.to_csv(index=False, sep=',')}")
        const data_rows = data.map(item => {
          return Object.keys(item).map(key => item[key]).join(',');
        }).join('\n');
        const headers = data.length > 0 ? Object.keys(data[0]).join(',') : '';
        contexts.push(`-----${label}-----\n${headers}\n${data_rows}`);
      }
    }

    return contexts.join('\n\n');
  }

  // Python: edges = [...]
  const edges: Record<string, any>[] = [];
  for (const record of local_context) {
    const edge_details = record[edge_details_column];
    if (Array.isArray(edge_details)) {
      for (const e of edge_details) {
        if (typeof e === 'object' && e !== null) {
          edges.push({
            ...e,
            [schemas.SHORT_ID]: parseInt(e[schemas.SHORT_ID])
          });
        }
      }
    }
  }

  // Python: node_details = {...}
  const node_details: Record<string, any> = {};
  for (const record of local_context) {
    const node_name = record[node_name_column];
    const node_detail = record[node_details_column];
    if (node_detail) {
      node_details[node_name] = {
        ...node_detail,
        [schemas.SHORT_ID]: parseInt(node_detail[schemas.SHORT_ID])
      };
    }
  }

  // Python: claim_details = {...}
  const claim_details: Record<string, any[]> = {};
  for (const record of local_context) {
    const node_name = record[node_name_column];
    const claims = record[claim_details_column];
    if (Array.isArray(claims)) {
      claim_details[node_name] = claims
        .filter(c => typeof c === 'object' && c !== null && c[schemas.SHORT_ID] != null)
        .map(c => ({
          ...c,
          [schemas.SHORT_ID]: parseInt(c[schemas.SHORT_ID])
        }));
    }
  }

  // Python: edges.sort(key=lambda x: (-x.get(edge_degree_column, 0), x.get(edge_id_column, "")))
  edges.sort((a, b) => {
    const degree_a = a[edge_degree_column] || 0;
    const degree_b = b[edge_degree_column] || 0;
    if (degree_b !== degree_a) {
      return degree_b - degree_a;
    }
    const id_a = a[edge_id_column] || '';
    const id_b = b[edge_id_column] || '';
    return id_a.toString().localeCompare(id_b.toString());
  });

  // Python: edge_ids, nodes_ids, claims_ids = set(), set(), set()
  const edge_ids = new Set<number>();
  const nodes_ids = new Set<number>();
  const claims_ids = new Set<number>();
  const sorted_edges: Record<string, any>[] = [];
  const sorted_nodes: Record<string, any>[] = [];
  const sorted_claims: Record<string, any>[] = [];
  let context_string = '';

  for (const edge of edges) {
    const source = edge[edge_source_column];
    const target = edge[edge_target_column];

    // Python: for node in [node_details.get(source), node_details.get(target)]:
    for (const node_name of [source, target]) {
      const node = node_details[node_name];
      if (node && !nodes_ids.has(node[schemas.SHORT_ID])) {
        nodes_ids.add(node[schemas.SHORT_ID]);
        sorted_nodes.push(node);
      }
    }

    // Python: for claims in [claim_details.get(source), claim_details.get(target)]:
    for (const node_name of [source, target]) {
      const claims = claim_details[node_name];
      if (claims) {
        for (const claim of claims) {
          if (!claims_ids.has(claim[schemas.SHORT_ID])) {
            claims_ids.add(claim[schemas.SHORT_ID]);
            sorted_claims.push(claim);
          }
        }
      }
    }

    // Python: if edge[schemas.SHORT_ID] not in edge_ids:
    if (!edge_ids.has(edge[schemas.SHORT_ID])) {
      edge_ids.add(edge[schemas.SHORT_ID]);
      sorted_edges.push(edge);
    }

    // Python: new_context_string = _get_context_string(...)
    const new_context_string = _get_context_string(
      sorted_nodes,
      sorted_edges,
      sorted_claims,
      sub_community_reports
    );

    // Python: if max_context_tokens and num_tokens(new_context_string) > max_context_tokens:
    if (max_context_tokens && numTokens(new_context_string) > max_context_tokens) {
      break;
    }
    context_string = new_context_string;
  }

  // Python: return context_string or _get_context_string(...)
  return context_string || _get_context_string(
    sorted_nodes,
    sorted_edges,
    sorted_claims,
    sub_community_reports
  );
}

/**
 * Calculate context using parallelization if enabled.
 * Matches the Python parallel_sort_context_batch function exactly.
 */
export function parallel_sort_context_batch(
  community_df: DataFrame,
  max_context_tokens: number,
  parallel: boolean = false
): DataFrame {
  let context_strings: string[];

  if (parallel) {
    // Python: with ThreadPoolExecutor(max_workers=None) as executor:
    // Use Promise.all for parallel execution in TypeScript
    const all_contexts = community_df.data.map(row => row[schemas.ALL_CONTEXT]);
    const promises = all_contexts.map(context =>
      Promise.resolve(sort_context(context, undefined, max_context_tokens))
    );

    // Note: This would need to be async in real implementation
    context_strings = all_contexts.map(context_list =>
      sort_context(context_list, undefined, max_context_tokens)
    );
  } else {
    // Python: community_df[schemas.CONTEXT_STRING] = community_df[schemas.ALL_CONTEXT].apply(...)
    const all_contexts = community_df.data.map(row => row[schemas.ALL_CONTEXT]);
    context_strings = all_contexts.map(context_list =>
      sort_context(context_list, undefined, max_context_tokens)
    );
  }

  // Python: community_df[schemas.CONTEXT_STRING] = context_strings
  const result_data = community_df.data.map((row, index) => ({
    ...row,
    [schemas.CONTEXT_STRING]: context_strings[index]
  }));

  // Python: community_df[schemas.CONTEXT_SIZE] = community_df[schemas.CONTEXT_STRING].apply(num_tokens)
  result_data.forEach(row => {
    row[schemas.CONTEXT_SIZE] = numTokens(row[schemas.CONTEXT_STRING]);
  });

  // Python: community_df[schemas.CONTEXT_EXCEED_FLAG] = (community_df[schemas.CONTEXT_SIZE] > max_context_tokens)
  result_data.forEach(row => {
    row[schemas.CONTEXT_EXCEED_FLAG] = row[schemas.CONTEXT_SIZE] > max_context_tokens;
  });

  const result_columns = [...community_df.columns];
  if (!result_columns.includes(schemas.CONTEXT_STRING)) {
    result_columns.push(schemas.CONTEXT_STRING);
  }
  if (!result_columns.includes(schemas.CONTEXT_SIZE)) {
    result_columns.push(schemas.CONTEXT_SIZE);
  }
  if (!result_columns.includes(schemas.CONTEXT_EXCEED_FLAG)) {
    result_columns.push(schemas.CONTEXT_EXCEED_FLAG);
  }

  return { columns: result_columns, data: result_data };
}

// Compatibility exports for existing code
export const sortContext = sort_context;
export const parallelSortContextBatch = parallel_sort_context_batch;
