/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * All the steps to transform final entities.
 */

import { v4 as uuidv4 } from 'uuid';
import { DataFrame } from '../../data-model/types';
import { ENTITIES_FINAL_COLUMNS } from '../../data-model/schemas';
import { EmbedGraphConfig } from '../../config/models/embed-graph-config';
import { createGraph } from './create-graph';
import { computeDegree } from './compute-degree';
import { join } from '../utils/dataframes';

/**
 * All the steps to transform final entities.
 * @param entities - DataFrame containing entities data
 * @param relationships - DataFrame containing relationships data
 * @param embedConfig - Optional embed graph configuration
 * @param layoutEnabled - Whether layout is enabled
 * @returns Finalized entities DataFrame
 */
export function finalizeEntities(
    entities: DataFrame,
    relationships: DataFrame,
    embedConfig?: EmbedGraphConfig,
    layoutEnabled: boolean = false
): DataFrame {
    // Create graph and compute degrees
    const graph = createGraph(relationships, ['weight']);
    
    // Compute graph embeddings if enabled
    let graphEmbeddings: DataFrame | null = null;
    if (embedConfig?.enabled) {
        // In a real implementation, you would call embedGraph here
        // graphEmbeddings = embedGraph(graph, embedConfig);
        console.warn('Graph embedding not fully implemented');
    }

    // Layout graph (simplified implementation)
    const layout = layoutGraph(graph, layoutEnabled, graphEmbeddings);
    
    // Compute degrees
    const degrees = computeDegree(graph);

    // Merge entities with layout and degrees
    let finalEntities = join(entities, layout, 'title', 'left');
    finalEntities = join(finalEntities, degrees, 'title', 'left');

    // Remove duplicates based on title
    const uniqueEntities = new Map<string, any>();
    finalEntities.data.forEach(row => {
        if (row.title && !uniqueEntities.has(row.title)) {
            uniqueEntities.set(row.title, row);
        }
    });

    // Filter to entities with valid titles
    const validEntitiesData = Array.from(uniqueEntities.values())
        .filter(row => row.title != null);

    // Fill missing degrees and transform data
    const transformedData = validEntitiesData.map((row, index) => ({
        ...row,
        degree: row.degree != null ? parseInt(String(row.degree), 10) : 0,
        human_readable_id: index,
        id: uuidv4()
    }));

    // Filter to final columns
    const filteredData = transformedData.map(row => {
        const newRow: Record<string, any> = {};
        ENTITIES_FINAL_COLUMNS.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });

    return {
        columns: ENTITIES_FINAL_COLUMNS,
        data: filteredData
    };
}

/**
 * Layout graph (simplified implementation).
 * In a real implementation, this would use proper graph layout algorithms.
 */
function layoutGraph(
    graph: any,
    layoutEnabled: boolean,
    embeddings?: DataFrame | null
): DataFrame {
    const nodes = Array.from(graph.nodes.keys());
    
    const layoutData = nodes.map(nodeId => ({
        label: nodeId,
        x: embeddings ? Math.random() * 100 : 0, // Simplified layout
        y: embeddings ? Math.random() * 100 : 0
    }));

    return {
        columns: ['label', 'x', 'y'],
        data: layoutData
    };
}