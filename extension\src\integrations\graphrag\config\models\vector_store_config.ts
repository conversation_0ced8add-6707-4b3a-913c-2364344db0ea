// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the vector store configuration.
 */

import { vectorStoreDefaults } from '../defaults.js';
import { VectorStoreType } from '../../vector_stores/factory.js';

/**
 * The configuration section for Vector Store.
 */
export interface VectorStoreConfig {
  /**
   * The vector store type to use.
   */
  type: string;

  /**
   * The database URI to use.
   */
  dbUri?: string;

  /**
   * The database URL when type == azure_ai_search.
   */
  url?: string;

  /**
   * The database API key when type == azure_ai_search.
   */
  apiKey?: string;

  /**
   * The database audience when type == azure_ai_search.
   */
  audience?: string;

  /**
   * The container name to use.
   */
  containerName: string;

  /**
   * The database name to use when type == cosmos_db.
   */
  databaseName?: string;

  /**
   * Overwrite the existing data.
   */
  overwrite: boolean;
}

/**
 * Validate the database URI.
 */
export function validateDbUri(config: VectorStoreConfig): void {
  if (config.type === VectorStoreType.LanceDB && (!config.dbUri || config.dbUri.trim() === '')) {
    config.dbUri = vectorStoreDefaults.dbUri;
  }

  if (config.type !== VectorStoreType.LanceDB && config.dbUri && config.dbUri.trim() !== '') {
    throw new Error(
      'vector_store.db_uri is only used when vector_store.type == lancedb. Please rerun `graphrag init` and select the correct vector store type.'
    );
  }
}

/**
 * Validate the database URL.
 */
export function validateUrl(config: VectorStoreConfig): void {
  if (config.type === VectorStoreType.AzureAISearch && (!config.url || config.url.trim() === '')) {
    throw new Error(
      'vector_store.url is required when vector_store.type == azure_ai_search. Please rerun `graphrag init` and select the correct vector store type.'
    );
  }

  if (config.type === VectorStoreType.CosmosDB && (!config.url || config.url.trim() === '')) {
    throw new Error(
      'vector_store.url is required when vector_store.type == cosmos_db. Please rerun `graphrag init` and select the correct vector store type.'
    );
  }

  if (config.type === VectorStoreType.LanceDB && config.url && config.url.trim() !== '') {
    throw new Error(
      'vector_store.url is only used when vector_store.type == azure_ai_search or vector_store.type == cosmos_db. Please rerun `graphrag init` and select the correct vector store type.'
    );
  }
}

/**
 * Create a VectorStoreConfig with default values.
 */
export function createVectorStoreConfig(config: Partial<VectorStoreConfig> = {}): VectorStoreConfig {
  const result: VectorStoreConfig = {
    type: config.type ?? vectorStoreDefaults.type,
    dbUri: config.dbUri,
    url: config.url ?? vectorStoreDefaults.url,
    apiKey: config.apiKey ?? vectorStoreDefaults.apiKey,
    audience: config.audience ?? vectorStoreDefaults.audience,
    containerName: config.containerName ?? vectorStoreDefaults.containerName,
    databaseName: config.databaseName ?? vectorStoreDefaults.databaseName,
    overwrite: config.overwrite ?? vectorStoreDefaults.overwrite,
  };

  // Validate the configuration
  validateDbUri(result);
  validateUrl(result);

  return result;
}
