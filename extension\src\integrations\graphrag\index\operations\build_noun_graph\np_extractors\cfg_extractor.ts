// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * CFG-based noun phrase extractor.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { BaseNounPhraseExtractor, SpacyDoc, SpacyNLP } from './base.js';
import { hasValidTokenLength, isCompound, isValidEntity } from './np_validator.js';

/**
 * CFG-based noun phrase extractor.
 */
export class CFGNounPhraseExtractor extends BaseNounPhraseExtractor {
  private includeNamedEntities: boolean;
  private excludeEntityTags: string[];
  private excludePosTags: string[];
  private nounPhraseGrammars: Record<string, string>;
  private nounPhraseTags: string[];
  private nlp: SpacyNLP;

  constructor(
    modelName: string,
    maxWordLength: number,
    includeNamedEntities: boolean,
    excludeEntityTags: string[],
    excludePosTags: string[],
    excludeNouns: string[],
    wordDelimiter: string,
    nounPhraseGrammars: Record<string, string>,
    nounPhraseTags: string[]
  ) {
    super(modelName, maxWordLength, excludeNouns, wordDelimiter);
    
    this.includeNamedEntities = includeNamedEntities;
    this.excludeEntityTags = excludeEntityTags;
    this.excludePosTags = excludePosTags;
    this.nounPhraseGrammars = nounPhraseGrammars;
    this.nounPhraseTags = nounPhraseTags;

    // Load SpaCy model with appropriate exclusions
    if (!includeNamedEntities) {
      this.nlp = this.loadSpacyModel(modelName, ['lemmatizer', 'parser', 'ner']);
    } else {
      this.nlp = this.loadSpacyModel(modelName, ['lemmatizer', 'parser']);
    }
  }

  /**
   * Extract noun phrases from text. Noun phrases may include named entities and noun chunks, 
   * which are filtered based on some heuristics.
   */
  extract(text: string): string[] {
    const doc = this.nlp(text);
    const filteredNounPhrases = new Set<string>();

    if (this.includeNamedEntities) {
      // Extract noun chunks + entities then filter overlapping spans
      const entities = doc.ents
        .filter(ent => !this.excludeEntityTags.includes(ent.label_))
        .map(ent => [ent.text, ent.label_] as [string, string]);
      
      const entityTexts = new Set(entities.map(ent => ent[0]));
      const cfgMatches = this.extractCfgMatches(doc);
      const nounPhrases = [
        ...entities,
        ...cfgMatches.filter(np => !entityTexts.has(np[0]))
      ];

      // Filter noun phrases based on heuristics
      const taggedNounPhrases = nounPhrases.map(np => this.tagNounPhrases(np, entityTexts));
      
      for (const taggedNp of taggedNounPhrases) {
        if (taggedNp.isValidEntity || 
            ((taggedNp.cleanedTokens.length > 1 || taggedNp.hasCompoundWords) && 
             taggedNp.hasValidTokens)) {
          filteredNounPhrases.add(taggedNp.cleanedText);
        }
      }
    } else {
      const nounPhrases = this.extractCfgMatches(doc);
      const taggedNounPhrases = nounPhrases.map(np => this.tagNounPhrases(np));
      
      for (const taggedNp of taggedNounPhrases) {
        if (taggedNp.hasProperNouns || 
            ((taggedNp.cleanedTokens.length > 1 || taggedNp.hasCompoundWords) && 
             taggedNp.hasValidTokens)) {
          filteredNounPhrases.add(taggedNp.cleanedText);
        }
      }
    }

    return Array.from(filteredNounPhrases);
  }

  /**
   * Return noun phrases that match a given context-free grammar.
   * This method implements the same CFG matching logic as the Python version.
   */
  private extractCfgMatches(doc: SpacyDoc): Array<[string, string]> {
    // Filter tokens based on POS tags and other criteria
    let taggedTokens = doc.tokens
      .filter(token =>
        !this.excludePosTags.includes(token.pos_) &&
        !token.is_space &&
        token.text !== '-'
      )
      .map(token => [token.text, token.pos_] as [string, string]);

    // Apply CFG rules iteratively until no more merges are possible
    let merge = true;
    while (merge) {
      merge = false;
      for (let index = 0; index < taggedTokens.length - 1; index++) {
        const first = taggedTokens[index];
        const second = taggedTokens[index + 1];
        const key = `${first[1]},${second[1]}`;
        const value = this.nounPhraseGrammars[key];

        if (value) {
          // Found a matching pattern, merge the two tokens
          merge = true;
          taggedTokens.splice(index, 2);
          const match = `${first[0]}${this.wordDelimiter}${second[0]}`;
          const pos = value;
          taggedTokens.splice(index, 0, [match, pos]);
          break;
        }
      }
    }

    // Return only tokens that match the specified noun phrase tags
    return taggedTokens.filter(t => this.nounPhraseTags.includes(t[1]));
  }

  /**
   * Extract attributes of a noun chunk, to be used for filtering.
   */
  private tagNounPhrases(
    nounChunk: [string, string], 
    entities?: Set<string>
  ): {
    cleanedTokens: string[];
    cleanedText: string;
    isValidEntity: boolean;
    hasProperNouns: boolean;
    hasCompoundWords: boolean;
    hasValidTokens: boolean;
  } {
    const tokens = nounChunk[0].split(this.wordDelimiter);
    const cleanedTokens = tokens.filter(token => 
      !this.excludeNouns.includes(token.toUpperCase())
    );

    let hasValidEntity = false;
    if (entities && entities.has(nounChunk[0])) {
      hasValidEntity = isValidEntity(nounChunk, cleanedTokens);
    }

    return {
      cleanedTokens,
      cleanedText: cleanedTokens
        .join(this.wordDelimiter)
        .replace(/\n/g, '')
        .toUpperCase(),
      isValidEntity: hasValidEntity,
      hasProperNouns: nounChunk[1] === 'PROPN',
      hasCompoundWords: isCompound(cleanedTokens),
      hasValidTokens: hasValidTokenLength(cleanedTokens, this.maxWordLength),
    };
  }

  /**
   * Return string representation of the extractor, used for cache key generation.
   */
  toString(): string {
    return `cfg_${this.modelName}_${this.maxWordLength}_${this.includeNamedEntities}_${this.excludeEntityTags}_${this.excludePosTags}_${this.excludeNouns}_${this.wordDelimiter}_${JSON.stringify(this.nounPhraseGrammars)}_${this.nounPhraseTags}`;
  }

  /**
   * Mock implementation of SpaCy model loading
   */
  private loadSpacyModel(_modelName: string, _exclude: string[]): SpacyNLP {
    // This is a mock implementation - in a real scenario, you'd integrate with a JS NLP library
    return (text: string): SpacyDoc => {
      // Mock implementation
      return {
        text,
        ents: [],
        tokens: text.split(/\s+/).map(word => ({
          text: word,
          pos_: 'NOUN', // Mock POS tag
          lemma_: word.toLowerCase(),
          is_space: false,
          is_punct: false,
          ent_type_: ''
        })),
        noun_chunks: []
      };
    };
  }
}
