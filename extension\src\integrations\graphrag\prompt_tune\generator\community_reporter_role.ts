// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Generate a community reporter role for community summarization.
 */

import { ChatModel } from '../../language_model/protocol/base';
import { GENERATE_COMMUNITY_REPORTER_ROLE_PROMPT } from '../prompt/community_reporter_role';

/**
 * Generate an LLM persona to use for GraphRAG prompts.
 * 
 * @param model - The LLM to use for generation
 * @param domain - The domain to generate a persona for
 * @param persona - The persona to generate a role for
 * @param docs - The domain to generate a persona for
 * @returns The generated domain prompt response
 */
export async function generateCommunityReporterRole(
    model: ChatModel,
    domain: string,
    persona: string,
    docs: string | string[]
): Promise<string> {
    const docsStr = Array.isArray(docs) ? docs.join(' ') : docs;
    const domainPrompt = GENERATE_COMMUNITY_REPORTER_ROLE_PROMPT
        .replace('{domain}', domain)
        .replace('{persona}', persona)
        .replace('{input_text}', docsStr);

    const response = await model.achat(domainPrompt);

    return response.output.content || '';
}

/**
 * Generate community reporter role with validation.
 */
export async function generateCommunityReporterRoleSafe(
    model: ChatModel,
    domain: string,
    persona: string,
    docs: string | string[]
): Promise<{ role?: string; success: boolean; errors: string[] }> {
    const errors: string[] = [];

    // Validate inputs
    if (!domain.trim()) {
        errors.push('Domain cannot be empty');
    }

    if (!persona.trim()) {
        errors.push('Persona cannot be empty');
    }

    const docsArray = Array.isArray(docs) ? docs : [docs];
    if (docsArray.length === 0 || docsArray.every(doc => !doc.trim())) {
        errors.push('Documents cannot be empty');
    }

    if (errors.length > 0) {
        return { success: false, errors };
    }

    try {
        const role = await generateCommunityReporterRole(model, domain, persona, docs);
        
        if (!role.trim()) {
            errors.push('Generated role is empty');
            return { success: false, errors };
        }

        return {
            role,
            success: true,
            errors: []
        };
    } catch (error) {
        errors.push(`Failed to generate role: ${error instanceof Error ? error.message : String(error)}`);
        return { success: false, errors };
    }
}

/**
 * Validate community reporter role parameters.
 */
export function validateCommunityReporterRoleParams(
    domain: string,
    persona: string,
    docs: string | string[]
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!domain || domain.trim() === '') {
        errors.push('Domain cannot be empty');
    }

    if (!persona || persona.trim() === '') {
        errors.push('Persona cannot be empty');
    }

    const docsArray = Array.isArray(docs) ? docs : [docs];
    if (docsArray.length === 0) {
        errors.push('Documents array cannot be empty');
    } else if (docsArray.every(doc => !doc || doc.trim() === '')) {
        errors.push('All documents cannot be empty');
    }

    if (domain.length > 1000) {
        errors.push('Domain is too long (max 1000 characters)');
    }

    if (persona.length > 2000) {
        errors.push('Persona is too long (max 2000 characters)');
    }

    const totalDocsLength = docsArray.join(' ').length;
    if (totalDocsLength > 10000) {
        errors.push('Documents are too long (max 10000 characters total)');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Generate community reporter role with retry logic.
 */
export async function generateCommunityReporterRoleWithRetry(
    model: ChatModel,
    domain: string,
    persona: string,
    docs: string | string[],
    maxRetries: number = 3
): Promise<{ role?: string; success: boolean; errors: string[]; attempts: number }> {
    let attempts = 0;
    const allErrors: string[] = [];

    while (attempts < maxRetries) {
        attempts++;
        
        try {
            const result = await generateCommunityReporterRoleSafe(model, domain, persona, docs);
            
            if (result.success) {
                return {
                    role: result.role,
                    success: true,
                    errors: [],
                    attempts
                };
            } else {
                allErrors.push(...result.errors.map(err => `Attempt ${attempts}: ${err}`));
            }
        } catch (error) {
            allErrors.push(`Attempt ${attempts}: ${error instanceof Error ? error.message : String(error)}`);
        }

        // Wait before retry (exponential backoff)
        if (attempts < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempts) * 1000));
        }
    }

    return {
        success: false,
        errors: allErrors,
        attempts
    };
}
