// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Context builders for graphs.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../../data_model/types.js';
import * as schemas from '../../../../data_model/schemas.js';
import { WorkflowCallbacks } from '../../../../callbacks/workflow_callbacks.js';
import { buildMixedContext } from '../build_mixed_context.js';
import { parallelSortContextBatch, sortContext } from './sort_context.js';
import { getLevels } from '../utils.js';
import {
  antijoin,
  dropColumns,
  join,
  select,
  transformSeries,
  union,
  whereColumnEquals,
} from '../../../utils/dataframes.js';
import { progressIterable } from '../../../../logger/progress.js';
import { numTokens } from '../../../../query/llm/text_utils.js';

const logger = console;

/**
 * Concatenate multiple DataFrames.
 * Helper function to match pandas concat behavior.
 */
function concatDataFrames(dfs: DataFrame[]): DataFrame {
  if (dfs.length === 0) {
    return { columns: [], data: [] };
  }

  const allColumns = new Set<string>();
  dfs.forEach(df => df.columns.forEach(col => allColumns.add(col)));
  const columns = Array.from(allColumns);

  const data: Record<string, any>[] = [];
  dfs.forEach(df => {
    df.data.forEach(row => {
      const newRow: Record<string, any> = {};
      columns.forEach(col => {
        newRow[col] = row[col] !== undefined ? row[col] : null;
      });
      data.push(newRow);
    });
  });

  return { columns, data };
}

/**
 * Filter DataFrame rows based on a predicate.
 */
function filterDataFrame(df: DataFrame, predicate: (row: Record<string, any>) => boolean): DataFrame {
  const filteredData = df.data.filter(predicate);
  return { columns: df.columns, data: filteredData };
}

/**
 * Get column values from DataFrame.
 */
function getColumnValues(df: DataFrame, column: string): any[] {
  return df.data.map(row => row[column]);
}

/**
 * Select specific columns from DataFrame.
 */
function selectColumns(df: DataFrame, columns: string[]): DataFrame {
  const data = df.data.map(row => {
    const newRow: Record<string, any> = {};
    columns.forEach(col => {
      newRow[col] = row[col];
    });
    return newRow;
  });
  return { columns, data };
}

/**
 * Prep communities for report generation.
 * Matches the Python build_local_context function exactly.
 */
export function build_local_context(
  nodes: DataFrame,
  edges: DataFrame,
  claims: DataFrame,
  callbacks: WorkflowCallbacks,
  max_context_tokens: number = 16000
): DataFrame {
  const levels = getLevels(nodes, schemas.COMMUNITY_LEVEL);
  const dfs: DataFrame[] = [];

  for (const level of progressIterable(levels, callbacks.progress, levels.length)) {
    const communities_at_level_df = _prepare_reports_at_level(
      nodes,
      edges,
      claims,
      level,
      max_context_tokens
    );

    // Python: communities_at_level_df.loc[:, schemas.COMMUNITY_LEVEL] = level
    communities_at_level_df.data.forEach(row => {
      row[schemas.COMMUNITY_LEVEL] = level;
    });
    dfs.push(communities_at_level_df);
  }

  // Python: return pd.concat(dfs)
  return concatDataFrames(dfs);
}

/**
 * Prepare reports at a given level.
 */
function _prepare_reports_at_level(
  node_df: DataFrame,
  edge_df: DataFrame,
  claim_df: DataFrame | undefined,
  level: number,
  max_context_tokens: number = 16000
): DataFrame {
  // Python: level_node_df = node_df[node_df[schemas.COMMUNITY_LEVEL] == level]
  const level_node_df = filterDataFrame(node_df, row => row[schemas.COMMUNITY_LEVEL] === level);
  logger.log(`Number of nodes at level=${level} => ${level_node_df.data.length}`);
  const nodes_set = new Set(getColumnValues(level_node_df, schemas.TITLE));

  // Python: level_edge_df = edge_df[...]
  let level_edge_df = filterDataFrame(edge_df, row =>
    nodes_set.has(row[schemas.EDGE_SOURCE]) && nodes_set.has(row[schemas.EDGE_TARGET])
  );

  // Python: level_edge_df.loc[:, schemas.EDGE_DETAILS] = level_edge_df.loc[...].to_dict(orient="records")
  level_edge_df.data.forEach(row => {
    row[schemas.EDGE_DETAILS] = {
      [schemas.SHORT_ID]: row[schemas.SHORT_ID],
      [schemas.EDGE_SOURCE]: row[schemas.EDGE_SOURCE],
      [schemas.EDGE_TARGET]: row[schemas.EDGE_TARGET],
      [schemas.DESCRIPTION]: row[schemas.DESCRIPTION],
      [schemas.EDGE_DEGREE]: row[schemas.EDGE_DEGREE],
    };
  });

  let level_claim_df: DataFrame = { columns: [], data: [] };
  if (claim_df && claim_df.data.length > 0) {
    level_claim_df = filterDataFrame(claim_df, row =>
      nodes_set.has(row[schemas.CLAIM_SUBJECT])
    );
  }

  // Python: source_edges = level_edge_df.groupby(schemas.EDGE_SOURCE).agg({schemas.EDGE_DETAILS: "first"}).reset_index()
  const source_edges_map = new Map<string, any>();
  level_edge_df.data.forEach(row => {
    const source = row[schemas.EDGE_SOURCE];
    if (!source_edges_map.has(source)) {
      source_edges_map.set(source, row[schemas.EDGE_DETAILS]);
    }
  });

  const target_edges_map = new Map<string, any>();
  level_edge_df.data.forEach(row => {
    const target = row[schemas.EDGE_TARGET];
    if (!target_edges_map.has(target)) {
      target_edges_map.set(target, row[schemas.EDGE_DETAILS]);
    }
  });

  // Python: merged_node_df = level_node_df.merge(source_edges, on=schemas.TITLE, how="left").merge(target_edges, on=schemas.TITLE, how="left")
  const merged_node_data = level_node_df.data.map(row => {
    const title = row[schemas.TITLE];
    const source_edge_details = source_edges_map.get(title);
    const target_edge_details = target_edges_map.get(title);

    return {
      ...row,
      [`${schemas.EDGE_DETAILS}_x`]: source_edge_details,
      [`${schemas.EDGE_DETAILS}_y`]: target_edge_details,
    };
  });

  // Python: merged_node_df.loc[:, schemas.EDGE_DETAILS] = merged_node_df.loc[:, f"{schemas.EDGE_DETAILS}_x"].combine_first(merged_node_df.loc[:, f"{schemas.EDGE_DETAILS}_y"])
  merged_node_data.forEach(row => {
    row[schemas.EDGE_DETAILS] = row[`${schemas.EDGE_DETAILS}_x`] || row[`${schemas.EDGE_DETAILS}_y`] || [];
    delete row[`${schemas.EDGE_DETAILS}_x`];
    delete row[`${schemas.EDGE_DETAILS}_y`];
  });

  // Python: merged_node_df = merged_node_df.groupby([...]).agg({...}).reset_index()
  const community_groups = new Map<string, any[]>();
  merged_node_data.forEach(row => {
    const key = `${row[schemas.TITLE]}_${row[schemas.COMMUNITY_ID]}_${row[schemas.COMMUNITY_LEVEL]}_${row[schemas.NODE_DEGREE]}`;
    if (!community_groups.has(key)) {
      community_groups.set(key, []);
    }
    community_groups.get(key)!.push(row);
  });

  const aggregated_data: any[] = [];
  community_groups.forEach((rows, key) => {
    const first_row = rows[0];
    const edge_details_list = rows.map(r => r[schemas.EDGE_DETAILS]).filter(ed => ed && ed.length > 0).flat();

    aggregated_data.push({
      [schemas.TITLE]: first_row[schemas.TITLE],
      [schemas.COMMUNITY_ID]: first_row[schemas.COMMUNITY_ID],
      [schemas.COMMUNITY_LEVEL]: first_row[schemas.COMMUNITY_LEVEL],
      [schemas.NODE_DEGREE]: first_row[schemas.NODE_DEGREE],
      [schemas.NODE_DETAILS]: first_row[schemas.NODE_DETAILS],
      [schemas.EDGE_DETAILS]: edge_details_list,
    });
  });

  // Python: if claim_df is not None: merged_node_df = merged_node_df.merge(...)
  if (claim_df && claim_df.data.length > 0) {
    const claim_map = new Map<string, any>();
    level_claim_df.data.forEach(row => {
      claim_map.set(row[schemas.CLAIM_SUBJECT], row[schemas.CLAIM_DETAILS]);
    });

    aggregated_data.forEach(row => {
      row[schemas.CLAIM_DETAILS] = claim_map.get(row[schemas.TITLE]) || [];
    });
  }

  // Python: merged_node_df[schemas.ALL_CONTEXT] = merged_node_df.loc[:, [...].to_dict(orient="records")
  aggregated_data.forEach(row => {
    const context_obj: any = {
      [schemas.TITLE]: row[schemas.TITLE],
      [schemas.NODE_DEGREE]: row[schemas.NODE_DEGREE],
      [schemas.NODE_DETAILS]: row[schemas.NODE_DETAILS],
      [schemas.EDGE_DETAILS]: row[schemas.EDGE_DETAILS],
    };

    if (claim_df && claim_df.data.length > 0) {
      context_obj[schemas.CLAIM_DETAILS] = row[schemas.CLAIM_DETAILS];
    }

    row[schemas.ALL_CONTEXT] = context_obj;
  });

  // Python: community_df = merged_node_df.groupby(schemas.COMMUNITY_ID).agg({schemas.ALL_CONTEXT: list}).reset_index()
  const community_context_map = new Map<string, any[]>();
  aggregated_data.forEach(row => {
    const community_id = row[schemas.COMMUNITY_ID];
    if (!community_context_map.has(community_id)) {
      community_context_map.set(community_id, []);
    }
    community_context_map.get(community_id)!.push(row[schemas.ALL_CONTEXT]);
  });

  const community_data: any[] = [];
  community_context_map.forEach((contexts, community_id) => {
    community_data.push({
      [schemas.COMMUNITY_ID]: community_id,
      [schemas.ALL_CONTEXT]: contexts,
    });
  });

  const community_df: DataFrame = {
    columns: [schemas.COMMUNITY_ID, schemas.ALL_CONTEXT],
    data: community_data
  };

  // Python: return parallel_sort_context_batch(community_df, max_context_tokens=max_context_tokens)
  return parallelSortContextBatch(community_df, max_context_tokens);
}

/**
 * Prep context for each community in a given level.
 */
export function buildLevelContext(
  reportDf: DataFrame | undefined,
  communityHierarchyDf: DataFrame,
  localContextDf: DataFrame,
  level: number,
  maxContextTokens: number
): DataFrame {
  // Filter by community level
  const levelContextDf = localContextDf.filter(
    row => row[schemas.COMMUNITY_LEVEL] === level
  );

  // Filter valid and invalid contexts using boolean logic
  const validContextDf = levelContextDf.filter(
    row => !row[schemas.CONTEXT_EXCEED_FLAG]
  );
  const invalidContextDf = levelContextDf.filter(
    row => row[schemas.CONTEXT_EXCEED_FLAG]
  );

  // There is no report to substitute with, so we just trim the local context
  if (invalidContextDf.isEmpty()) {
    return validContextDf;
  }

  if (!reportDf || reportDf.isEmpty()) {
    const trimmedInvalidDf = invalidContextDf.withColumn(
      schemas.CONTEXT_STRING,
      sortAndTrimContext(invalidContextDf, maxContextTokens)
    );

    const updatedInvalidDf = trimmedInvalidDf
      .withColumn(
        schemas.CONTEXT_SIZE,
        trimmedInvalidDf.getColumn(schemas.CONTEXT_STRING).map(numTokens)
      )
      .withColumn(schemas.CONTEXT_EXCEED_FLAG, false);

    return union(validContextDf, updatedInvalidDf);
  }

  const levelContextDfFiltered = antijoinReports(levelContextDf, reportDf);

  // For each invalid context, try to substitute with sub-community reports
  const subContextDf = getSubcontextDf(level + 1, reportDf, localContextDf);
  const communityDf = getCommunityDf(
    level,
    invalidContextDf,
    subContextDf,
    communityHierarchyDf,
    maxContextTokens
  );

  // Handle any remaining invalid records
  const remainingDf = antijoinReports(invalidContextDf, communityDf)
    .withColumn(
      schemas.CONTEXT_STRING,
      sortAndTrimContext(invalidContextDf, maxContextTokens)
    );

  const result = union(validContextDf, communityDf, remainingDf)
    .withColumn(
      schemas.CONTEXT_SIZE,
      (df: DataFrame) => df.getColumn(schemas.CONTEXT_STRING).map(numTokens)
    )
    .withColumn(schemas.CONTEXT_EXCEED_FLAG, false);

  return result;
}

// Helper functions
function dropCommunityLevel(df: DataFrame): DataFrame {
  return dropColumns(df, schemas.COMMUNITY_LEVEL);
}

function atLevel(level: number, df: DataFrame): DataFrame {
  return whereColumnEquals(df, schemas.COMMUNITY_LEVEL, level);
}

function antijoinReports(df: DataFrame, reports: DataFrame): DataFrame {
  return antijoin(df, reports, schemas.COMMUNITY_ID);
}

function sortAndTrimContext(df: DataFrame, maxContextTokens: number): any[] {
  const series = df.getColumn(schemas.ALL_CONTEXT);
  return transformSeries(
    series,
    (x: any) => sortContext(x, maxContextTokens)
  );
}

function buildMixedContextSeries(df: DataFrame, maxContextTokens: number): any[] {
  const series = df.getColumn(schemas.ALL_CONTEXT);
  return transformSeries(
    series,
    (x: any) => buildMixedContext(x, maxContextTokens)
  );
}

function getSubcontextDf(
  level: number,
  reportDf: DataFrame,
  localContextDf: DataFrame
): DataFrame {
  const subReportDf = dropCommunityLevel(atLevel(level, reportDf));
  let subContextDf = atLevel(level, localContextDf);
  subContextDf = join(subContextDf, subReportDf, schemas.COMMUNITY_ID);
  return subContextDf.rename({ [schemas.COMMUNITY_ID]: schemas.SUB_COMMUNITY });
}

function getCommunityDf(
  level: number,
  invalidContextDf: DataFrame,
  subContextDf: DataFrame,
  communityHierarchyDf: DataFrame,
  maxContextTokens: number
): DataFrame {
  // Collect all sub communities' contexts for each community
  const communityDf = dropCommunityLevel(atLevel(level, communityHierarchyDf));
  const invalidCommunityIds = select(invalidContextDf, schemas.COMMUNITY_ID);
  const subcontextSelection = select(
    subContextDf,
    schemas.SUB_COMMUNITY,
    schemas.FULL_CONTENT,
    schemas.ALL_CONTEXT,
    schemas.CONTEXT_SIZE
  );

  const invalidCommunities = join(
    communityDf,
    invalidCommunityIds,
    schemas.COMMUNITY_ID,
    'inner'
  );

  let resultDf = join(invalidCommunities, subcontextSelection, schemas.SUB_COMMUNITY);

  resultDf = resultDf.withColumn(
    schemas.ALL_CONTEXT,
    resultDf.toRecords().map(row => ({
      [schemas.SUB_COMMUNITY]: row[schemas.SUB_COMMUNITY],
      [schemas.ALL_CONTEXT]: row[schemas.ALL_CONTEXT],
      [schemas.FULL_CONTENT]: row[schemas.FULL_CONTENT],
      [schemas.CONTEXT_SIZE]: row[schemas.CONTEXT_SIZE],
    }))
  );

  resultDf = resultDf
    .groupBy(schemas.COMMUNITY_ID)
    .agg({ [schemas.ALL_CONTEXT]: 'collect_list' });

  return resultDf
    .withColumn(
      schemas.CONTEXT_STRING,
      buildMixedContextSeries(resultDf, maxContextTokens)
    )
    .withColumn(schemas.COMMUNITY_LEVEL, level);
}
