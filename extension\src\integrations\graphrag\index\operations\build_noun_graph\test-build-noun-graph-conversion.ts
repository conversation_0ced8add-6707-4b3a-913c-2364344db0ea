// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Comprehensive test file for GraphRAG build_noun_graph conversion from Python to TypeScript.
 * This file validates that all converted TypeScript code maintains functional parity
 * with the original Python implementation.
 */

import { DataFrame } from '../../../data_model/types.js';
import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { NoopPipelineCache } from '../../../cache/noop_pipeline_cache.js';
import { BaseNounPhraseExtractor } from './np_extractors/base.js';
import { RegexENNounPhraseExtractor } from './np_extractors/regex_extractor.js';
import { buildNounGraph } from './build_noun_graph.js';

/**
 * Test results interface.
 */
interface TestResult {
    name: string;
    passed: boolean;
    error?: string;
    details?: string;
}

/**
 * Mock noun phrase extractor for testing.
 */
class MockNounPhraseExtractor extends BaseNounPhraseExtractor {
    constructor() {
        super('mock', 15, [], ' ');
    }

    extract(text: string): string[] {
        // Simple mock extraction - split by spaces and filter capitalized words
        return text.split(/\s+/)
            .filter(word => /^[A-Z]/.test(word))
            .filter(word => word.length > 2);
    }

    toString(): string {
        return 'MockNounPhraseExtractor';
    }
}

/**
 * Test runner class for build_noun_graph conversion.
 */
class BuildNounGraphConversionTester {
    private results: TestResult[] = [];

    /**
     * Add a test result.
     */
    private addResult(name: string, passed: boolean, error?: string, details?: string): void {
        this.results.push({ name, passed, error, details });
    }

    /**
     * Test basic noun graph building functionality.
     */
    async testBasicNounGraphBuilding(): Promise<void> {
        try {
            // Create test data
            const textUnitDF: DataFrame = {
                columns: ['id', 'text'],
                data: [
                    { id: 'unit1', text: 'John Smith works at Microsoft Corporation.' },
                    { id: 'unit2', text: 'Microsoft Corporation develops software products.' },
                    { id: 'unit3', text: 'John Smith is a software engineer.' }
                ]
            };

            const extractor = new MockNounPhraseExtractor();
            const [nodesDF, edgesDF] = await buildNounGraph(
                textUnitDF,
                extractor,
                false, // Don't normalize edge weights for this test
                1 // Single thread for deterministic results
            );

            // Validate nodes structure
            const nodesValid = nodesDF.columns.includes('title') &&
                              nodesDF.columns.includes('frequency') &&
                              nodesDF.columns.includes('text_unit_ids') &&
                              nodesDF.data.length > 0;

            // Validate edges structure
            const edgesValid = edgesDF.columns.includes('source') &&
                              edgesDF.columns.includes('target') &&
                              edgesDF.columns.includes('weight') &&
                              edgesDF.columns.includes('text_unit_ids');

            const passed = nodesValid && edgesValid;
            this.addResult(
                'Basic Noun Graph Building',
                passed,
                undefined,
                `Nodes: ${nodesDF.data.length}, Edges: ${edgesDF.data.length}`
            );
        } catch (error) {
            this.addResult('Basic Noun Graph Building', false, error.message);
        }
    }

    /**
     * Test noun phrase extraction and node creation.
     */
    async testNodeExtraction(): Promise<void> {
        try {
            const textUnitDF: DataFrame = {
                columns: ['id', 'text'],
                data: [
                    { id: 'unit1', text: 'Apple Inc is a technology company.' },
                    { id: 'unit2', text: 'Apple Inc makes iPhone devices.' },
                    { id: 'unit3', text: 'Technology companies innovate constantly.' }
                ]
            };

            const extractor = new MockNounPhraseExtractor();
            const [nodesDF, _] = await buildNounGraph(textUnitDF, extractor, false, 1);

            // Check if nodes contain expected entities
            const nodesTitles = nodesDF.data.map((row: any) => row.title);
            const hasApple = nodesTitles.some((title: string) => title.includes('Apple'));
            const hasInc = nodesTitles.some((title: string) => title.includes('Inc'));

            // Check frequency calculation
            const appleNode = nodesDF.data.find((row: any) => row.title === 'Apple');
            const appleFrequency = appleNode ? appleNode.frequency : 0;

            const passed = hasApple && hasInc && appleFrequency >= 1;
            this.addResult(
                'Node Extraction',
                passed,
                undefined,
                `Found ${nodesTitles.length} unique nodes, Apple frequency: ${appleFrequency}`
            );
        } catch (error) {
            this.addResult('Node Extraction', false, error.message);
        }
    }

    /**
     * Test edge creation and co-occurrence logic.
     */
    async testEdgeCreation(): Promise<void> {
        try {
            const textUnitDF: DataFrame = {
                columns: ['id', 'text'],
                data: [
                    { id: 'unit1', text: 'John Smith works at Microsoft Corporation.' },
                    { id: 'unit2', text: 'John Smith and Bill Gates founded companies.' }
                ]
            };

            const extractor = new MockNounPhraseExtractor();
            const [_, edgesDF] = await buildNounGraph(textUnitDF, extractor, false, 1);

            // Check if edges are created between co-occurring entities
            const edgeExists = edgesDF.data.some((row: any) => 
                (row.source === 'John' && row.target === 'Smith') ||
                (row.source === 'Smith' && row.target === 'John') ||
                (row.source === 'John' && row.target === 'Microsoft') ||
                (row.source === 'Microsoft' && row.target === 'John')
            );

            // Check edge weight calculation
            const totalWeight = edgesDF.data.reduce((sum: number, row: any) => sum + row.weight, 0);

            const passed = edgeExists && totalWeight > 0;
            this.addResult(
                'Edge Creation',
                passed,
                undefined,
                `Created ${edgesDF.data.length} edges with total weight: ${totalWeight}`
            );
        } catch (error) {
            this.addResult('Edge Creation', false, error.message);
        }
    }

    /**
     * Test caching functionality.
     */
    async testCaching(): Promise<void> {
        try {
            const textUnitDF: DataFrame = {
                columns: ['id', 'text'],
                data: [
                    { id: 'unit1', text: 'Test caching functionality.' },
                    { id: 'unit2', text: 'Test caching functionality.' } // Same text
                ]
            };

            const cache = new NoopPipelineCache();
            const extractor = new MockNounPhraseExtractor();

            // First call
            const [nodesDF1, _] = await buildNounGraph(textUnitDF, extractor, false, 1, cache);
            
            // Second call with same data (should use cache)
            const [nodesDF2, _2] = await buildNounGraph(textUnitDF, extractor, false, 1, cache);

            // Results should be identical
            const sameNodeCount = nodesDF1.data.length === nodesDF2.data.length;
            const sameStructure = JSON.stringify(nodesDF1.columns) === JSON.stringify(nodesDF2.columns);

            const passed = sameNodeCount && sameStructure;
            this.addResult(
                'Caching Functionality',
                passed,
                undefined,
                `Cache test completed, consistent results: ${passed}`
            );
        } catch (error) {
            this.addResult('Caching Functionality', false, error.message);
        }
    }

    /**
     * Test with real regex extractor.
     */
    async testWithRealExtractor(): Promise<void> {
        try {
            const textUnitDF: DataFrame = {
                columns: ['id', 'text'],
                data: [
                    { id: 'unit1', text: 'Natural language processing is fascinating.' },
                    { id: 'unit2', text: 'Machine learning algorithms are powerful.' }
                ]
            };

            const extractor = new RegexENNounPhraseExtractor([], 15, ' ');
            const [nodesDF, edgesDF] = await buildNounGraph(textUnitDF, extractor, false, 1);

            const hasNodes = nodesDF.data.length > 0;
            const hasValidStructure = nodesDF.columns.includes('title') &&
                                    nodesDF.columns.includes('frequency') &&
                                    nodesDF.columns.includes('text_unit_ids');

            const passed = hasNodes && hasValidStructure;
            this.addResult(
                'Real Extractor Integration',
                passed,
                undefined,
                `Extracted ${nodesDF.data.length} nodes and ${edgesDF.data.length} edges`
            );
        } catch (error) {
            this.addResult('Real Extractor Integration', false, error.message);
        }
    }

    /**
     * Test edge normalization order (source < target).
     */
    async testEdgeNormalization(): Promise<void> {
        try {
            const textUnitDF: DataFrame = {
                columns: ['id', 'text'],
                data: [
                    { id: 'unit1', text: 'Zebra Animal lives in Africa.' }
                ]
            };

            const extractor = new MockNounPhraseExtractor();
            const [_, edgesDF] = await buildNounGraph(textUnitDF, extractor, false, 1);

            // Check that all edges have source <= target (lexicographically)
            const allNormalized = edgesDF.data.every((row: any) => 
                row.source <= row.target
            );

            this.addResult(
                'Edge Normalization',
                allNormalized,
                undefined,
                `All ${edgesDF.data.length} edges properly normalized`
            );
        } catch (error) {
            this.addResult('Edge Normalization', false, error.message);
        }
    }

    /**
     * Run all tests and return results.
     */
    async runAllTests(): Promise<TestResult[]> {
        console.log('🚀 Starting comprehensive GraphRAG build_noun_graph conversion tests...\n');

        await this.testBasicNounGraphBuilding();
        await this.testNodeExtraction();
        await this.testEdgeCreation();
        await this.testCaching();
        await this.testWithRealExtractor();
        await this.testEdgeNormalization();

        return this.results;
    }

    /**
     * Print test results.
     */
    printResults(): void {
        const passed = this.results.filter(r => r.passed).length;
        const total = this.results.length;

        console.log('\n📊 Test Results Summary:');
        console.log('========================');

        for (const result of this.results) {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.name}`);
            
            if (result.details) {
                console.log(`   Details: ${result.details}`);
            }
            
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        }

        console.log('\n📈 Overall Results:');
        console.log(`   Passed: ${passed}/${total} (${Math.round(passed/total*100)}%)`);
        
        if (passed === total) {
            console.log('🎉 All tests passed! TypeScript conversion is successful.');
        } else {
            console.log('⚠️  Some tests failed. Please review the conversion.');
        }
    }
}

/**
 * Run tests if this file is executed directly.
 */
async function runTests(): Promise<void> {
    const tester = new BuildNounGraphConversionTester();
    
    try {
        await tester.runAllTests();
        tester.printResults();
    } catch (error) {
        console.error('Test execution failed:', error);
    }
}

// Export for use in other modules
export { BuildNounGraphConversionTester, runTests };

// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}
