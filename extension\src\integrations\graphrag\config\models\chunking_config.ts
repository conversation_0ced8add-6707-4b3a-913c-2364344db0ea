// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the chunking configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';
import { ChunkStrategyType } from '../enums.js';

/**
 * Configuration section for chunking.
 */
export interface ChunkingConfig {
  /**
   * The chunk size to use.
   */
  size: number;

  /**
   * The chunk overlap to use.
   */
  overlap: number;

  /**
   * The chunk by columns to use.
   */
  groupByColumns: string[];

  /**
   * The chunking strategy to use.
   */
  strategy: ChunkStrategyType;

  /**
   * The encoding model to use.
   */
  encodingModel: string;

  /**
   * Prepend metadata into each chunk.
   */
  prependMetadata: boolean;

  /**
   * Count metadata in max tokens.
   */
  chunkSizeIncludesMetadata: boolean;
}

/**
 * Create a ChunkingConfig with default values.
 */
export function createChunkingConfig(config: Partial<ChunkingConfig> = {}): ChunkingConfig {
  return {
    size: config.size ?? graphragConfigDefaults.chunks.size,
    overlap: config.overlap ?? graphragConfigDefaults.chunks.overlap,
    groupByColumns: config.groupByColumns ?? graphragConfigDefaults.chunks.groupByColumns,
    strategy: config.strategy ?? graphragConfigDefaults.chunks.strategy,
    encodingModel: config.encodingModel ?? graphragConfigDefaults.chunks.encodingModel,
    prependMetadata: config.prependMetadata ?? graphragConfigDefaults.chunks.prependMetadata,
    chunkSizeIncludesMetadata: config.chunkSizeIncludesMetadata ?? graphragConfigDefaults.chunks.chunkSizeIncludesMetadata,
  };
}
