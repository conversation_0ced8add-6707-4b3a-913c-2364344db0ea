// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the embed graph configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';

/**
 * The configuration section for Node2Vec graph embedding.
 */
export interface EmbedGraphConfig {
  /**
   * A flag indicating whether to enable node2vec.
   */
  enabled: boolean;

  /**
   * The node2vec vector dimensions.
   */
  dimensions: number;

  /**
   * The node2vec number of walks.
   */
  numWalks: number;

  /**
   * The node2vec walk length.
   */
  walkLength: number;

  /**
   * The node2vec window size.
   */
  windowSize: number;

  /**
   * The node2vec iterations.
   */
  iterations: number;

  /**
   * The node2vec random seed.
   */
  randomSeed: number;

  /**
   * Whether to use the largest connected component.
   */
  useLcc: boolean;
}

/**
 * Create an EmbedGraphConfig with default values.
 */
export function createEmbedGraphConfig(config: Partial<EmbedGraphConfig> = {}): EmbedGraphConfig {
  return {
    enabled: config.enabled ?? graphragConfigDefaults.embedGraph.enabled,
    dimensions: config.dimensions ?? graphragConfigDefaults.embedGraph.dimensions,
    numWalks: config.numWalks ?? graphragConfigDefaults.embedGraph.numWalks,
    walkLength: config.walkLength ?? graphragConfigDefaults.embedGraph.walkLength,
    windowSize: config.windowSize ?? graphragConfigDefaults.embedGraph.windowSize,
    iterations: config.iterations ?? graphragConfigDefaults.embedGraph.iterations,
    randomSeed: config.randomSeed ?? graphragConfigDefaults.embedGraph.randomSeed,
    useLcc: config.useLcc ?? graphragConfigDefaults.embedGraph.useLcc,
  };
}
