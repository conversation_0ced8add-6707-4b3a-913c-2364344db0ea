// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing run method definition for OpenAI text embedding.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineCache } from '../../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../../callbacks/workflow_callbacks.js';
import { LanguageModelConfig } from '../../../../config/models/language_model_config.js';
import { TextEmbeddingResult } from './types.js';
import { TokenTextSplitter } from '../../../text_splitting/text_splitting.js';
import { isNull } from '../../../utils/is_null.js';
import { ModelManager } from '../../../../language_model/manager.js';
import { EmbeddingModel } from '../../../../language_model/protocol/base.js';
import { ProgressTicker, progressTicker } from '../../../../logger/progress.js';

/**
 * Run the OpenAI text embedding strategy.
 */
export async function run(
  input: string[],
  callbacks: WorkflowCallbacks,
  cache: PipelineCache,
  args: Record<string, any>
): Promise<TextEmbeddingResult> {
  if (isNull(input)) {
    return { embeddings: null };
  }

  const batchSize = args.batch_size || 16;
  const batchMaxTokens = args.batch_max_tokens || 8191;
  const llmConfig = args.llm as LanguageModelConfig;
  const splitter = getSplitter(llmConfig, batchMaxTokens);
  const model = ModelManager.getInstance().getOrCreateEmbeddingModel(
    'text_embedding',
    llmConfig.type,
    llmConfig
  );

  const maxConcurrency = args.num_threads || 4;

  // Break up the input texts. The sizes here indicate how many snippets are in each input text
  const [texts, inputSizes] = prepareEmbedTexts(input, splitter);
  const textBatches = createTextBatches(texts, batchSize, batchMaxTokens, splitter);

  console.log(
    `embedding ${input.length} inputs via ${texts.length} snippets using ${textBatches.length} batches. max_batch_size=${batchSize}, batch_max_tokens=${batchMaxTokens}`
  );

  const ticker = progressTicker(
    callbacks.progress,
    textBatches.length,
    'generate embeddings progress: '
  );

  // Embed each chunk of snippets
  const embeddings = await executeEmbedding(model, textBatches, ticker, maxConcurrency);
  const reconstitutedEmbeddings = reconstituteEmbeddings(embeddings, inputSizes);

  return { embeddings: reconstitutedEmbeddings.map(emb => emb || null) };
}

/**
 * Get text splitter for the given configuration.
 */
function getSplitter(config: LanguageModelConfig, batchMaxTokens: number): TokenTextSplitter {
  return new TokenTextSplitter({
    encodingName: config.encoding_model,
    chunkSize: batchMaxTokens,
  });
}

/**
 * Execute embedding for all text batches.
 */
async function executeEmbedding(
  model: EmbeddingModel,
  chunks: string[][],
  tick: ProgressTicker,
  maxConcurrency: number
): Promise<number[][]> {
  const semaphore = new Semaphore(maxConcurrency);

  const embed = async (chunk: string[]): Promise<number[][]> => {
    await semaphore.acquire();
    try {
      const chunkEmbeddings = await model.aembedBatch(chunk);
      tick.tick(1);
      return chunkEmbeddings;
    } finally {
      semaphore.release();
    }
  };

  const futures = chunks.map(chunk => embed(chunk));
  const results = await Promise.all(futures);
  
  // Merge results in a single list of lists (reduce the collect dimension)
  return results.flat();
}

/**
 * Create batches of texts to embed.
 */
function createTextBatches(
  texts: string[],
  maxBatchSize: number,
  maxBatchTokens: number,
  splitter: TokenTextSplitter
): string[][] {
  // According to Azure OpenAI embeddings reference, Azure limits us to 16 concurrent embeddings and 8191 tokens per request
  const result: string[][] = [];
  let currentBatch: string[] = [];
  let currentBatchTokens = 0;

  for (const text of texts) {
    const tokenCount = splitter.numTokens(text);
    
    if (
      currentBatch.length >= maxBatchSize ||
      currentBatchTokens + tokenCount > maxBatchTokens
    ) {
      result.push(currentBatch);
      currentBatch = [];
      currentBatchTokens = 0;
    }

    currentBatch.push(text);
    currentBatchTokens += tokenCount;
  }

  if (currentBatch.length > 0) {
    result.push(currentBatch);
  }

  return result;
}

/**
 * Prepare embed texts by splitting them into snippets.
 */
function prepareEmbedTexts(
  input: string[],
  splitter: TokenTextSplitter
): [string[], number[]] {
  const sizes: number[] = [];
  const snippets: string[] = [];

  for (const text of input) {
    // Split the input text and filter out any empty content
    let splitTexts = splitter.splitText(text);
    if (!splitTexts) {
      continue;
    }
    splitTexts = splitTexts.filter((text: string) => text.length > 0);

    sizes.push(splitTexts.length);
    snippets.push(...splitTexts);
  }

  return [snippets, sizes];
}

/**
 * Reconstitute the embeddings into the original input texts.
 */
function reconstituteEmbeddings(
  rawEmbeddings: number[][],
  sizes: number[]
): (number[] | undefined)[] {
  const embeddings: (number[] | undefined)[] = [];
  let cursor = 0;

  for (const size of sizes) {
    if (size === 0) {
      embeddings.push(undefined);
    } else if (size === 1) {
      const embedding = rawEmbeddings[cursor];
      embeddings.push(embedding);
      cursor += 1;
    } else {
      const chunk = rawEmbeddings.slice(cursor, cursor + size);
      const average = averageVectors(chunk);
      const normalized = normalizeVector(average);
      embeddings.push(normalized);
      cursor += size;
    }
  }

  return embeddings;
}

/**
 * Calculate average of vectors.
 */
function averageVectors(vectors: number[][]): number[] {
  if (vectors.length === 0) return [];
  
  const dimensions = vectors[0].length;
  const result = new Array(dimensions).fill(0);
  
  for (const vector of vectors) {
    for (let i = 0; i < dimensions; i++) {
      result[i] += vector[i];
    }
  }
  
  for (let i = 0; i < dimensions; i++) {
    result[i] /= vectors.length;
  }
  
  return result;
}

/**
 * Normalize a vector.
 */
function normalizeVector(vector: number[]): number[] {
  const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
  return vector.map(val => val / norm);
}

/**
 * Simple semaphore implementation for concurrency control.
 */
class Semaphore {
  private permits: number;
  private waitQueue: (() => void)[] = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return;
    }

    return new Promise<void>(resolve => {
      this.waitQueue.push(resolve);
    });
  }

  release(): void {
    this.permits++;
    if (this.waitQueue.length > 0) {
      const resolve = this.waitQueue.shift()!;
      this.permits--;
      resolve();
    }
  }
}
