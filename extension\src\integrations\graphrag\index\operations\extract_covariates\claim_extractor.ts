/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'ClaimExtractorResult' and 'ClaimExtractor' models.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { ErrorHandlerFn } from '../../typing/error_handler.js';
import { ChatModel } from '../../../language_model/protocol/base.js';

const DEFAULT_TUPLE_DELIMITER = "<|>";
const DEFAULT_RECORD_DELIMITER = "##";
const DEFAULT_COMPLETION_DELIMITER = "<|COMPLETE|>";
const logger = console;

/**
 * Claim extractor result class definition.
 * Matches the Python dataclass structure exactly.
 */
export interface ClaimExtractorResult {
    output: Record<string, any>[];
    source_docs: Record<string, any>;
}

/**
 * Claim extractor class definition.
 * Matches the Python class structure exactly.
 */
export class ClaimExtractor {
    private _model: ChatModel;
    private _extraction_prompt: string;
    private _input_text_key: string;
    private _input_entity_spec_key: string;
    private _input_claim_description_key: string;
    private _input_resolved_entities_key: string;
    private _tuple_delimiter_key: string;
    private _record_delimiter_key: string;
    private _completion_delimiter_key: string;
    private _max_gleanings: number;
    private _on_error: ErrorHandlerFn;

    constructor(
        model_invoker: ChatModel,
        extraction_prompt?: string | null,
        input_text_key?: string | null,
        input_entity_spec_key?: string | null,
        input_claim_description_key?: string | null,
        input_resolved_entities_key?: string | null,
        tuple_delimiter_key?: string | null,
        record_delimiter_key?: string | null,
        completion_delimiter_key?: string | null,
        max_gleanings?: number | null,
        on_error?: ErrorHandlerFn | null
    ) {
        // Python: Init method definition
        this._model = model_invoker;
        this._extraction_prompt = extraction_prompt || "EXTRACT_CLAIMS_PROMPT"; // TODO: Import actual prompt
        this._input_text_key = input_text_key || "input_text";
        this._input_entity_spec_key = input_entity_spec_key || "entity_specs";
        this._tuple_delimiter_key = tuple_delimiter_key || "tuple_delimiter";
        this._record_delimiter_key = record_delimiter_key || "record_delimiter";
        this._completion_delimiter_key = completion_delimiter_key || "completion_delimiter";
        this._input_claim_description_key = input_claim_description_key || "claim_description";
        this._input_resolved_entities_key = input_resolved_entities_key || "resolved_entities";
        this._max_gleanings = max_gleanings !== null && max_gleanings !== undefined ? max_gleanings : 1; // TODO: Use actual default
        this._on_error = on_error || ((e: Error | null, s: string | null, d: any) => {});
    }

    /**
     * Call method definition.
     * Matches the Python __call__ method exactly.
     */
    async call(
        inputs: Record<string, any>,
        prompt_variables?: Record<string, any> | null
    ): Promise<ClaimExtractorResult> {
        if (prompt_variables === null || prompt_variables === undefined) {
            prompt_variables = {};
        }

        const texts = inputs[this._input_text_key];
        const entity_spec = String(inputs[this._input_entity_spec_key]);
        const claim_description = inputs[this._input_claim_description_key];
        const resolved_entities = inputs[this._input_resolved_entities_key] || {};
        const source_doc_map: Record<string, any> = {};

        const prompt_args = {
            [this._input_entity_spec_key]: entity_spec,
            [this._input_claim_description_key]: claim_description,
            [this._tuple_delimiter_key]: prompt_variables[this._tuple_delimiter_key] || DEFAULT_TUPLE_DELIMITER,
            [this._record_delimiter_key]: prompt_variables[this._record_delimiter_key] || DEFAULT_RECORD_DELIMITER,
            [this._completion_delimiter_key]: prompt_variables[this._completion_delimiter_key] || DEFAULT_COMPLETION_DELIMITER,
        };

        const all_claims: Record<string, any>[] = [];

        for (let doc_index = 0; doc_index < texts.length; doc_index++) {
            const text = texts[doc_index];
            const document_id = `d${doc_index}`;

            try {
                const claims = await this._process_document(prompt_args, text, doc_index);
                const cleaned_claims = claims.map(c => this._clean_claim(c, document_id, resolved_entities));
                all_claims.push(...cleaned_claims);
                source_doc_map[document_id] = text;
            } catch (e) {
                const error = e instanceof Error ? e : new Error(String(e));
                logger.error("error extracting claim", error);
                this._on_error(
                    error,
                    error.stack || '',
                    { doc_index: doc_index, text: text }
                );
                continue;
            }
        }

        return {
            output: all_claims,
            source_docs: source_doc_map,
        };
    }

    /**
     * Clean the parsed claims to remove any claims with status = False.
     * Matches the Python _clean_claim method exactly.
     */
    private _clean_claim(
        claim: Record<string, any>,
        document_id: string,
        resolved_entities: Record<string, any>
    ): Record<string, any> {
        // Python: clean the parsed claims to remove any claims with status = False
        let obj = claim["object_id"] || claim["object"];
        let subject = claim["subject_id"] || claim["subject"];

        // If subject or object in resolved entities, then replace with resolved entity
        obj = resolved_entities[obj] || obj;
        subject = resolved_entities[subject] || subject;
        claim["object_id"] = obj;
        claim["subject_id"] = subject;
        return claim;
    }

    /**
     * Process a single document.
     * Matches the Python _process_document method exactly.
     */
    private async _process_document(
        prompt_args: Record<string, any>,
        doc: any,
        doc_index: number
    ): Promise<Record<string, any>[]> {
        const record_delimiter = prompt_args[this._record_delimiter_key] || DEFAULT_RECORD_DELIMITER;
        const completion_delimiter = prompt_args[this._completion_delimiter_key] || DEFAULT_COMPLETION_DELIMITER;

        // Python: response = await self._model.achat(...)
        const response = await this._model.achat(
            this._extraction_prompt.replace(/\{input_text\}/g, doc).replace(/\{([^}]+)\}/g, (match, key) => {
                return prompt_args[key] || match;
            })
        );

        let results = response.output?.content || "";
        let claims = results.trim();
        if (claims.endsWith(completion_delimiter)) {
            claims = claims.slice(0, -completion_delimiter.length);
        }

        // Python: if gleanings are specified, enter a loop to extract more claims
        if (this._max_gleanings > 0) {
            for (let i = 0; i < this._max_gleanings; i++) {
                const continuation_response = await this._model.achat(
                    "CONTINUE_PROMPT", // TODO: Import actual prompt
                    response.history || []
                );

                let extension = continuation_response.output?.content || "";
                extension = extension.trim();
                if (extension.endsWith(completion_delimiter)) {
                    extension = extension.slice(0, -completion_delimiter.length);
                }
                claims += record_delimiter + extension;

                // If this isn't the last loop, check to see if we should continue
                if (i >= this._max_gleanings - 1) {
                    break;
                }

                const loop_response = await this._model.achat(
                    "LOOP_PROMPT", // TODO: Import actual prompt
                    continuation_response.history || []
                );

                if (loop_response.output?.content !== "Y") {
                    break;
                }
            }
        }

        return this._parse_claim_tuples(results, prompt_args);
    }

    /**
     * Parse claim tuples.
     * Matches the Python _parse_claim_tuples method exactly.
     */
    private _parse_claim_tuples(
        claims: string,
        prompt_variables: Record<string, any>
    ): Record<string, any>[] {
        const record_delimiter = prompt_variables[this._record_delimiter_key] || DEFAULT_RECORD_DELIMITER;
        const completion_delimiter = prompt_variables[this._completion_delimiter_key] || DEFAULT_COMPLETION_DELIMITER;
        const tuple_delimiter = prompt_variables[this._tuple_delimiter_key] || DEFAULT_TUPLE_DELIMITER;

        function pull_field(index: number, fields: string[]): string | null {
            return fields.length > index ? fields[index].trim() : null;
        }

        const result: Record<string, any>[] = [];
        let claims_trimmed = claims.trim();
        if (claims_trimmed.endsWith(completion_delimiter)) {
            claims_trimmed = claims_trimmed.slice(0, -completion_delimiter.length);
        }

        const claims_values = claims_trimmed.split(record_delimiter);

        for (let claim of claims_values) {
            claim = claim.trim();
            if (claim.startsWith("(")) {
                claim = claim.slice(1);
            }
            if (claim.endsWith(")")) {
                claim = claim.slice(0, -1);
            }

            // Ignore the completion delimiter
            if (claim === completion_delimiter) {
                continue;
            }

            const claim_fields = claim.split(tuple_delimiter);
            result.push({
                "subject_id": pull_field(0, claim_fields),
                "object_id": pull_field(1, claim_fields),
                "type": pull_field(2, claim_fields),
                "status": pull_field(3, claim_fields),
                "start_date": pull_field(4, claim_fields),
                "end_date": pull_field(5, claim_fields),
                "description": pull_field(6, claim_fields),
                "source_text": pull_field(7, claim_fields),
            });
        }

        return result;
    }
}