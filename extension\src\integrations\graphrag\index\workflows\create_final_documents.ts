/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { DOCUMENTS_FINAL_COLUMNS } from '../../data_model/schemas';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * All the steps to transform final documents.
 */
export async function runWorkflow(
    _config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: create_final_documents");
    
    const documents = await loadTableFromStorage("documents", context.outputStorage);
    const textUnits = await loadTableFromStorage("text_units", context.outputStorage);

    const output = createFinalDocuments(documents, textUnits);

    await writeTableToStorage(output, "documents", context.outputStorage);

    logger.info("Workflow completed: create_final_documents");
    return { result: output };
}

/**
 * All the steps to transform final documents.
 */
export function createFinalDocuments(
    documents: DataFrame, 
    textUnits: DataFrame
): DataFrame {
    // Explode text_units by document_ids and select relevant columns
    const exploded = explodeDocumentIds(textUnits);
    
    // Merge exploded with documents
    const joined = mergeDataFrames(
        exploded,
        documents,
        "chunk_doc_id",
        "id",
        "inner"
    );

    // Group by document id and aggregate text_unit_ids
    const docsWithTextUnits = groupByAndAggregate(joined, "id", "chunk_id", "text_unit_ids");

    // Merge back with documents
    const rejoined = mergeDataFrames(
        docsWithTextUnits,
        documents,
        "id",
        "id",
        "right"
    );

    // Convert id to string and add human_readable_id
    const result: DataFrame = { ...rejoined, length: rejoined.length };
    
    // Convert id to string
    if (result.id) {
        result.id = result.id.map((id: any) => String(id));
    }

    // Add human_readable_id as index + 1
    result.human_readable_id = Array.from({ length: result.length }, (_, i) => i + 1);

    // Ensure metadata column exists
    if (!result.metadata) {
        result.metadata = new Array(result.length).fill(null);
    }

    // Return only final columns
    return selectColumns(result, DOCUMENTS_FINAL_COLUMNS);
}

/**
 * Explode document_ids column and rename columns.
 */
function explodeDocumentIds(textUnits: DataFrame): DataFrame {
    const result: DataFrame = { length: 0 };
    const explodedRows: any[] = [];

    const ids = textUnits.id || [];
    const documentIds = textUnits.document_ids || [];
    const texts = textUnits.text || [];

    for (let i = 0; i < textUnits.length; i++) {
        const docIds = documentIds[i];
        if (Array.isArray(docIds)) {
            for (const docId of docIds) {
                explodedRows.push({
                    chunk_id: ids[i],
                    chunk_doc_id: docId,
                    chunk_text: texts[i],
                });
            }
        } else if (docIds != null) {
            explodedRows.push({
                chunk_id: ids[i],
                chunk_doc_id: docIds,
                chunk_text: texts[i],
            });
        }
    }

    result.length = explodedRows.length;
    result.chunk_id = explodedRows.map(row => row.chunk_id);
    result.chunk_doc_id = explodedRows.map(row => row.chunk_doc_id);
    result.chunk_text = explodedRows.map(row => row.chunk_text);

    return result;
}

/**
 * Merge two DataFrames based on specified columns.
 */
function mergeDataFrames(
    left: DataFrame,
    right: DataFrame,
    leftOn: string,
    rightOn: string,
    how: "inner" | "left" | "right" | "outer"
): DataFrame {
    const result: DataFrame = { length: 0 };
    const mergedRows: any[] = [];

    const leftKeys = left[leftOn] || [];
    const rightKeys = right[rightOn] || [];

    // Get all column names from both DataFrames
    const leftColumns = Object.keys(left).filter(key => key !== 'length');
    const rightColumns = Object.keys(right).filter(key => key !== 'length');
    const allColumns = [...new Set([...leftColumns, ...rightColumns])];

    if (how === "inner") {
        for (let i = 0; i < left.length; i++) {
            const leftKey = leftKeys[i];
            for (let j = 0; j < right.length; j++) {
                const rightKey = rightKeys[j];
                if (leftKey === rightKey) {
                    const mergedRow: any = {};
                    // Add left columns
                    for (const col of leftColumns) {
                        if (col !== 'length') {
                            mergedRow[col] = left[col][i];
                        }
                    }
                    // Add right columns (avoid duplicating join key)
                    for (const col of rightColumns) {
                        if (col !== 'length' && col !== rightOn) {
                            mergedRow[col] = right[col][j];
                        }
                    }
                    mergedRows.push(mergedRow);
                }
            }
        }
    } else if (how === "right") {
        for (let j = 0; j < right.length; j++) {
            const rightKey = rightKeys[j];
            let found = false;
            
            for (let i = 0; i < left.length; i++) {
                const leftKey = leftKeys[i];
                if (leftKey === rightKey) {
                    const mergedRow: any = {};
                    // Add left columns
                    for (const col of leftColumns) {
                        if (col !== 'length') {
                            mergedRow[col] = left[col][i];
                        }
                    }
                    // Add right columns
                    for (const col of rightColumns) {
                        if (col !== 'length') {
                            mergedRow[col] = right[col][j];
                        }
                    }
                    mergedRows.push(mergedRow);
                    found = true;
                }
            }
            
            if (!found) {
                const mergedRow: any = {};
                // Add null values for left columns
                for (const col of leftColumns) {
                    if (col !== 'length') {
                        mergedRow[col] = null;
                    }
                }
                // Add right columns
                for (const col of rightColumns) {
                    if (col !== 'length') {
                        mergedRow[col] = right[col][j];
                    }
                }
                mergedRows.push(mergedRow);
            }
        }
    }

    // Convert merged rows back to DataFrame format
    result.length = mergedRows.length;
    for (const col of allColumns) {
        if (col !== 'length') {
            result[col] = mergedRows.map(row => row[col]);
        }
    }

    return result;
}

/**
 * Group by a column and aggregate another column into a list.
 */
function groupByAndAggregate(
    df: DataFrame,
    groupByCol: string,
    aggregateCol: string,
    resultCol: string
): DataFrame {
    const groups = new Map<any, any[]>();
    const groupKeys = df[groupByCol] || [];
    const aggregateValues = df[aggregateCol] || [];

    // Group values by key
    for (let i = 0; i < df.length; i++) {
        const key = groupKeys[i];
        const value = aggregateValues[i];
        
        if (!groups.has(key)) {
            groups.set(key, []);
        }
        groups.get(key)!.push(value);
    }

    // Convert groups back to DataFrame
    const result: DataFrame = { length: groups.size };
    const keys = Array.from(groups.keys());
    const aggregatedValues = Array.from(groups.values());

    result[groupByCol] = keys;
    result[resultCol] = aggregatedValues;

    return result;
}

/**
 * Select specific columns from a DataFrame.
 */
function selectColumns(df: DataFrame, columns: string[]): DataFrame {
    const result: DataFrame = { length: df.length };
    
    for (const col of columns) {
        if (df[col] !== undefined) {
            result[col] = df[col];
        } else {
            result[col] = new Array(df.length).fill(null);
        }
    }

    return result;
}