// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the local search configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';

/**
 * The configuration section for Local Search.
 */
export interface LocalSearchConfig {
  /**
   * The local search prompt to use.
   */
  prompt?: string;

  /**
   * The model ID to use for local search.
   */
  chatModelId: string;

  /**
   * The model ID to use for text embeddings.
   */
  embeddingModelId: string;

  /**
   * The text unit proportion.
   */
  textUnitProp: number;

  /**
   * The community proportion.
   */
  communityProp: number;

  /**
   * The conversation history maximum turns.
   */
  conversationHistoryMaxTurns: number;

  /**
   * The top k mapped entities.
   */
  topKEntities: number;

  /**
   * The top k mapped relations.
   */
  topKRelationships: number;

  /**
   * The maximum tokens.
   */
  maxContextTokens: number;
}

/**
 * Create a LocalSearchConfig with default values.
 */
export function createLocalSearchConfig(config: Partial<LocalSearchConfig> = {}): LocalSearchConfig {
  return {
    prompt: config.prompt ?? graphragConfigDefaults.localSearch.prompt,
    chatModelId: config.chatModelId ?? graphragConfigDefaults.localSearch.chatModelId,
    embeddingModelId: config.embeddingModelId ?? graphragConfigDefaults.localSearch.embeddingModelId,
    textUnitProp: config.textUnitProp ?? graphragConfigDefaults.localSearch.textUnitProp,
    communityProp: config.communityProp ?? graphragConfigDefaults.localSearch.communityProp,
    conversationHistoryMaxTurns: config.conversationHistoryMaxTurns ?? graphragConfigDefaults.localSearch.conversationHistoryMaxTurns,
    topKEntities: config.topKEntities ?? graphragConfigDefaults.localSearch.topKEntities,
    topKRelationships: config.topKRelationships ?? graphragConfigDefaults.localSearch.topKRelationships,
    maxContextTokens: config.maxContextTokens ?? graphragConfigDefaults.localSearch.maxContextTokens,
  };
}
