/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the Pipeline class.
 */

import { Workflow } from './workflow';

/**
 * Encapsulates running workflows.
 */
export class Pipeline {
    private workflows: Workflow[];

    constructor(workflows: Workflow[]) {
        this.workflows = workflows;
    }

    /**
     * Return a Generator over the pipeline workflows.
     */
    *run(): Generator<Workflow> {
        yield* this.workflows;
    }

    /**
     * Return the names of the workflows in the pipeline.
     */
    names(): string[] {
        return this.workflows.map(([name, _]) => name);
    }
}