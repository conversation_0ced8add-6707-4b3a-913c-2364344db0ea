// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing 'JsonPipelineCache' model.
 */

import { PipelineCache } from './pipeline_cache';
import { PipelineStorage } from '../storage/pipeline_storage';

/**
 * File pipeline cache class definition.
 */
export class JsonPipelineCache extends PipelineCache {
    private _storage: PipelineStorage;
    private _encoding: string;

    /**
     * Init method definition.
     */
    constructor(storage: PipelineStorage, encoding: string = "utf-8") {
        super();
        this._storage = storage;
        this._encoding = encoding;
    }

    /**
     * Get method definition.
     */
    async get(key: string): Promise<string | null> {
        if (await this.has(key)) {
            try {
                const data = await this._storage.get(key, { encoding: this._encoding });
                const parsedData = JSON.parse(data);
                return parsedData.result;
            } catch (error) {
                // Handle UnicodeDecodeError equivalent and JSONDecodeError
                await this._storage.delete(key);
                return null;
            }
        }
        return null;
    }

    /**
     * Set method definition.
     */
    async set(key: string, value: any, debugData?: Record<string, any> | null): Promise<void> {
        if (value === null || value === undefined) {
            return;
        }
        const data = { result: value, ...(debugData || {}) };
        await this._storage.set(
            key,
            JSON.stringify(data),
            { encoding: this._encoding }
        );
    }

    /**
     * Has method definition.
     */
    async has(key: string): Promise<boolean> {
        return await this._storage.has(key);
    }

    /**
     * Delete method definition.
     */
    async delete(key: string): Promise<void> {
        if (await this.has(key)) {
            await this._storage.delete(key);
        }
    }

    /**
     * Clear method definition.
     */
    async clear(): Promise<void> {
        await this._storage.clear();
    }

    /**
     * Child method definition.
     */
    child(name: string): JsonPipelineCache {
        return new JsonPipelineCache(this._storage.child(name), this._encoding);
    }
}