/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { PipelineCache } from '../../cache/pipeline-cache';
import { ExtractGraphNLPConfig } from '../../config/models/extract-graph-nlp-config';
import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { buildNounGraph } from '../operations/build_noun_graph/build-noun-graph';
import { createNounPhraseExtractor } from '../operations/build_noun_graph/np_extractors/factory';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * All the steps to create the base entity graph.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: extract_graph_nlp");
    
    const textUnits = await loadTableFromStorage("text_units", context.outputStorage);

    const { entities, relationships } = await extractGraphNlp({
        textUnits,
        cache: context.cache,
        extractionConfig: config.extractGraphNlp,
    });

    await writeTableToStorage(entities, "entities", context.outputStorage);
    await writeTableToStorage(relationships, "relationships", context.outputStorage);

    logger.info("Workflow completed: extract_graph_nlp");

    return {
        result: {
            entities,
            relationships,
        }
    };
}

export interface ExtractGraphNlpParams {
    textUnits: DataFrame;
    cache: PipelineCache;
    extractionConfig: ExtractGraphNLPConfig;
}

export interface ExtractGraphNlpResult {
    entities: DataFrame;
    relationships: DataFrame;
}

/**
 * All the steps to create the base entity graph.
 */
export async function extractGraphNlp({
    textUnits,
    cache,
    extractionConfig,
}: ExtractGraphNlpParams): Promise<ExtractGraphNlpResult> {
    const textAnalyzerConfig = extractionConfig.textAnalyzer;
    const textAnalyzer = createNounPhraseExtractor(textAnalyzerConfig);
    
    const { extractedNodes, extractedEdges } = await buildNounGraph({
        textUnits,
        textAnalyzer,
        normalizeEdgeWeights: extractionConfig.normalizeEdgeWeights,
        numThreads: extractionConfig.concurrentRequests,
        cache,
    });

    // Add in any other columns required by downstream workflows
    extractedNodes.type = new Array(extractedNodes.length).fill("NOUN PHRASE");
    extractedNodes.description = new Array(extractedNodes.length).fill("");
    extractedEdges.description = new Array(extractedEdges.length).fill("");

    return {
        entities: extractedNodes,
        relationships: extractedEdges,
    };
}