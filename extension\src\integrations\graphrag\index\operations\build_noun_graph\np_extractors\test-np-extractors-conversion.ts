// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Comprehensive test file for GraphRAG np_extractors conversion from Python to TypeScript.
 * This file validates that all converted TypeScript code maintains functional parity
 * with the original Python implementation.
 */

import { BaseNounPhraseExtractor } from './base.js';
import { CFGNounPhraseExtractor } from './cfg_extractor.js';
import { NounPhraseExtractorFactory, createNounPhraseExtractor } from './factory.js';
import { hasValidTokenLength, isCompound, isValidEntity } from './np_validator.js';
import { RegexENNounPhraseExtractor } from './regex_extractor.js';
import { downloadIfNotExists } from './resource_loader.js';
import { EN_STOP_WORDS } from './stop_words.js';
import { SyntacticNounPhraseExtractor } from './syntactic_parsing_extractor.js';

/**
 * Test results interface.
 */
interface TestResult {
    name: string;
    passed: boolean;
    error?: string;
    details?: string;
}

/**
 * Mock TextAnalyzerConfig for testing.
 */
interface MockTextAnalyzerConfig {
    extractorType: string;
    modelName: string;
    maxWordLength: number;
    includeNamedEntities: boolean;
    excludeEntityTags: string[];
    excludePosTags: string[];
    excludeNouns?: string[];
    wordDelimiter: string;
    nounPhraseGrammars: Record<string, string>;
    nounPhraseTags: string[];
}

/**
 * Test runner class for noun phrase extractors.
 */
class NpExtractorsConversionTester {
    private results: TestResult[] = [];

    /**
     * Add a test result.
     */
    private addResult(name: string, passed: boolean, error?: string, details?: string): void {
        this.results.push({ name, passed, error, details });
    }

    /**
     * Test BaseNounPhraseExtractor functionality.
     */
    testBaseExtractor(): void {
        try {
            // Test abstract class cannot be instantiated directly
            let canInstantiate = false;
            try {
                // This should fail since BaseNounPhraseExtractor is abstract
                new (BaseNounPhraseExtractor as any)();
                canInstantiate = true;
            } catch {
                // Expected to fail
            }

            const passed = !canInstantiate;
            this.addResult(
                'Base Extractor Abstract Class',
                passed,
                undefined,
                'BaseNounPhraseExtractor correctly prevents direct instantiation'
            );
        } catch (error) {
            this.addResult('Base Extractor Abstract Class', false, error.message);
        }
    }

    /**
     * Test np_validator functions.
     */
    testNpValidator(): void {
        try {
            // Test isCompound function
            const compoundTests = [
                { tokens: ['multi-word'], expected: true },
                { tokens: ['single'], expected: false },
                { tokens: ['word1', 'word-two'], expected: true },
                { tokens: ['word1', 'word2'], expected: false }
            ];

            let compoundPassed = true;
            for (const test of compoundTests) {
                const result = isCompound(test.tokens);
                if (result !== test.expected) {
                    compoundPassed = false;
                    break;
                }
            }

            // Test hasValidTokenLength function
            const lengthTests = [
                { tokens: ['short'], maxLength: 10, expected: true },
                { tokens: ['verylongword'], maxLength: 5, expected: false },
                { tokens: ['word1', 'word2'], maxLength: 10, expected: true }
            ];

            let lengthPassed = true;
            for (const test of lengthTests) {
                const result = hasValidTokenLength(test.tokens, test.maxLength);
                if (result !== test.expected) {
                    lengthPassed = false;
                    break;
                }
            }

            // Test isValidEntity function
            const entityTests = [
                { entity: ['John Doe', 'PERSON'] as [string, string], tokens: ['John', 'Doe'], expected: true },
                { entity: ['123', 'CARDINAL'] as [string, string], tokens: ['123'], expected: false },
                { entity: ['multi-123', 'CARDINAL'] as [string, string], tokens: ['multi-123'], expected: true }
            ];

            let entityPassed = true;
            for (const test of entityTests) {
                const result = isValidEntity(test.entity, test.tokens);
                if (result !== test.expected) {
                    entityPassed = false;
                    break;
                }
            }

            const passed = compoundPassed && lengthPassed && entityPassed;
            this.addResult(
                'NP Validator Functions',
                passed,
                undefined,
                `Compound: ${compoundPassed}, Length: ${lengthPassed}, Entity: ${entityPassed}`
            );
        } catch (error) {
            this.addResult('NP Validator Functions', false, error.message);
        }
    }

    /**
     * Test RegexENNounPhraseExtractor functionality.
     */
    testRegexExtractor(): void {
        try {
            const extractor = new RegexENNounPhraseExtractor(
                EN_STOP_WORDS,
                15,
                ' '
            );

            const testText = 'John Smith works at Microsoft Corporation. The company develops software.';
            const result = extractor.extract(testText);

            const passed = Array.isArray(result) && result.length > 0;
            this.addResult(
                'Regex Extractor',
                passed,
                undefined,
                `Extracted ${result.length} noun phrases: ${result.slice(0, 3).join(', ')}`
            );
        } catch (error) {
            this.addResult('Regex Extractor', false, error.message);
        }
    }

    /**
     * Test CFGNounPhraseExtractor functionality.
     */
    testCfgExtractor(): void {
        try {
            const grammars = {
                'NOUN,NOUN': 'NOUN',
                'ADJ,NOUN': 'NOUN'
            };

            const extractor = new CFGNounPhraseExtractor(
                'en_core_web_sm',
                15,
                true,
                ['CARDINAL'],
                ['PUNCT'],
                EN_STOP_WORDS,
                ' ',
                grammars,
                ['NOUN']
            );

            const testText = 'The quick brown fox jumps over the lazy dog.';
            const result = extractor.extract(testText);

            const passed = Array.isArray(result);
            this.addResult(
                'CFG Extractor',
                passed,
                undefined,
                `CFG extractor created and executed, returned ${result.length} phrases`
            );
        } catch (error) {
            this.addResult('CFG Extractor', false, error.message);
        }
    }

    /**
     * Test SyntacticNounPhraseExtractor functionality.
     */
    testSyntacticExtractor(): void {
        try {
            const extractor = new SyntacticNounPhraseExtractor(
                'en_core_web_sm',
                15,
                true,
                ['CARDINAL'],
                ['PUNCT'],
                EN_STOP_WORDS,
                ' '
            );

            const testText = 'Natural language processing is a fascinating field.';
            const result = extractor.extract(testText);

            const passed = Array.isArray(result);
            this.addResult(
                'Syntactic Extractor',
                passed,
                undefined,
                `Syntactic extractor created and executed, returned ${result.length} phrases`
            );
        } catch (error) {
            this.addResult('Syntactic Extractor', false, error.message);
        }
    }

    /**
     * Test NounPhraseExtractorFactory functionality.
     */
    testExtractorFactory(): void {
        try {
            // Test factory registration
            NounPhraseExtractorFactory.register('test', RegexENNounPhraseExtractor);

            // Test factory creation with different types
            const configs: MockTextAnalyzerConfig[] = [
                {
                    extractorType: 'regex_english',
                    modelName: 'en_core_web_sm',
                    maxWordLength: 15,
                    includeNamedEntities: false,
                    excludeEntityTags: [],
                    excludePosTags: [],
                    wordDelimiter: ' ',
                    nounPhraseGrammars: {},
                    nounPhraseTags: []
                }
            ];

            let factoryPassed = true;
            for (const config of configs) {
                try {
                    const extractor = createNounPhraseExtractor(config as any);
                    if (!(extractor instanceof BaseNounPhraseExtractor)) {
                        factoryPassed = false;
                        break;
                    }
                } catch {
                    factoryPassed = false;
                    break;
                }
            }

            this.addResult(
                'Extractor Factory',
                factoryPassed,
                undefined,
                'Factory successfully creates extractors from configuration'
            );
        } catch (error) {
            this.addResult('Extractor Factory', false, error.message);
        }
    }

    /**
     * Test resource loader functionality.
     */
    testResourceLoader(): void {
        try {
            const result1 = downloadIfNotExists('brown');
            const result2 = downloadIfNotExists('punkt');

            const passed = typeof result1 === 'boolean' && typeof result2 === 'boolean';
            this.addResult(
                'Resource Loader',
                passed,
                undefined,
                'Resource loader functions execute without errors'
            );
        } catch (error) {
            this.addResult('Resource Loader', false, error.message);
        }
    }

    /**
     * Test stop words functionality.
     */
    testStopWords(): void {
        try {
            const passed = Array.isArray(EN_STOP_WORDS) && 
                          EN_STOP_WORDS.length > 0 &&
                          EN_STOP_WORDS.includes('stuff');

            this.addResult(
                'Stop Words',
                passed,
                undefined,
                `Stop words list contains ${EN_STOP_WORDS.length} words`
            );
        } catch (error) {
            this.addResult('Stop Words', false, error.message);
        }
    }

    /**
     * Run all tests and return results.
     */
    runAllTests(): TestResult[] {
        console.log('🚀 Starting comprehensive GraphRAG np_extractors conversion tests...\n');

        this.testBaseExtractor();
        this.testNpValidator();
        this.testRegexExtractor();
        this.testCfgExtractor();
        this.testSyntacticExtractor();
        this.testExtractorFactory();
        this.testResourceLoader();
        this.testStopWords();

        return this.results;
    }

    /**
     * Print test results.
     */
    printResults(): void {
        const passed = this.results.filter(r => r.passed).length;
        const total = this.results.length;

        console.log('\n📊 Test Results Summary:');
        console.log('========================');

        for (const result of this.results) {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.name}`);
            
            if (result.details) {
                console.log(`   Details: ${result.details}`);
            }
            
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        }

        console.log('\n📈 Overall Results:');
        console.log(`   Passed: ${passed}/${total} (${Math.round(passed/total*100)}%)`);
        
        if (passed === total) {
            console.log('🎉 All tests passed! TypeScript conversion is successful.');
        } else {
            console.log('⚠️  Some tests failed. Please review the conversion.');
        }
    }
}

/**
 * Run tests if this file is executed directly.
 */
async function runTests(): Promise<void> {
    const tester = new NpExtractorsConversionTester();
    
    try {
        tester.runAllTests();
        tester.printResults();
    } catch (error) {
        console.error('Test execution failed:', error);
    }
}

// Export for use in other modules
export { NpExtractorsConversionTester, runTests };

// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}
