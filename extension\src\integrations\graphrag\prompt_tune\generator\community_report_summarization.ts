// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Module for generating prompts for community report summarization.
 */

import * as fs from 'fs';
import * as path from 'path';
import { COMMUNITY_REPORT_SUMMARIZATION_PROMPT } from '../template/community_report_summarization';

export const COMMUNITY_SUMMARIZATION_FILENAME = 'community_report_graph.txt';

/**
 * Create a prompt for community summarization. If outputPath is provided, write the prompt to a file.
 * 
 * @param persona - The persona to use for the community summarization prompt
 * @param role - The role to use for the community summarization prompt
 * @param reportRatingDescription - The report rating description
 * @param language - The language to use for the community summarization prompt
 * @param outputPath - The path to write the prompt to (optional)
 * @returns The community summarization prompt
 */
export function createCommunitySummarizationPrompt(
    persona: string,
    role: string,
    reportRatingDescription: string,
    language: string,
    outputPath?: string
): string {
    const prompt = COMMUNITY_REPORT_SUMMARIZATION_PROMPT
        .replace('{persona}', persona)
        .replace('{role}', role)
        .replace('{report_rating_description}', reportRatingDescription)
        .replace('{language}', language);

    if (outputPath) {
        // Ensure directory exists
        fs.mkdirSync(outputPath, { recursive: true });

        const fullPath = path.join(outputPath, COMMUNITY_SUMMARIZATION_FILENAME);
        
        // Write file to output path
        fs.writeFileSync(fullPath, prompt, { encoding: 'utf-8' });
    }

    return prompt;
}

/**
 * Create community summarization prompt asynchronously.
 */
export async function createCommunitySummarizationPromptAsync(
    persona: string,
    role: string,
    reportRatingDescription: string,
    language: string,
    outputPath?: string
): Promise<string> {
    const prompt = COMMUNITY_REPORT_SUMMARIZATION_PROMPT
        .replace('{persona}', persona)
        .replace('{role}', role)
        .replace('{report_rating_description}', reportRatingDescription)
        .replace('{language}', language);

    if (outputPath) {
        // Ensure directory exists
        await fs.promises.mkdir(outputPath, { recursive: true });

        const fullPath = path.join(outputPath, COMMUNITY_SUMMARIZATION_FILENAME);
        
        // Write file to output path
        await fs.promises.writeFile(fullPath, prompt, { encoding: 'utf-8' });
    }

    return prompt;
}

/**
 * Validate community summarization prompt parameters.
 */
export function validateCommunitySummarizationParams(
    persona: string,
    role: string,
    reportRatingDescription: string,
    language: string
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!persona || persona.trim() === '') {
        errors.push('Persona cannot be empty');
    }

    if (!role || role.trim() === '') {
        errors.push('Role cannot be empty');
    }

    if (!reportRatingDescription || reportRatingDescription.trim() === '') {
        errors.push('Report rating description cannot be empty');
    }

    if (!language || language.trim() === '') {
        errors.push('Language cannot be empty');
    }

    if (persona.length > 5000) {
        errors.push('Persona is too long (max 5000 characters)');
    }

    if (role.length > 1000) {
        errors.push('Role is too long (max 1000 characters)');
    }

    if (reportRatingDescription.length > 2000) {
        errors.push('Report rating description is too long (max 2000 characters)');
    }

    if (language.length > 100) {
        errors.push('Language specification is too long (max 100 characters)');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Create community summarization prompt with validation.
 */
export function createCommunitySummarizationPromptSafe(
    persona: string,
    role: string,
    reportRatingDescription: string,
    language: string,
    outputPath?: string
): { prompt?: string; success: boolean; errors: string[] } {
    const validation = validateCommunitySummarizationParams(persona, role, reportRatingDescription, language);
    
    if (!validation.isValid) {
        return {
            success: false,
            errors: validation.errors
        };
    }

    try {
        const prompt = createCommunitySummarizationPrompt(persona, role, reportRatingDescription, language, outputPath);
        return {
            prompt,
            success: true,
            errors: []
        };
    } catch (error) {
        return {
            success: false,
            errors: [`Failed to create prompt: ${error instanceof Error ? error.message : String(error)}`]
        };
    }
}
