/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Utility functions for the GraphRAG run module.
 */

import { InMemoryCache } from '../../cache/memory-pipeline-cache';
import { PipelineCache } from '../../cache/pipeline-cache';
import { NoopWorkflowCallbacks } from '../../callbacks/noop-workflow-callbacks';
import { WorkflowCallbacks } from '../../callbacks/workflow-callbacks';
import { WorkflowCallbacksManager } from '../../callbacks/workflow-callbacks-manager';
import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { MemoryPipelineStorage } from '../../storage/memory-pipeline-storage';
import { PipelineStorage } from '../../storage/pipeline-storage';
import { createStorageFromConfig } from '../../utils/api';
import { PipelineRunContext } from '../typing/context';
import { PipelineState } from '../typing/state';
import { PipelineRunStats, createPipelineRunStats } from '../typing/stats';

/**
 * Create the run context for the pipeline.
 */
export function createRunContext(options: {
    inputStorage?: PipelineStorage;
    outputStorage?: PipelineStorage;
    previousStorage?: PipelineStorage;
    cache?: PipelineCache;
    callbacks?: WorkflowCallbacks;
    stats?: PipelineRunStats;
    state?: PipelineState;
}): PipelineRunContext {
    return {
        inputStorage: options.inputStorage || new MemoryPipelineStorage(),
        outputStorage: options.outputStorage || new MemoryPipelineStorage(),
        previousStorage: options.previousStorage || new MemoryPipelineStorage(),
        cache: options.cache || new InMemoryCache(),
        callbacks: options.callbacks || new NoopWorkflowCallbacks(),
        stats: options.stats || createPipelineRunStats(),
        state: options.state || {},
    };
}

/**
 * Create a callback manager that encompasses multiple callbacks.
 */
export function createCallbackChain(callbacks?: WorkflowCallbacks[]): WorkflowCallbacks {
    const manager = new WorkflowCallbacksManager();
    if (callbacks) {
        for (const callback of callbacks) {
            manager.register(callback);
        }
    }
    return manager;
}

/**
 * Get storage objects for the update index run.
 */
export function getUpdateStorages(
    config: GraphRagConfig,
    timestamp: string
): [PipelineStorage, PipelineStorage, PipelineStorage] {
    const outputStorage = createStorageFromConfig(config.output);
    const updateStorage = createStorageFromConfig(config.updateIndexOutput);
    const timestampedStorage = updateStorage.child(timestamp);
    const deltaStorage = timestampedStorage.child("delta");
    const previousStorage = timestampedStorage.child("previous");

    return [outputStorage, previousStorage, deltaStorage];
}