// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * BasicSearch implementation.
 */

import { ChatModel } from '../../../language_model/protocol/base';
import { QueryCallbacks } from '../../../callbacks/query-callbacks';
import { BasicContextBuilder } from '../../context_builder/builders';
import { ConversationHistory } from '../../context_builder/conversation-history';
import { numTokens } from '../../llm/text-utils';
import { BaseSearch, SearchResult } from '../base';

const BASIC_SEARCH_SYSTEM_PROMPT = `
---Role---

You are a helpful assistant responding to questions about data in the tables provided.

---Goal---

Generate a response of the target length and format that responds to the user's question, summarizing all information in the input data tables appropriate for the response length and format, and incorporating any relevant general knowledge.
If you don't know the answer, just say so. Do not make anything up.

---Target response length and format---

{response_type}

---Data tables---

{context_data}

Add sections and commentary to the response as appropriate for the length and format. Style the response in markdown.
`;

/**
 * Implementation of a generic RAG algorithm (vector search on raw text chunks)
 * Search orchestration for basic search mode.
 */
export class BasicSearch extends BaseSearch<BasicContextBuilder> {
  private systemPrompt: string;
  private callbacks: QueryCallbacks[];
  private responseType: string;

  constructor(
    model: ChatModel,
    contextBuilder: BasicContextBuilder,
    tokenEncoder?: any,
    systemPrompt?: string,
    responseType: string = 'multiple paragraphs',
    callbacks?: QueryCallbacks[],
    modelParams?: Record<string, any>,
    contextBuilderParams?: Record<string, any>
  ) {
    super(
      model,
      contextBuilder,
      tokenEncoder,
      modelParams,
      contextBuilderParams || {}
    );
    this.systemPrompt = systemPrompt || BASIC_SEARCH_SYSTEM_PROMPT;
    this.callbacks = callbacks || [];
    this.responseType = responseType;
  }

  async search(
    query: string,
    conversationHistory?: ConversationHistory,
    ...kwargs: any[]
  ): Promise<SearchResult> {
    /**
     * Build rag search context that fits a single context window and generate answer for the user query.
     */
    const startTime = Date.now();
    let searchPrompt = '';
    const llmCalls: Record<string, number> = {};
    const promptTokens: Record<string, number> = {};
    const outputTokens: Record<string, number> = {};

    const contextResult = this.contextBuilder.buildContext(
      query,
      conversationHistory,
      { ...kwargs, ...this.contextBuilderParams }
    );

    llmCalls['build_context'] = contextResult.llmCalls;
    promptTokens['build_context'] = contextResult.promptTokens;
    outputTokens['build_context'] = contextResult.outputTokens;

    console.debug(`GENERATE ANSWER: ${startTime}. QUERY: ${query}`);
    
    try {
      searchPrompt = this.systemPrompt
        .replace('{context_data}', contextResult.contextChunks)
        .replace('{response_type}', this.responseType);

      const searchMessages = [
        { role: 'system' as const, content: searchPrompt }
      ];

      let response = '';
      const stream = this.model.achatStream(
        query,
        searchMessages,
        this.modelParams
      );

      for await (const chunk of stream) {
        for (const callback of this.callbacks) {
          callback.onLlmNewToken(chunk);
        }
        response += chunk;
      }

      llmCalls['response'] = 1;
      promptTokens['response'] = numTokens(searchPrompt, this.tokenEncoder);
      outputTokens['response'] = numTokens(response, this.tokenEncoder);

      for (const callback of this.callbacks) {
        callback.onContext(contextResult.contextRecords);
      }

      return {
        response,
        contextData: contextResult.contextRecords,
        contextText: contextResult.contextChunks,
        completionTime: Date.now() - startTime,
        llmCalls: 1,
        promptTokens: numTokens(searchPrompt, this.tokenEncoder),
        outputTokens: Object.values(outputTokens).reduce((sum, val) => sum + val, 0),
        llmCallsCategories: llmCalls,
        promptTokensCategories: promptTokens,
        outputTokensCategories: outputTokens
      };

    } catch (error) {
      console.error('Exception in _asearch', error);
      return {
        response: '',
        contextData: contextResult.contextRecords,
        contextText: contextResult.contextChunks,
        completionTime: Date.now() - startTime,
        llmCalls: 1,
        promptTokens: numTokens(searchPrompt, this.tokenEncoder),
        outputTokens: 0,
        llmCallsCategories: llmCalls,
        promptTokensCategories: promptTokens,
        outputTokensCategories: outputTokens
      };
    }
  }

  async *streamSearch(
    query: string,
    conversationHistory?: ConversationHistory
  ): AsyncGenerator<string, void, unknown> {
    /**
     * Build basic search context that fits a single context window and generate answer for the user query.
     */
    const startTime = Date.now();

    const contextResult = this.contextBuilder.buildContext(
      query,
      conversationHistory,
      this.contextBuilderParams
    );

    console.debug(`GENERATE ANSWER: ${startTime}. QUERY: ${query}`);
    
    const searchPrompt = this.systemPrompt
      .replace('{context_data}', contextResult.contextChunks)
      .replace('{response_type}', this.responseType);

    const searchMessages = [
      { role: 'system' as const, content: searchPrompt }
    ];

    for (const callback of this.callbacks) {
      callback.onContext(contextResult.contextRecords);
    }

    const stream = this.model.achatStream(
      query,
      searchMessages,
      this.modelParams
    );

    for await (const chunkResponse of stream) {
      for (const callback of this.callbacks) {
        callback.onLlmNewToken(chunkResponse);
      }
      yield chunkResponse;
    }
  }
}