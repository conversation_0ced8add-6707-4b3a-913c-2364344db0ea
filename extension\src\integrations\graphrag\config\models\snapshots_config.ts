// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the snapshots configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';

/**
 * Configuration section for snapshots.
 */
export interface SnapshotsConfig {
  /**
   * A flag indicating whether to take snapshots of embeddings.
   */
  embeddings: boolean;

  /**
   * A flag indicating whether to take snapshots of GraphML.
   */
  graphml: boolean;

  /**
   * A flag indicating whether to take snapshots of the raw extracted graph (entities and relationships) before merging.
   */
  rawGraph: boolean;
}

/**
 * Create a SnapshotsConfig with default values.
 */
export function createSnapshotsConfig(config: Partial<SnapshotsConfig> = {}): SnapshotsConfig {
  return {
    embeddings: config.embeddings ?? graphragConfigDefaults.snapshots.embeddings,
    graphml: config.graphml ?? graphragConfigDefaults.snapshots.graphml,
    rawGraph: config.rawGraph ?? graphragConfigDefaults.snapshots.rawGraph,
  };
}
