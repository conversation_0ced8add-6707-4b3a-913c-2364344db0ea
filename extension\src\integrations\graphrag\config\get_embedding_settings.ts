/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing getEmbeddingSettings.
 */

import { GraphRagConfig } from './models/graph-rag-config';
import { getResolvedTextEmbeddingStrategy } from './models/text_embedding_config';

/**
 * Transform GraphRAG config into settings for workflows.
 */
export function getEmbeddingSettings(
    settings: GraphRagConfig,
    vectorStoreParams?: Record<string, any>
): Record<string, any> {
    // TEMP
    const embeddingsLlmSettings = settings.getLanguageModelConfig(
        settings.embedText.modelId
    );
    const vectorStoreSettings = settings.getVectorStoreConfig(
        settings.embedText.vectorStoreId
    );

    //
    // If we get to this point, settings.vectorStore is defined, and there's a specific setting for this embedding.
    // settings.vectorStore.base contains connection information, or may be undefined
    // settings.vectorStore.<vector_name> contains the specific settings for this embedding
    //
    const strategy = getResolvedTextEmbeddingStrategy(
        settings.embedText,
        embeddingsLlmSettings
    ); // get the default strategy
    
    strategy.vectorStore = {
        ...(vectorStoreParams || {}),
        ...vectorStoreSettings,
    }; // update the default strategy with the vector store settings
    
    // This ensures the vector store config is part of the strategy and not the global config
    return {
        strategy,
    };
}