// Copyright (c) 2025 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Singleton LLM Manager for ChatLLM and EmbeddingsLLM instances.
 * 
 * This manager lets you register chat and embeddings LLMs independently.
 * It leverages the LLMFactory for instantiation.
 */

import { ModelFactory } from './factory';
import { ChatModel, EmbeddingModel } from './protocol/base';

/**
 * Singleton manager for LLM instances.
 */
export class ModelManager {
    private static instance: ModelManager | null = null;
    private chatModels: Map<string, ChatModel> = new Map();
    private embeddingModels: Map<string, EmbeddingModel> = new Map();
    private initialized: boolean = false;

    private constructor() {
        // Private constructor for singleton pattern
        if (!this.initialized) {
            this.chatModels = new Map();
            this.embeddingModels = new Map();
            this.initialized = true;
        }
    }

    /**
     * Create a new instance of ModelManager if it does not exist.
     */
    static getInstance(): ModelManager {
        if (ModelManager.instance === null) {
            ModelManager.instance = new ModelManager();
        }
        return ModelManager.instance;
    }

    /**
     * Register a ChatLLM instance under a unique name.
     * 
     * @param name - Unique identifier for the ChatLLM instance.
     * @param modelType - Key for the ChatLLM implementation in ModelFactory.
     * @param chatKwargs - Additional parameters for instantiation.
     * @returns The registered ChatModel instance.
     */
    registerChat(name: string, modelType: string, chatKwargs: Record<string, any> = {}): ChatModel {
        const kwargs = { ...chatKwargs, name };
        const chatModel = ModelFactory.createChatModel(modelType, kwargs);
        this.chatModels.set(name, chatModel);
        return chatModel;
    }

    /**
     * Register an EmbeddingsLLM instance under a unique name.
     * 
     * @param name - Unique identifier for the EmbeddingsLLM instance.
     * @param modelType - Key for the EmbeddingsLLM implementation in ModelFactory.
     * @param embeddingKwargs - Additional parameters for instantiation.
     * @returns The registered EmbeddingModel instance.
     */
    registerEmbedding(name: string, modelType: string, embeddingKwargs: Record<string, any> = {}): EmbeddingModel {
        const kwargs = { ...embeddingKwargs, name };
        const embeddingModel = ModelFactory.createEmbeddingModel(modelType, kwargs);
        this.embeddingModels.set(name, embeddingModel);
        return embeddingModel;
    }

    /**
     * Retrieve the ChatLLM instance registered under the given name.
     * 
     * @param name - The name of the ChatLLM instance to retrieve.
     * @returns The ChatModel instance.
     * @throws Error if no ChatLLM is registered under the name.
     */
    getChatModel(name: string): ChatModel {
        const chatModel = this.chatModels.get(name);
        if (!chatModel) {
            const msg = `No ChatLLM registered under the name '${name}'.`;
            throw new Error(msg);
        }
        return chatModel;
    }

    /**
     * Retrieve the EmbeddingsLLM instance registered under the given name.
     * 
     * @param name - The name of the EmbeddingsLLM instance to retrieve.
     * @returns The EmbeddingModel instance.
     * @throws Error if no EmbeddingsLLM is registered under the name.
     */
    getEmbeddingModel(name: string): EmbeddingModel {
        const embeddingModel = this.embeddingModels.get(name);
        if (!embeddingModel) {
            const msg = `No EmbeddingsLLM registered under the name '${name}'.`;
            throw new Error(msg);
        }
        return embeddingModel;
    }

    /**
     * Retrieve the ChatLLM instance registered under the given name.
     * 
     * If the ChatLLM does not exist, it is created and registered.
     * 
     * @param name - Unique identifier for the ChatLLM instance.
     * @param modelType - Key for the ChatModel implementation in ModelFactory.
     * @param chatKwargs - Additional parameters for instantiation.
     * @returns The ChatModel instance.
     */
    getOrCreateChatModel(name: string, modelType: string, chatKwargs: Record<string, any> = {}): ChatModel {
        if (!this.chatModels.has(name)) {
            return this.registerChat(name, modelType, chatKwargs);
        }
        return this.chatModels.get(name)!;
    }

    /**
     * Retrieve the EmbeddingsLLM instance registered under the given name.
     * 
     * If the EmbeddingsLLM does not exist, it is created and registered.
     * 
     * @param name - Unique identifier for the EmbeddingsLLM instance.
     * @param modelType - Key for the EmbeddingsLLM implementation in ModelFactory.
     * @param embeddingKwargs - Additional parameters for instantiation.
     * @returns The EmbeddingModel instance.
     */
    getOrCreateEmbeddingModel(name: string, modelType: string, embeddingKwargs: Record<string, any> = {}): EmbeddingModel {
        if (!this.embeddingModels.has(name)) {
            return this.registerEmbedding(name, modelType, embeddingKwargs);
        }
        return this.embeddingModels.get(name)!;
    }

    /**
     * Remove the ChatLLM instance registered under the given name.
     * 
     * @param name - The name of the ChatLLM instance to remove.
     */
    removeChat(name: string): void {
        this.chatModels.delete(name);
    }

    /**
     * Remove the EmbeddingsLLM instance registered under the given name.
     * 
     * @param name - The name of the EmbeddingsLLM instance to remove.
     */
    removeEmbedding(name: string): void {
        this.embeddingModels.delete(name);
    }

    /**
     * Return a copy of all registered ChatLLM instances.
     * 
     * @returns A record of all registered ChatModel instances.
     */
    listChatModels(): Record<string, ChatModel> {
        const result: Record<string, ChatModel> = {};
        for (const [name, model] of this.chatModels.entries()) {
            result[name] = model;
        }
        return result;
    }

    /**
     * Return a copy of all registered EmbeddingsLLM instances.
     * 
     * @returns A record of all registered EmbeddingModel instances.
     */
    listEmbeddingModels(): Record<string, EmbeddingModel> {
        const result: Record<string, EmbeddingModel> = {};
        for (const [name, model] of this.embeddingModels.entries()) {
            result[name] = model;
        }
        return result;
    }
}