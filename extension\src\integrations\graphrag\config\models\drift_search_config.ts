// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the DRIFT search configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';

/**
 * The configuration section for DRIFT Search.
 */
export interface DRIFTSearchConfig {
  /**
   * The drift search prompt to use.
   */
  prompt?: string;

  /**
   * The drift search reduce prompt to use.
   */
  reducePrompt?: string;

  /**
   * The model ID to use for drift search.
   */
  chatModelId: string;

  /**
   * The model ID to use for drift search.
   */
  embeddingModelId: string;

  /**
   * The data llm maximum tokens.
   */
  dataMaxTokens: number;

  /**
   * The reduce llm maximum tokens response to produce.
   */
  reduceMaxTokens?: number;

  /**
   * The temperature to use for token generation in reduce.
   */
  reduceTemperature: number;

  /**
   * The reduce llm maximum tokens response to produce.
   */
  reduceMaxCompletionTokens?: number;

  /**
   * The number of concurrent requests.
   */
  concurrency: number;

  /**
   * The number of top global results to retrieve.
   */
  driftKFollowups: number;

  /**
   * The number of folds for search priming.
   */
  primerFolds: number;

  /**
   * The maximum number of tokens for the LLM in primer.
   */
  primerLlmMaxTokens: number;

  /**
   * The number of drift search steps to take.
   */
  nDepth: number;

  /**
   * The proportion of search dedicated to text units.
   */
  localSearchTextUnitProp: number;

  /**
   * The proportion of search dedicated to community properties.
   */
  localSearchCommunityProp: number;

  /**
   * The number of top K entities to map during local search.
   */
  localSearchTopKMappedEntities: number;

  /**
   * The number of top K relationships to map during local search.
   */
  localSearchTopKRelationships: number;

  /**
   * The maximum context size in tokens for local search.
   */
  localSearchMaxDataTokens: number;

  /**
   * The temperature to use for token generation in local search.
   */
  localSearchTemperature: number;

  /**
   * The top-p value to use for token generation in local search.
   */
  localSearchTopP: number;

  /**
   * The number of completions to generate in local search.
   */
  localSearchN: number;

  /**
   * The maximum number of generated tokens for the LLM in local search.
   */
  localSearchLlmMaxGenTokens?: number;

  /**
   * The maximum number of generated tokens for the LLM in local search.
   */
  localSearchLlmMaxGenCompletionTokens?: number;
}

/**
 * Create a DRIFTSearchConfig with default values.
 */
export function createDRIFTSearchConfig(config: Partial<DRIFTSearchConfig> = {}): DRIFTSearchConfig {
  const defaults = graphragConfigDefaults.driftSearch;
  
  return {
    prompt: config.prompt ?? defaults.prompt,
    reducePrompt: config.reducePrompt ?? defaults.reducePrompt,
    chatModelId: config.chatModelId ?? defaults.chatModelId,
    embeddingModelId: config.embeddingModelId ?? defaults.embeddingModelId,
    dataMaxTokens: config.dataMaxTokens ?? defaults.dataMaxTokens,
    reduceMaxTokens: config.reduceMaxTokens ?? defaults.reduceMaxTokens,
    reduceTemperature: config.reduceTemperature ?? defaults.reduceTemperature,
    reduceMaxCompletionTokens: config.reduceMaxCompletionTokens ?? defaults.reduceMaxCompletionTokens,
    concurrency: config.concurrency ?? defaults.concurrency,
    driftKFollowups: config.driftKFollowups ?? defaults.driftKFollowups,
    primerFolds: config.primerFolds ?? defaults.primerFolds,
    primerLlmMaxTokens: config.primerLlmMaxTokens ?? defaults.primerLlmMaxTokens,
    nDepth: config.nDepth ?? defaults.nDepth,
    localSearchTextUnitProp: config.localSearchTextUnitProp ?? defaults.localSearchTextUnitProp,
    localSearchCommunityProp: config.localSearchCommunityProp ?? defaults.localSearchCommunityProp,
    localSearchTopKMappedEntities: config.localSearchTopKMappedEntities ?? defaults.localSearchTopKMappedEntities,
    localSearchTopKRelationships: config.localSearchTopKRelationships ?? defaults.localSearchTopKRelationships,
    localSearchMaxDataTokens: config.localSearchMaxDataTokens ?? defaults.localSearchMaxDataTokens,
    localSearchTemperature: config.localSearchTemperature ?? defaults.localSearchTemperature,
    localSearchTopP: config.localSearchTopP ?? defaults.localSearchTopP,
    localSearchN: config.localSearchN ?? defaults.localSearchN,
    localSearchLlmMaxGenTokens: config.localSearchLlmMaxGenTokens ?? defaults.localSearchLlmMaxGenTokens,
    localSearchLlmMaxGenCompletionTokens: config.localSearchLlmMaxGenCompletionTokens ?? defaults.localSearchLlmMaxGenCompletionTokens,
  };
}
