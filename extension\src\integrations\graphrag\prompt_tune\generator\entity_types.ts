// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Entity type generation module for fine-tuning.
 */

import { ChatModel } from '../../language_model/protocol/base';
import { DEFAULT_TASK } from '../defaults';
import { 
    ENTITY_TYPE_GENERATION_JSON_PROMPT,
    ENTITY_TYPE_GENERATION_PROMPT 
} from '../prompt/entity_types';

/**
 * Entity types response model.
 */
export interface EntityTypesResponse {
    entity_types: string[];
}

/**
 * Generate entity type categories from a given set of (small) documents.
 * 
 * Example Output:
 * "entity_types": ['military unit', 'organization', 'person', 'location', 'event', 'date', 'equipment']
 * 
 * @param model - The chat model to use for generation
 * @param domain - The domain context
 * @param persona - The persona to use
 * @param docs - The documents to analyze
 * @param task - The task description (defaults to DEFAULT_TASK)
 * @param jsonMode - Whether to use JSON mode
 * @returns Generated entity types as string or array
 */
export async function generateEntityTypes(
    model: ChatModel,
    domain: string,
    persona: string,
    docs: string | string[],
    task: string = DEFAULT_TASK,
    jsonMode: boolean = false
): Promise<string | string[]> {
    const formattedTask = task.replace('{domain}', domain);
    
    const docsStr = Array.isArray(docs) ? docs.join('\n') : docs;
    
    const entityTypesPrompt = (jsonMode 
        ? ENTITY_TYPE_GENERATION_JSON_PROMPT 
        : ENTITY_TYPE_GENERATION_PROMPT
    )
        .replace('{task}', formattedTask)
        .replace('{input_text}', docsStr);
    
    const history = [{ role: 'system', content: persona }];
    
    if (jsonMode) {
        const response = await model.achat(
            entityTypesPrompt,
            {
                history,
                json: jsonMode,
                jsonModel: 'EntityTypesResponse' // TypeScript doesn't have runtime model validation like Pydantic
            }
        );
        
        // Parse the JSON response manually since we don't have Pydantic
        try {
            const parsedResponse = JSON.parse(response.output.content || '{}') as EntityTypesResponse;
            return parsedResponse.entity_types || [];
        } catch (error) {
            console.warn('Failed to parse JSON response:', error);
            return [];
        }
    }
    
    const response = await model.achat(entityTypesPrompt, { history, json: jsonMode });
    return response.output.content || '';
}

/**
 * Validate entity types response.
 */
export function validateEntityTypesResponse(data: any): data is EntityTypesResponse {
    return (
        typeof data === 'object' &&
        data !== null &&
        Array.isArray(data.entity_types) &&
        data.entity_types.every((item: any) => typeof item === 'string')
    );
}

/**
 * Create a default EntityTypesResponse.
 */
export function createEntityTypesResponse(entityTypes: string[] = []): EntityTypesResponse {
    return {
        entity_types: entityTypes
    };
}
