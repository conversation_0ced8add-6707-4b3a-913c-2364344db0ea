/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * CLI implementation of the initialization subcommand.
 */

import * as fs from 'fs';
import * as pathModule from 'path';
import { INIT_DOTENV, INIT_YAML } from '../config/init_content';
import {
    COMMUNITY_REPORT_PROMPT,
} from '../prompts/index/community_report';
import {
    COMMUNITY_REPORT_TEXT_PROMPT,
} from '../prompts/index/community_report_text_units';
import { EXTRACT_CLAIMS_PROMPT } from '../prompts/index/extract_claims';
import { GRAPH_EXTRACTION_PROMPT } from '../prompts/index/extract_graph';
import { SUMMARIZE_PROMPT } from '../prompts/index/summarize_descriptions';
import { BASIC_SEARCH_SYSTEM_PROMPT } from '../prompts/query/basic_search_system_prompt';
import {
    DRIFT_LOCAL_SYSTEM_PROMPT,
    DRIFT_REDUCE_PROMPT,
} from '../prompts/query/drift_search_system_prompt';
import {
    GENERAL_KNOWLEDGE_INSTRUCTION,
} from '../prompts/query/global_search_knowledge_system_prompt';
import { MAP_SYSTEM_PROMPT } from '../prompts/query/global_search_map_system_prompt';
import {
    REDUCE_SYSTEM_PROMPT,
} from '../prompts/query/global_search_reduce_system_prompt';
import { LOCAL_SEARCH_SYSTEM_PROMPT } from '../prompts/query/local_search_system_prompt';
import { QUESTION_SYSTEM_PROMPT } from '../prompts/query/question_gen_system_prompt';

const logger = console;

/**
 * Initialize the project at the given path.
 *
 * @param path - The path at which to initialize the project.
 * @param force - Whether to force initialization even if the project already exists.
 * @throws {Error} If the project already exists and force is false.
 */
export function initialize_project_at(path: string, force: boolean): void {
    logger.info(`Initializing project at ${path}`);

    const root = pathModule.resolve(path);
    if (!fs.existsSync(root)) {
        fs.mkdirSync(root, { recursive: true });
    }

    const settingsYaml = pathModule.join(root, 'settings.yaml');
    if (fs.existsSync(settingsYaml) && !force) {
        throw new Error(`Project already initialized at ${root}`);
    }

    fs.writeFileSync(settingsYaml, INIT_YAML, 'utf-8');

    const dotenv = pathModule.join(root, '.env');
    if (!fs.existsSync(dotenv) || force) {
        fs.writeFileSync(dotenv, INIT_DOTENV, 'utf-8');
    }

    const promptsDir = pathModule.join(root, 'prompts');
    if (!fs.existsSync(promptsDir)) {
        fs.mkdirSync(promptsDir, { recursive: true });
    }

    const prompts: Record<string, string> = {
        'extract_graph': GRAPH_EXTRACTION_PROMPT,
        'summarize_descriptions': SUMMARIZE_PROMPT,
        'extract_claims': EXTRACT_CLAIMS_PROMPT,
        'community_report_graph': COMMUNITY_REPORT_PROMPT,
        'community_report_text': COMMUNITY_REPORT_TEXT_PROMPT,
        'drift_search_system_prompt': DRIFT_LOCAL_SYSTEM_PROMPT,
        'drift_reduce_prompt': DRIFT_REDUCE_PROMPT,
        'global_search_map_system_prompt': MAP_SYSTEM_PROMPT,
        'global_search_reduce_system_prompt': REDUCE_SYSTEM_PROMPT,
        'global_search_knowledge_system_prompt': GENERAL_KNOWLEDGE_INSTRUCTION,
        'local_search_system_prompt': LOCAL_SEARCH_SYSTEM_PROMPT,
        'basic_search_system_prompt': BASIC_SEARCH_SYSTEM_PROMPT,
        'question_gen_system_prompt': QUESTION_SYSTEM_PROMPT,
    };

    for (const [name, content] of Object.entries(prompts)) {
        const promptFile = pathModule.join(promptsDir, `${name}.txt`);
        if (!fs.existsSync(promptFile) || force) {
            fs.writeFileSync(promptFile, content, 'utf-8');
        }
    }
}