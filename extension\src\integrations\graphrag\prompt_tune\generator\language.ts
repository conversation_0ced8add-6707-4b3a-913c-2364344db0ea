// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Language detection for GraphRAG prompts.
 */

import { ChatModel } from '../../language_model/protocol/base';
import { DETECT_LANGUAGE_PROMPT } from '../prompt/language';

/**
 * Detect input language to use for GraphRAG prompts.
 * 
 * @param model - The LLM to use for generation
 * @param docs - The docs to detect language from
 * @returns The detected language
 */
export async function detectLanguage(model: ChatModel, docs: string | string[]): Promise<string> {
    const docsStr = Array.isArray(docs) ? docs.join(' ') : docs;
    const languagePrompt = DETECT_LANGUAGE_PROMPT.replace('{input_text}', docsStr);

    const response = await model.achat(languagePrompt);

    return response.output.content || '';
}

/**
 * Common language codes and their names.
 */
export const LANGUAGE_CODES = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'nl': 'Dutch',
    'sv': 'Swedish',
    'no': 'Norwegian',
    'da': 'Danish',
    'fi': 'Finnish',
    'pl': 'Polish',
    'tr': 'Turkish',
    'he': 'Hebrew'
} as const;

export type LanguageCode = keyof typeof LANGUAGE_CODES;

/**
 * Detect language with validation and normalization.
 */
export async function detectLanguageWithValidation(
    model: ChatModel, 
    docs: string | string[]
): Promise<{ language: string; languageCode?: LanguageCode; confidence: 'high' | 'medium' | 'low' }> {
    const detectedLanguage = await detectLanguage(model, docs);
    const normalizedLanguage = detectedLanguage.toLowerCase().trim();
    
    // Try to find matching language code
    let languageCode: LanguageCode | undefined;
    let confidence: 'high' | 'medium' | 'low' = 'low';
    
    // Direct match with language codes
    if (normalizedLanguage in LANGUAGE_CODES) {
        languageCode = normalizedLanguage as LanguageCode;
        confidence = 'high';
    } else {
        // Try to match with language names
        for (const [code, name] of Object.entries(LANGUAGE_CODES)) {
            if (normalizedLanguage.includes(name.toLowerCase()) || name.toLowerCase().includes(normalizedLanguage)) {
                languageCode = code as LanguageCode;
                confidence = 'medium';
                break;
            }
        }
    }
    
    return {
        language: detectedLanguage,
        languageCode,
        confidence
    };
}

/**
 * Detect language from multiple document samples.
 */
export async function detectLanguageFromSamples(
    model: ChatModel,
    docs: string[],
    maxSamples: number = 3
): Promise<{ languages: string[]; mostCommon: string }> {
    const samples = docs.slice(0, maxSamples);
    const detectionTasks = samples.map(doc => detectLanguage(model, doc));
    
    const languages = await Promise.all(detectionTasks);
    
    // Find most common language
    const languageCounts = languages.reduce((counts, lang) => {
        const normalized = lang.toLowerCase().trim();
        counts[normalized] = (counts[normalized] || 0) + 1;
        return counts;
    }, {} as Record<string, number>);
    
    const mostCommon = Object.entries(languageCounts)
        .sort(([, a], [, b]) => b - a)[0]?.[0] || 'english';
    
    return {
        languages,
        mostCommon
    };
}
