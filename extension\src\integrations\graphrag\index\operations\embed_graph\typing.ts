/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing different lists and dictionaries.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * List of node names
 */
export type NodeList = string[];

/**
 * List of embeddings (generic type for flexibility)
 */
export type EmbeddingList = any[];

/**
 * Mapping from node labels to their embedding vectors
 * Label -> Embedding
 */
export type NodeEmbeddings = Record<string, number[]>;
