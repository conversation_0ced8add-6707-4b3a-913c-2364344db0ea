/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Entity related operations and utils for Incremental Indexing.
 */

import { DataFrame } from '../../data-model/types';
import { ENTITIES_FINAL_COLUMNS } from '../../data-model/schemas';

/**
 * Group and resolve entities.
 * @param oldEntitiesDF - The first dataframe
 * @param deltaEntitiesDF - The delta dataframe
 * @returns Tuple of [resolved dataframe, id mapping for existing entities]
 */
export function groupAndResolveEntities(
    oldEntitiesDF: DataFrame,
    deltaEntitiesDF: DataFrame
): [DataFrame, Record<string, string>] {
    // Create mapping for entities with same title
    const idMapping: Record<string, string> = {};
    
    // Find entities in delta that have same title as old entities
    deltaEntitiesDF.data.forEach(deltaRow => {
        const matchingOldRow = oldEntitiesDF.data.find(oldRow => 
            oldRow.title === deltaRow.title
        );
        if (matchingOldRow) {
            idMapping[deltaRow.id] = matchingOldRow.id;
        }
    });

    // Increment human readable id in delta by the max of old
    const maxHumanReadableId = Math.max(
        ...oldEntitiesDF.data.map(row => parseInt(String(row.human_readable_id), 10))
    );
    
    const updatedDeltaData = deltaEntitiesDF.data.map((row, index) => ({
        ...row,
        human_readable_id: maxHumanReadableId + 1 + index
    }));

    // Combine old and delta data
    const combinedData = [...oldEntitiesDF.data, ...updatedDeltaData];

    // Group by title and resolve conflicts
    const groupedByTitle = new Map<string, any[]>();
    combinedData.forEach(row => {
        const title = row.title;
        if (!groupedByTitle.has(title)) {
            groupedByTitle.set(title, []);
        }
        groupedByTitle.get(title)!.push(row);
    });

    // Aggregate grouped data
    const aggregatedData: any[] = [];
    groupedByTitle.forEach((rows, title) => {
        const firstRow = rows[0];
        
        // Collect all descriptions and text_unit_ids
        const descriptions: string[] = [];
        const textUnitIds: string[] = [];
        
        rows.forEach(row => {
            if (row.description) {
                descriptions.push(String(row.description));
            }
            if (row.text_unit_ids && Array.isArray(row.text_unit_ids)) {
                textUnitIds.push(...row.text_unit_ids);
            }
        });

        const aggregatedRow = {
            title: title,
            id: firstRow.id,
            type: firstRow.type,
            human_readable_id: firstRow.human_readable_id,
            description: descriptions,
            text_unit_ids: textUnitIds,
            degree: firstRow.degree,
            x: firstRow.x,
            y: firstRow.y,
            frequency: textUnitIds.length // Recompute frequency based on text units
        };

        aggregatedData.push(aggregatedRow);
    });

    // Filter to final columns
    const finalColumns = ENTITIES_FINAL_COLUMNS;
    const filteredData = aggregatedData.map(row => {
        const newRow: Record<string, any> = {};
        finalColumns.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });

    return [
        {
            columns: finalColumns,
            data: filteredData
        },
        idMapping
    ];
}