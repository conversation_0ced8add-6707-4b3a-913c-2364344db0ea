// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * No-op Query Callbacks.
 */

import { QueryCallbacks } from './query_callbacks';
import { SearchResult } from '../query/structured_search/base';

/**
 * A no-op implementation of QueryCallbacks.
 */
export class NoopQueryCallbacks implements QueryCallbacks {
    /**
     * Handle when context data is constructed.
     */
    on_context(context: any): void {
        // No-op implementation
    }

    /**
     * Handle the start of map operation.
     */
    on_map_response_start(map_response_contexts: string[]): void {
        // No-op implementation
    }

    /**
     * Handle the end of map operation.
     */
    on_map_response_end(map_response_outputs: SearchResult[]): void {
        // No-op implementation
    }

    /**
     * Handle the start of reduce operation.
     */
    on_reduce_response_start(reduce_response_context: string | Record<string, any>): void {
        // No-op implementation
    }

    /**
     * Handle the end of reduce operation.
     */
    on_reduce_response_end(reduce_response_output: string): void {
        // No-op implementation
    }

    /**
     * Handle when a new token is generated.
     */
    on_llm_new_token(token: string): void {
        // No-op implementation
    }
}