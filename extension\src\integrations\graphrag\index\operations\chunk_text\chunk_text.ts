/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing _get_num_total, chunk, run_strategy and load_strategy methods definitions.
 */

import { DataFrame } from '../../../data_model/types.js';
import { WorkflowCallbacks } from '../../../callbacks/workflow_callbacks.js';
import { ChunkingConfig } from '../../../config/models/chunking_config.js';
import { ChunkStrategyType } from '../../../config/enums.js';
import { ChunkInput, ChunkStrategy, TextChunk } from './typing.js';
import { runTokens, runSentences } from './strategies.js';
import { bootstrap } from './bootstrap.js';

/**
 * Chunk a piece of text into smaller pieces.
 * 
 * ## Usage
 * ```yaml
 * args:
 *     column: <column name> # The name of the column containing the text to chunk
 *     strategy: <strategy config> # The strategy to use to chunk the text
 * ```
 * 
 * ## Strategies
 * The text chunk verb uses a strategy to chunk the text.
 * 
 * ### tokens
 * This strategy uses tokens to chunk a piece of text.
 * 
 * ### sentence
 * This strategy chunks text into sentences.
 */
export function chunkText(
    input: DataFrame,
    column: string,
    size: number,
    overlap: number,
    encodingModel: string,
    strategy: ChunkStrategyType,
    callbacks: WorkflowCallbacks,
): any[] {
    const strategyExec = loadStrategy(strategy);
    const numTotal = getNumTotal(input, column);
    
    let completed = 0;
    const tick = (increment: number) => {
        completed += increment;
        if (callbacks.progress) {
            callbacks.progress(completed / numTotal, 'Chunking text');
        }
    };

    // Create config object
    const config: ChunkingConfig = {
        size: size,
        overlap: overlap,
        encodingModel: encodingModel,
        groupByColumns: [],
        strategy: ChunkStrategyType.TOKENS,
        prependMetadata: false,
        chunkSizeIncludesMetadata: false
    };

    // Process each row
    return input.data.map(row => {
        return runStrategy(strategyExec, row[column], config, tick);
    });
}

/**
 * Run strategy method definition.
 */
export function runStrategy(
    strategyExec: ChunkStrategy,
    input: ChunkInput,
    config: ChunkingConfig,
    tick: (increment: number) => void,
): Array<string | [string[], string, number]> {
    if (typeof input === 'string') {
        const chunks = Array.from(strategyExec([input], config, tick));
        return chunks.map(item => item.text_chunk);
    }

    // Handle array inputs
    const texts = Array.isArray(input) 
        ? input.map(item => typeof item === 'string' ? item : item[1])
        : [];

    const strategyResults = Array.from(strategyExec(texts, config, tick));

    const results: Array<string | [string[] | null, string, number]> = [];
    
    for (const strategyResult of strategyResults) {
        const docIndices = strategyResult.source_doc_indices;

        if (Array.isArray(input) && typeof input[docIndices[0]] === 'string') {
            results.push(strategyResult.text_chunk);
        } else if (Array.isArray(input)) {
            const docIds = docIndices.map(docIdx => {
                const item = input[docIdx];
                return Array.isArray(item) ? item[0] : '';
            });
            results.push([
                docIds,
                strategyResult.text_chunk,
                strategyResult.n_tokens || 0,
            ]);
        }
    }
    
    return results;
}

/**
 * Load strategy method definition.
 */
export function loadStrategy(strategy: ChunkStrategyType): ChunkStrategy {
    switch (strategy) {
        case ChunkStrategyType.tokens:
            return runTokens;
        case ChunkStrategyType.sentence:
            bootstrap();
            return runSentences;
        default:
            throw new Error(`Unknown strategy: ${strategy}`);
    }
}

/**
 * Get total number of items to process.
 */
function getNumTotal(output: DataFrame, column: string): number {
    let numTotal = 0;
    
    for (const row of output.data) {
        const value = row[column];
        if (typeof value === 'string') {
            numTotal += 1;
        } else if (Array.isArray(value)) {
            numTotal += value.length;
        }
    }
    
    return numTotal;
}