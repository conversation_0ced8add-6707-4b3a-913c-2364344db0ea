/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module for producing a stable largest connected component, i.e. same input graph == same output lcc.
 */

import { Graph } from './graphs';

/**
 * Return the largest connected component of the graph, with nodes and edges sorted in a stable way.
 * @param graph - Input graph
 * @returns Stabilized largest connected component
 */
export function stableLargestConnectedComponent(graph: Graph): Graph {
    // Create a copy of the graph
    const graphCopy: Graph = {
        nodes: new Map(graph.nodes),
        edges: new Map(graph.edges)
    };

    // Find largest connected component (simplified implementation)
    const lcc = findLargestConnectedComponent(graphCopy);
    
    // Normalize node names
    const normalizedGraph = normalizeNodeNames(lcc);
    
    // Stabilize the graph
    return stabilizeGraph(normalizedGraph);
}

/**
 * Find the largest connected component (simplified implementation).
 */
function findLargestConnectedComponent(graph: Graph): Graph {
    // This is a simplified implementation
    // In a real implementation, you would use proper graph algorithms
    return graph;
}

/**
 * Ensure an undirected graph with the same relationships will always be read the same way.
 */
function stabilizeGraph(graph: Graph): Graph {
    const fixedGraph: Graph = {
        nodes: new Map(),
        edges: new Map()
    };

    // Sort nodes by key
    const sortedNodes = Array.from(graph.nodes.entries()).sort((a, b) => a[0].localeCompare(b[0]));
    sortedNodes.forEach(([key, value]) => {
        fixedGraph.nodes.set(key, value);
    });

    // Sort edges in a stable way
    const edges = Array.from(graph.edges.entries());
    
    // For undirected graphs, ensure consistent source/target ordering
    const normalizedEdges = edges.map(([key, edge]) => {
        let { source, target, weight, data } = edge;
        
        // Sort source and target alphabetically for consistency
        if (source > target) {
            [source, target] = [target, source];
        }
        
        const newKey = getEdgeKey(source, target);
        return [newKey, { source, target, weight, data }] as [string, typeof edge];
    });

    // Sort edges by key
    normalizedEdges.sort((a, b) => a[0].localeCompare(b[0]));
    
    normalizedEdges.forEach(([key, edge]) => {
        fixedGraph.edges.set(key, edge);
    });

    return fixedGraph;
}

/**
 * Normalize node names.
 */
function normalizeNodeNames(graph: Graph): Graph {
    const normalizedGraph: Graph = {
        nodes: new Map(),
        edges: new Map()
    };

    // Create mapping from old names to normalized names
    const nodeMapping = new Map<string, string>();
    graph.nodes.forEach((value, key) => {
        const normalizedKey = htmlUnescape(key.toUpperCase().trim());
        nodeMapping.set(key, normalizedKey);
        normalizedGraph.nodes.set(normalizedKey, value);
    });

    // Update edges with normalized node names
    graph.edges.forEach((edge, key) => {
        const normalizedSource = nodeMapping.get(edge.source) || edge.source;
        const normalizedTarget = nodeMapping.get(edge.target) || edge.target;
        const newKey = getEdgeKey(normalizedSource, normalizedTarget);
        
        normalizedGraph.edges.set(newKey, {
            ...edge,
            source: normalizedSource,
            target: normalizedTarget
        });
    });

    return normalizedGraph;
}

/**
 * Get edge key for consistent edge identification.
 */
function getEdgeKey(source: string, target: string): string {
    return `${source} -> ${target}`;
}

/**
 * Simple HTML unescape function.
 */
function htmlUnescape(text: string): string {
    return text
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");
}