// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Prepare text units for community reports.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../../data_model/types.js';
import * as schemas from '../../../../data_model/schemas.js';

/**
 * Calculate text unit degree and concatenate text unit details.
 * Matches the Python prep_text_units function exactly.
 *
 * Returns: dataframe with columns [COMMUNITY_ID, TEXT_UNIT_ID, ALL_DETAILS]
 */
export function prep_text_units(
  text_unit_df: DataFrame,
  node_df: DataFrame
): DataFrame {
  // Python: node_without_id = node_df.drop(columns=["id"])
  const node_without_id_data = node_df.data.map(row => {
    const { id, ...rest } = row;
    return rest;
  });
  const node_without_id = {
    columns: node_df.columns.filter(col => col !== 'id'),
    data: node_without_id_data
  };

  // Python: node_to_text_ids = node_without_id.explode(schemas.TEXT_UNIT_IDS).rename(columns={schemas.TEXT_UNIT_IDS: schemas.ID})
  const exploded_data: Record<string, any>[] = [];
  node_without_id.data.forEach(row => {
    const text_unit_ids = row[schemas.TEXT_UNIT_IDS];
    if (Array.isArray(text_unit_ids)) {
      text_unit_ids.forEach(text_unit_id => {
        exploded_data.push({
          ...row,
          [schemas.ID]: text_unit_id
        });
      });
    }
  });

  const node_to_text_ids_data = exploded_data.map(row => ({
    [schemas.TITLE]: row[schemas.TITLE],
    [schemas.COMMUNITY_ID]: row[schemas.COMMUNITY_ID],
    [schemas.NODE_DEGREE]: row[schemas.NODE_DEGREE],
    [schemas.ID]: row[schemas.ID]
  }));

  const node_to_text_ids = {
    columns: [schemas.TITLE, schemas.COMMUNITY_ID, schemas.NODE_DEGREE, schemas.ID],
    data: node_to_text_ids_data
  };

  // Python: text_unit_degrees = node_to_text_ids.groupby([schemas.COMMUNITY_ID, schemas.ID]).agg({schemas.NODE_DEGREE: "sum"}).reset_index()
  const degree_groups = new Map<string, Record<string, any>[]>();
  node_to_text_ids.data.forEach(row => {
    const key = `${row[schemas.COMMUNITY_ID]}|${row[schemas.ID]}`;
    if (!degree_groups.has(key)) {
      degree_groups.set(key, []);
    }
    degree_groups.get(key)!.push(row);
  });

  const text_unit_degrees_data: Record<string, any>[] = [];
  degree_groups.forEach((rows, key) => {
    const first_row = rows[0];
    const total_degree = rows.reduce((sum, row) => sum + (row[schemas.NODE_DEGREE] || 0), 0);
    text_unit_degrees_data.push({
      [schemas.COMMUNITY_ID]: first_row[schemas.COMMUNITY_ID],
      [schemas.ID]: first_row[schemas.ID],
      [schemas.NODE_DEGREE]: total_degree
    });
  });

  const text_unit_degrees = {
    columns: [schemas.COMMUNITY_ID, schemas.ID, schemas.NODE_DEGREE],
    data: text_unit_degrees_data
  };

  // Python: result_df = text_unit_df.merge(text_unit_degrees, on=schemas.ID, how="left")
  const result_data = text_unit_df.data.map(text_row => {
    const matching_degree = text_unit_degrees.data.find(deg_row => deg_row[schemas.ID] === text_row[schemas.ID]);
    return {
      ...text_row,
      [schemas.NODE_DEGREE]: matching_degree ? matching_degree[schemas.NODE_DEGREE] : 0,
      [schemas.COMMUNITY_ID]: matching_degree ? matching_degree[schemas.COMMUNITY_ID] : null
    };
  });

  // Python: final_df[schemas.ALL_DETAILS] = final_df.apply(lambda row: {...}, axis=1)
  result_data.forEach(row => {
    row[schemas.ALL_DETAILS] = {
      [schemas.SHORT_ID]: row[schemas.SHORT_ID],
      [schemas.TEXT]: row[schemas.TEXT],
      [schemas.ENTITY_DEGREE]: row[schemas.NODE_DEGREE],
    };
  });

  // Python: return final_df[[schemas.COMMUNITY_ID, schemas.ID, schemas.ALL_DETAILS]]
  const final_data = result_data.map(row => ({
    [schemas.COMMUNITY_ID]: row[schemas.COMMUNITY_ID],
    [schemas.ID]: row[schemas.ID],
    [schemas.ALL_DETAILS]: row[schemas.ALL_DETAILS]
  }));

  return {
    columns: [schemas.COMMUNITY_ID, schemas.ID, schemas.ALL_DETAILS],
    data: final_data
  };
}

// Compatibility export for existing code
export const prepTextUnits = prep_text_units;
