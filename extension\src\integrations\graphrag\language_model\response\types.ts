// Copyright (c) 2025 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Type definitions for language model response.
 */

/**
 * Protocol for Model response's output object.
 */
export interface ModelOutput {
    /** The textual content of the output. */
    readonly content: string;
    /** The complete JSON response returned by the model. */
    readonly fullResponse: Record<string, any> | null;
}

/**
 * Protocol for LLM response.
 */
export interface ModelResponse<T = any> {
    /** The output of the response. */
    readonly output: ModelOutput;
    /** The parsed response. */
    readonly parsedResponse: T | null;
    /** The history of the response. */
    readonly history: any[];
}

/**
 * Constructor options for BaseModelOutput.
 */
export interface BaseModelOutputOptions {
    content: string;
    fullResponse?: Record<string, any> | null;
}

/**
 * Constructor options for BaseModelResponse.
 */
export interface BaseModelResponseOptions<T = any> {
    output: ModelOutput;
    parsedResponse?: T | null;
    history?: any[];
    toolCalls?: any[];
    metrics?: any | null;
    cacheHit?: boolean | null;
}

/**
 * Base model output type definition.
 */
export type BaseModelOutputType = {
    content: string;
    fullResponse: Record<string, any> | null;
};

/**
 * Base model response type definition.
 */
export type BaseModelResponseType<T = any> = {
    output: ModelOutput;
    parsedResponse: T | null;
    history: any[];
    toolCalls: any[];
    metrics: any | null;
    cacheHit: boolean | null;
};
