/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing compute_degree definition.
 */

import { DataFrame } from '../../data-model/types';
import { Graph } from '../utils/graphs';

/**
 * Create a new DataFrame with the degree of each node in the graph.
 * @param graph - The graph to compute degrees for
 * @returns DataFrame with node titles and their degrees
 */
export function computeDegree(graph: Graph): DataFrame {
    const degreeMap = new Map<string, number>();
    
    // Initialize all nodes with degree 0
    for (const nodeId of graph.nodes.keys()) {
        degreeMap.set(nodeId, 0);
    }
    
    // Count degrees from edges
    for (const edge of graph.edges.values()) {
        const sourceDegree = degreeMap.get(edge.source) || 0;
        const targetDegree = degreeMap.get(edge.target) || 0;
        
        degreeMap.set(edge.source, sourceDegree + 1);
        degreeMap.set(edge.target, targetDegree + 1);
    }
    
    // Convert to DataFrame format
    const data = Array.from(degreeMap.entries()).map(([title, degree]) => ({
        title,
        degree
    }));
    
    return {
        columns: ['title', 'degree'],
        data
    };
}