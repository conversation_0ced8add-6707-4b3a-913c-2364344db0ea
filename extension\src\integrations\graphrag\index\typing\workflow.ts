/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Pipeline workflow types.
 */

import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { PipelineRunContext } from './context';

/**
 * Data container for Workflow function results.
 */
export interface WorkflowFunctionOutput {
    /** The result of the workflow function. This can be anything - we use it only for logging downstream, and expect each workflow function to write official outputs to the provided storage */
    result: any | null;
    
    /** Flag to indicate if the workflow should stop after this function. This should only be used when continuation could cause an unstable failure */
    stop?: boolean;
}

/**
 * Workflow function type
 */
export type WorkflowFunction = (
    config: GraphRagConfig,
    context: PipelineRunContext
) => Promise<WorkflowFunctionOutput>;

/**
 * Workflow tuple type - [name, function]
 */
export type Workflow = [string, WorkflowFunction];