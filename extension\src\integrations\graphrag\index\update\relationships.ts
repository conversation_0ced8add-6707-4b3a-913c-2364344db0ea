/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Relationship related operations and utils for Incremental Indexing.
 */

import { DataFrame } from '../../data-model/types';
import { RELATIONSHIPS_FINAL_COLUMNS } from '../../data-model/schemas';

/**
 * Update and merge relationships.
 * @param oldRelationships - The old relationships
 * @param deltaRelationships - The delta relationships
 * @returns Updated relationships
 */
export function updateAndMergeRelationships(
    oldRelationships: DataFrame,
    deltaRelationships: DataFrame
): DataFrame {
    // Ensure human_readable_id columns are integers
    const oldData = oldRelationships.data.map(row => ({
        ...row,
        human_readable_id: parseInt(String(row.human_readable_id), 10)
    }));

    // Find max human readable ID from old relationships
    const maxHumanReadableId = Math.max(
        ...oldData.map(row => row.human_readable_id)
    );

    // Adjust delta relationships IDs to be greater than any in old relationships
    const updatedDeltaData = deltaRelationships.data.map((row, index) => ({
        ...row,
        human_readable_id: maxHumanReadableId + 1 + index
    }));

    // Merge the DataFrames
    const mergedData = [...oldData, ...updatedDeltaData];

    // Group by source and target and resolve conflicts
    const groupedBySourceTarget = new Map<string, any[]>();
    mergedData.forEach(row => {
        const key = `${row.source}|${row.target}`;
        if (!groupedBySourceTarget.has(key)) {
            groupedBySourceTarget.set(key, []);
        }
        groupedBySourceTarget.get(key)!.push(row);
    });

    // Aggregate grouped data
    const aggregatedData: any[] = [];
    groupedBySourceTarget.forEach((rows, key) => {
        const [source, target] = key.split('|');
        const firstRow = rows[0];
        
        // Collect all descriptions and text_unit_ids
        const descriptions: string[] = [];
        const textUnitIds: string[] = [];
        let totalWeight = 0;
        let totalCombinedDegree = 0;
        
        rows.forEach(row => {
            if (row.description) {
                descriptions.push(String(row.description));
            }
            if (row.text_unit_ids && Array.isArray(row.text_unit_ids)) {
                textUnitIds.push(...row.text_unit_ids);
            }
            if (row.weight) {
                totalWeight += parseFloat(String(row.weight));
            }
            if (row.combined_degree) {
                totalCombinedDegree += parseFloat(String(row.combined_degree));
            }
        });

        const aggregatedRow = {
            source: source,
            target: target,
            id: firstRow.id,
            human_readable_id: firstRow.human_readable_id,
            description: descriptions,
            text_unit_ids: textUnitIds,
            weight: totalWeight / rows.length, // Mean weight
            combined_degree: totalCombinedDegree
        };

        aggregatedData.push(aggregatedRow);
    });

    // Calculate source and target degrees
    const sourceDegrees = new Map<string, number>();
    const targetDegrees = new Map<string, number>();

    aggregatedData.forEach(row => {
        // Count occurrences as source
        sourceDegrees.set(row.source, (sourceDegrees.get(row.source) || 0) + 1);
        // Count occurrences as target
        targetDegrees.set(row.target, (targetDegrees.get(row.target) || 0) + 1);
    });

    // Add degree information to each row
    const finalData = aggregatedData.map(row => ({
        ...row,
        source_degree: sourceDegrees.get(row.source) || 0,
        target_degree: targetDegrees.get(row.target) || 0,
        combined_degree: (sourceDegrees.get(row.source) || 0) + (targetDegrees.get(row.target) || 0)
    }));

    // Filter to final columns
    const finalColumns = RELATIONSHIPS_FINAL_COLUMNS;
    const filteredData = finalData.map(row => {
        const newRow: Record<string, any> = {};
        finalColumns.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });

    return {
        columns: finalColumns,
        data: filteredData
    };
}