/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * Errors for the default configuration.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * LLM Key missing error.
 */
export class ApiKeyMissingError extends Error {
    constructor(llmType: string, azureAuthType?: string) {
        let msg = `API Key is required for ${llmType}`
        if (azureAuthType) {
            msg += ` when using ${azureAuthType} authentication`
        }
        msg += ". Please rerun `graphrag init` and set the API_KEY."
        super(msg)
        this.name = "ApiKeyMissingError"
    }
}

/**
 * Azure API Base missing error.
 */
export class AzureApiBaseMissingError extends Error {
    constructor(llmType: string) {
        const msg = `API Base is required for ${llmType}. Please rerun \`graphrag init\` and set the api_base.`
        super(msg)
        this.name = "AzureApiBaseMissingError"
    }
}

/**
 * Azure API version missing error.
 */
export class AzureApiVersionMissingError extends Error {
    constructor(llmType: string) {
        const msg = `API Version is required for ${llmType}. Please rerun \`graphrag init\` and set the api_version.`
        super(msg)
        this.name = "AzureApiVersionMissingError"
    }
}

/**
 * Azure Deployment Name missing error.
 */
export class AzureDeploymentNameMissingError extends Error {
    constructor(llmType: string) {
        const msg = `Deployment name is required for ${llmType}. Please rerun \`graphrag init\` set the deployment_name.`
        super(msg)
        this.name = "AzureDeploymentNameMissingError"
    }
}

/**
 * Missing model configuration error.
 */
export class LanguageModelConfigMissingError extends Error {
    constructor(key: string = "") {
        const msg = `A ${key} model configuration is required. Please rerun \`graphrag init\` and set models["${key}"] in settings.yaml.`
        super(msg)
        this.name = "LanguageModelConfigMissingError"
    }
}

/**
 * Missing model configuration error.
 */
export class ConflictingSettingsError extends Error {
    constructor(msg: string) {
        super(msg)
        this.name = "ConflictingSettingsError"
    }
}