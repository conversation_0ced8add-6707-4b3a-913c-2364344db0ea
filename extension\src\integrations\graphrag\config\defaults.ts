/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * Common default configuration values.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { 
    AsyncType, 
    AuthType, 
    CacheType, 
    ChunkStrategyType, 
    InputFileType, 
    ModelType, 
    NounPhraseExtractorType, 
    ReportingType, 
    StorageType 
} from './enums'

// Constants
export const DEFAULT_OUTPUT_BASE_DIR = "output"
export const DEFAULT_CHAT_MODEL_ID = "default_chat_model"
export const DEFAULT_CHAT_MODEL_TYPE = ModelType.OPENAI_CHAT
export const DEFAULT_CHAT_MODEL = "gpt-4-turbo-preview"
export const DEFAULT_CHAT_MODEL_AUTH_TYPE = AuthType.API_KEY
export const DEFAULT_EMBEDDING_MODEL_ID = "default_embedding_model"
export const DEFAULT_EMBEDDING_MODEL_TYPE = ModelType.OPENAI_EMBEDDING
export const DEFAULT_EMBEDDING_MODEL = "text-embedding-3-small"
export const DEFAULT_EMBEDDING_MODEL_AUTH_TYPE = AuthType.API_KEY
export const DEFAULT_VECTOR_STORE_ID = "default_vector_store"

export const ENCODING_MODEL = "cl100k_base"
export const COGNITIVE_SERVICES_AUDIENCE = "https://cognitiveservices.azure.com/.default"

// English stop words (imported from the actual stop words module)
export const EN_STOP_WORDS = [
    "stuff",
    "thing",
    "things",
    "bunch",
    "bit",
    "bits",
    "people",
    "person",
    "okay",
    "hey",
    "hi",
    "hello",
    "laughter",
    "oh",
]

/**
 * Default values for basic search.
 */
export interface BasicSearchDefaults {
    prompt?: string
    k: number
    max_context_tokens: number
    chat_model_id: string
    embedding_model_id: string
}

export const basicSearchDefaults: BasicSearchDefaults = {
    prompt: undefined,
    k: 10,
    max_context_tokens: 12_000,
    chat_model_id: DEFAULT_CHAT_MODEL_ID,
    embedding_model_id: DEFAULT_EMBEDDING_MODEL_ID
}

/**
 * Default values for cache.
 */
export interface CacheDefaults {
    type: CacheType
    base_dir: string
    connection_string?: string
    container_name?: string
    storage_account_blob_url?: string
    cosmosdb_account_url?: string
}

export const cacheDefaults: CacheDefaults = {
    type: CacheType.FILE,
    base_dir: "cache",
    connection_string: undefined,
    container_name: undefined,
    storage_account_blob_url: undefined,
    cosmosdb_account_url: undefined
}

/**
 * Default values for chunks.
 */
export interface ChunksDefaults {
    size: number
    overlap: number
    group_by_columns: string[]
    strategy: ChunkStrategyType
    encoding_model: string
    prepend_metadata: boolean
    chunk_size_includes_metadata: boolean
}

export const chunksDefaults: ChunksDefaults = {
    size: 1200,
    overlap: 100,
    group_by_columns: ["id"],
    strategy: ChunkStrategyType.TOKENS,
    encoding_model: "cl100k_base",
    prepend_metadata: false,
    chunk_size_includes_metadata: false
}

/**
 * Default values for cluster graph.
 */
export interface ClusterGraphDefaults {
    max_cluster_size: number
    use_lcc: boolean
    seed: number
}

export const clusterGraphDefaults: ClusterGraphDefaults = {
    max_cluster_size: 10,
    use_lcc: true,
    seed: 0xDEADBEEF
}

/**
 * Default values for community report.
 */
export interface CommunityReportDefaults {
    graph_prompt?: string
    text_prompt?: string
    max_length: number
    max_input_length: number
    strategy?: string
    model_id: string
}

export const communityReportDefaults: CommunityReportDefaults = {
    graph_prompt: undefined,
    text_prompt: undefined,
    max_length: 2000,
    max_input_length: 8000,
    strategy: undefined,
    model_id: DEFAULT_CHAT_MODEL_ID
}

/**
 * Default values for embedding graph.
 */
export interface EmbedGraphDefaults {
    enabled: boolean
    dimensions: number
    num_walks: number
    walk_length: number
    window_size: number
    iterations: number
    random_seed: number
    use_lcc: boolean
}

export const embedGraphDefaults: EmbedGraphDefaults = {
    enabled: false,
    dimensions: 1536,
    num_walks: 10,
    walk_length: 40,
    window_size: 2,
    iterations: 3,
    random_seed: 597832,
    use_lcc: true
}

/**
 * Default values for embedding text.
 */
export interface EmbedTextDefaults {
    model: string
    batch_size: number
    batch_max_tokens: number
    model_id: string
    names: string[]
    strategy?: string
    vector_store_id: string
}

export const embedTextDefaults: EmbedTextDefaults = {
    model: "text-embedding-3-small",
    batch_size: 16,
    batch_max_tokens: 8191,
    model_id: DEFAULT_EMBEDDING_MODEL_ID,
    names: [], // Will be populated with default_embeddings
    strategy: undefined,
    vector_store_id: DEFAULT_VECTOR_STORE_ID
}

/**
 * Default values for language model.
 */
export interface LanguageModelDefaults {
    api_key?: string
    auth_type: AuthType
    encoding_model: string
    max_tokens?: number
    temperature: number
    max_completion_tokens?: number
    reasoning_effort?: string
    top_p: number
    n: number
    frequency_penalty: number
    presence_penalty: number
    request_timeout: number
    api_base?: string
    api_version?: string
    deployment_name?: string
    organization?: string
    proxy?: string
    audience?: string
    model_supports_json?: boolean
    tokens_per_minute: "auto"
    requests_per_minute: "auto"
    retry_strategy: string
    max_retries: number
    max_retry_wait: number
    concurrent_requests: number
    responses?: any
    async_mode: AsyncType
}

export const languageModelDefaults: LanguageModelDefaults = {
    api_key: undefined,
    auth_type: AuthType.API_KEY,
    encoding_model: "",
    max_tokens: undefined,
    temperature: 0,
    max_completion_tokens: undefined,
    reasoning_effort: undefined,
    top_p: 1,
    n: 1,
    frequency_penalty: 0.0,
    presence_penalty: 0.0,
    request_timeout: 180.0,
    api_base: undefined,
    api_version: undefined,
    deployment_name: undefined,
    organization: undefined,
    proxy: undefined,
    audience: undefined,
    model_supports_json: undefined,
    tokens_per_minute: "auto",
    requests_per_minute: "auto",
    retry_strategy: "native",
    max_retries: 10,
    max_retry_wait: 10.0,
    concurrent_requests: 25,
    responses: undefined,
    async_mode: AsyncType.THREADED
}

/**
 * Default values for storage.
 */
export interface StorageDefaults {
    type: StorageType
    base_dir: string
    connection_string?: string
    container_name?: string
    storage_account_blob_url?: string
    cosmosdb_account_url?: string
}

export const storageDefaults: StorageDefaults = {
    type: StorageType.FILE,
    base_dir: DEFAULT_OUTPUT_BASE_DIR,
    connection_string: undefined,
    container_name: undefined,
    storage_account_blob_url: undefined,
    cosmosdb_account_url: undefined
}

/**
 * Default values for input.
 */
export interface InputDefaults {
    storage: StorageDefaults
    file_type: InputFileType
    encoding: string
    file_pattern: string
    file_filter?: string
    text_column: string
    title_column?: string
    metadata?: any
}

export const inputDefaults: InputDefaults = {
    storage: {
        ...storageDefaults,
        base_dir: "input"
    },
    file_type: InputFileType.TEXT,
    encoding: "utf-8",
    file_pattern: "",
    file_filter: undefined,
    text_column: "text",
    title_column: undefined,
    metadata: undefined
}



/**
 * Text analyzer configuration defaults.
 */
export interface TextAnalyzerDefaults {
    extractor_type: NounPhraseExtractorType
    model_name: string
    max_word_length: number
    word_delimiter: string
    include_named_entities: boolean
    exclude_nouns: string[]
    exclude_entity_tags: string[]
    exclude_pos_tags: string[]
    noun_phrase_tags: string[]
    noun_phrase_grammars: Record<string, string>
}

export const textAnalyzerDefaults: TextAnalyzerDefaults = {
    extractor_type: NounPhraseExtractorType.REGEX_ENGLISH,
    model_name: "en_core_web_md",
    max_word_length: 15,
    word_delimiter: " ",
    include_named_entities: true,
    exclude_nouns: [...EN_STOP_WORDS],
    exclude_entity_tags: ["DATE"],
    exclude_pos_tags: ["DET", "PRON", "INTJ", "X"],
    noun_phrase_tags: ["PROPN", "NOUNS"],
    noun_phrase_grammars: {
        "PROPN,PROPN": "PROPN",
        "NOUN,NOUN": "NOUNS",
        "NOUNS,NOUN": "NOUNS",
        "ADJ,ADJ": "ADJ",
        "ADJ,NOUN": "NOUNS",
    }
}

/**
 * Extract graph NLP configuration defaults.
 */
export interface ExtractGraphNLPDefaults {
    normalize_edge_weights: boolean
    text_analyzer: TextAnalyzerDefaults
    concurrent_requests: number
}

export const extractGraphNlpDefaults: ExtractGraphNLPDefaults = {
    normalize_edge_weights: true,
    text_analyzer: textAnalyzerDefaults,
    concurrent_requests: 25
}

/**
 * Default values for extract graph.
 */
export interface ExtractGraphDefaults {
    model_id: string
    prompt?: string
    entity_types: string[]
    max_gleanings: number
    strategy?: Record<string, any>
}

export const extractGraphDefaults: ExtractGraphDefaults = {
    model_id: "default_chat",
    prompt: undefined,
    entity_types: ["PERSON", "ORGANIZATION", "LOCATION"],
    max_gleanings: 1,
    strategy: undefined
}

/**
 * Default values for summarize descriptions.
 */
export interface SummarizeDescriptionsDefaults {
    model_id: string
    prompt?: string
    max_length: number
    strategy?: Record<string, any>
}

export const summarizeDescriptionsDefaults: SummarizeDescriptionsDefaults = {
    model_id: "default_chat",
    prompt: undefined,
    max_length: 500,
    strategy: undefined
}

/**
 * Default values for community reports.
 */
export interface CommunityReportsDefaults {
    model_id: string
    prompt?: string
    max_length: number
    max_input_length: number
    strategy?: Record<string, any>
}

export const communityReportsDefaults: CommunityReportsDefaults = {
    model_id: "default_chat",
    prompt: undefined,
    max_length: 2000,
    max_input_length: 8000,
    strategy: undefined
}

/**
 * Default values for extract claims.
 */
export interface ExtractClaimsDefaults {
    enabled: boolean
    model_id: string
    prompt?: string
    description?: string
    max_gleanings: number
    strategy?: Record<string, any>
}

export const extractClaimsDefaults: ExtractClaimsDefaults = {
    enabled: false,
    model_id: "default_chat",
    prompt: undefined,
    description: undefined,
    max_gleanings: 1,
    strategy: undefined
}

/**
 * Default values for prune graph.
 */
export interface PruneGraphDefaults {
    min_node_freq: number
    max_node_freq_std?: number
    min_node_degree: number
    max_node_degree_std?: number
    min_edge_weight_pct: number
    remove_ego_nodes: boolean
    lcc_only: boolean
}

export const pruneGraphDefaults: PruneGraphDefaults = {
    min_node_freq: 2,
    max_node_freq_std: undefined,
    min_node_degree: 1,
    max_node_degree_std: undefined,
    min_edge_weight_pct: 40.0,
    remove_ego_nodes: true,
    lcc_only: false
}

/**
 * Default values for local search.
 */
export interface LocalSearchDefaults {
    model_id: string
    prompt?: string
    text_unit_prop: number
    community_prop: number
    conversation_history_max_turns: number
    top_k_mapped_entities: number
    top_k_relationships: number
    max_tokens: number
}

export const localSearchDefaults: LocalSearchDefaults = {
    model_id: "default_chat",
    prompt: undefined,
    text_unit_prop: 0.5,
    community_prop: 0.1,
    conversation_history_max_turns: 5,
    top_k_mapped_entities: 10,
    top_k_relationships: 10,
    max_tokens: 12000
}

/**
 * Default values for global search.
 */
export interface GlobalSearchDefaults {
    model_id: string
    prompt?: string
    max_tokens: number
    data_max_tokens: number
    map_max_tokens: number
    reduce_max_tokens: number
    concurrency: number
}

export const globalSearchDefaults: GlobalSearchDefaults = {
    model_id: "default_chat",
    prompt: undefined,
    max_tokens: 12000,
    data_max_tokens: 12000,
    map_max_tokens: 1000,
    reduce_max_tokens: 2000,
    concurrency: 32
}

/**
 * Default values for drift search.
 */
export interface DriftSearchDefaults {
    model_id: string
    prompt?: string
    max_tokens: number
}

export const driftSearchDefaults: DriftSearchDefaults = {
    model_id: "default_chat",
    prompt: undefined,
    max_tokens: 12000
}



/**
 * Default values for UMAP.
 */
export interface UmapDefaults {
    enabled: boolean
}

export const umapDefaults: UmapDefaults = {
    enabled: false
}

/**
 * Default values for vector store.
 */
export interface VectorStoreDefaults {
    type: string
    db_uri?: string
    url?: string
    api_key?: string
    audience?: string
    container_name: string
    database_name?: string
    overwrite: boolean
}

export const vectorStoreDefaults: VectorStoreDefaults = {
    type: "lancedb",
    db_uri: "lancedb",
    url: undefined,
    api_key: undefined,
    audience: undefined,
    container_name: "default",
    database_name: undefined,
    overwrite: true
}

/**
 * Default values for reporting.
 */
export interface ReportingDefaults {
    type: string
    base_dir: string
    connection_string?: string
    container_name?: string
    storage_account_blob_url?: string
}

export const reportingDefaults: ReportingDefaults = {
    type: "file",
    base_dir: "logs",
    connection_string: undefined,
    container_name: undefined,
    storage_account_blob_url: undefined
}

/**
 * Default values for snapshots.
 */
export interface SnapshotsDefaults {
    embeddings: boolean
    graphml: boolean
    raw_graph: boolean
}

export const snapshotsDefaults: SnapshotsDefaults = {
    embeddings: false,
    graphml: false,
    raw_graph: false
}

/**
 * Default values for update index output.
 */
export interface UpdateIndexOutputDefaults {
    base_dir: string
}

export const updateIndexOutputDefaults: UpdateIndexOutputDefaults = {
    base_dir: "update_index_output"
}

/**
 * Main GraphRAG configuration defaults.
 */
export interface GraphRagConfigDefaults {
    root_dir: string
    models: Record<string, any>
    reporting: ReportingDefaults
    storage: StorageDefaults
    output: StorageDefaults
    outputs?: Record<string, StorageDefaults>
    update_index_output: UpdateIndexOutputDefaults
    cache: CacheDefaults
    input: InputDefaults
    embed_graph: EmbedGraphDefaults
    embed_text: EmbedTextDefaults
    chunks: ChunksDefaults
    language_model: LanguageModelDefaults
    extract_graph_nlp: ExtractGraphNLPDefaults
    extract_graph: ExtractGraphDefaults
    summarize_descriptions: SummarizeDescriptionsDefaults
    community_reports: CommunityReportsDefaults
    extract_claims: ExtractClaimsDefaults
    prune_graph: PruneGraphDefaults
    cluster_graph: ClusterGraphDefaults
    local_search: LocalSearchDefaults
    global_search: GlobalSearchDefaults
    drift_search: DriftSearchDefaults
    basic_search: BasicSearchDefaults
    umap: UmapDefaults
    vector_store: Record<string, VectorStoreDefaults>
    workflows?: string[]
    snapshots: SnapshotsDefaults
}

export const graphragConfigDefaults: GraphRagConfigDefaults = {
    root_dir: "",
    models: {},
    reporting: reportingDefaults,
    storage: storageDefaults,
    output: storageDefaults,
    outputs: undefined,
    update_index_output: updateIndexOutputDefaults,
    cache: cacheDefaults,
    input: inputDefaults,
    embed_graph: embedGraphDefaults,
    embed_text: embedTextDefaults,
    chunks: chunksDefaults,
    language_model: languageModelDefaults,
    extract_graph_nlp: extractGraphNlpDefaults,
    extract_graph: extractGraphDefaults,
    summarize_descriptions: summarizeDescriptionsDefaults,
    community_reports: communityReportsDefaults,
    extract_claims: extractClaimsDefaults,
    prune_graph: pruneGraphDefaults,
    cluster_graph: clusterGraphDefaults,
    local_search: localSearchDefaults,
    global_search: globalSearchDefaults,
    drift_search: driftSearchDefaults,
    basic_search: basicSearchDefaults,
    umap: umapDefaults,
    vector_store: {
        "default": vectorStoreDefaults
    },
    workflows: undefined,
    snapshots: snapshotsDefaults
}