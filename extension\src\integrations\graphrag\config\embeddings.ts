// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing embeddings values.
 */

export const entityTitleEmbedding = "entity.title";
export const entityDescriptionEmbedding = "entity.description";
export const relationshipDescriptionEmbedding = "relationship.description";
export const documentTextEmbedding = "document.text";
export const communityTitleEmbedding = "community.title";
export const communitySummaryEmbedding = "community.summary";
export const communityFullContentEmbedding = "community.full_content";
export const textUnitTextEmbedding = "text_unit.text";

export const allEmbeddings: Set<string> = new Set([
    entityTitleEmbedding,
    entityDescriptionEmbedding,
    relationshipDescriptionEmbedding,
    documentTextEmbedding,
    communityTitleEmbedding,
    communitySummaryEmbedding,
    communityFullContentEmbedding,
    textUnitTextEmbedding,
]);

export const defaultEmbeddings: string[] = [
    entityDescriptionEmbedding,
    communityFullContentEmbedding,
    textUnitTextEmbedding,
];

/**
 * Create a collection name for the embedding store.
 *
 * Within any given vector store, we can have multiple sets of embeddings organized into projects.
 * The `container` param is used for this partitioning, and is added as a prefix to the collection name for differentiation.
 *
 * The embedding name is fixed, with the available list defined in graphrag.index.config.embeddings
 *
 * Note that we use dot notation in our names, but many vector stores do not support this - so we convert to dashes.
 */
export function createCollectionName(
    containerName: string,
    embeddingName: string,
    validate: boolean = true
): string {
    if (validate && !allEmbeddings.has(embeddingName)) {
        const msg = `Invalid embedding name: ${embeddingName}`;
        throw new Error(msg);
    }
    return `${containerName}-${embeddingName}`.replace(/\./g, "-");
}