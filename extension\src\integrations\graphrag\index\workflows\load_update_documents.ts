/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { InputConfig } from '../../config/models/input-config';
import { createInput } from '../input/factory';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { getDeltaDocs } from '../update/incremental-index';
import { PipelineStorage } from '../../storage/pipeline-storage';
import { writeTableToStorage } from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * Load and parse update-only input documents into a standard format.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    const output = await loadUpdateDocuments(
        config.input,
        context.inputStorage,
        context.previousStorage,
    );

    logger.info(`Final # of update rows loaded: ${output.length}`);
    context.stats.updateDocuments = output.length;

    if (output.length === 0) {
        logger.warn("No new update documents found.");
        return { result: null, stop: true };
    }

    await writeTableToStorage(output, "documents", context.outputStorage);

    return { result: output };
}

/**
 * Load and parse update-only input documents into a standard format.
 */
export async function loadUpdateDocuments(
    config: InputConfig,
    inputStorage: PipelineStorage,
    previousStorage: PipelineStorage,
): Promise<DataFrame> {
    const inputDocuments = await createInput(config, inputStorage);
    
    // Previous storage is the output of the previous run
    // We'll use this to diff the input from the prior
    const deltaDocuments = await getDeltaDocs(inputDocuments, previousStorage);
    
    return deltaDocuments.newInputs;
}