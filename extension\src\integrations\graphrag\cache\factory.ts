// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing create_cache method definition.
 */

import { CacheType } from '../config/enums';
import { PipelineCache } from './pipeline_cache';
import { JsonPipelineCache } from './json_pipeline_cache';
import { InMemoryCache } from './memory_pipeline_cache';
import { NoopPipelineCache } from './noop_pipeline_cache';
import { FilePipelineStorage } from '../storage/file_pipeline_storage';
import { createBlobStorage } from '../storage/blob_pipeline_storage';
import { createCosmosdbStorage } from '../storage/cosmosdb_pipeline_storage';

/**
 * A factory class for cache implementations.
 *
 * Includes a method for users to register a custom cache implementation.
 *
 * Configuration arguments are passed to each cache implementation as kwargs (where possible)
 * for individual enforcement of required/optional arguments.
 */
export class CacheFactory {
    static cacheTypes: Record<string, new (...args: any[]) => PipelineCache> = {};

    /**
     * Register a custom cache implementation.
     */
    static register(cacheType: string, cache: new (...args: any[]) => PipelineCache): void {
        this.cacheTypes[cacheType] = cache;
    }

    /**
     * Create or get a cache from the provided type.
     */
    static createCache(
        cacheType: CacheType | string | null | undefined,
        rootDir: string,
        kwargs: Record<string, any>
    ): PipelineCache {
        if (!cacheType) {
            return new NoopPipelineCache();
        }

        switch (cacheType) {
            case CacheType.none:
                return new NoopPipelineCache();
            case CacheType.memory:
                return new InMemoryCache();
            case CacheType.file:
                return new JsonPipelineCache(
                    new FilePipelineStorage({ rootDir }).child(kwargs.baseDir)
                );
            case CacheType.blob:
                return new JsonPipelineCache(createBlobStorage(kwargs));
            case CacheType.cosmosdb:
                return new JsonPipelineCache(createCosmosdbStorage(kwargs));
            default:
                if (cacheType in this.cacheTypes) {
                    const CacheClass = this.cacheTypes[cacheType];
                    return new CacheClass(kwargs);
                }
                const msg = `Unknown cache type: ${cacheType}`;
                throw new Error(msg);
        }
    }
}