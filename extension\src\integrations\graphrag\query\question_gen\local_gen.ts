// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Local question generation.
 */

import { ChatModel } from '../../language_model/protocol/base';
import { BaseLLMCallback } from '../../callbacks/llm-callbacks';
import { LocalContextBuilder, ContextBuilderResult } from '../context_builder/builders';
import { ConversationHistory } from '../context_builder/conversation-history';
import { numTokens } from '../llm/text-utils';
import { BaseQuestionGen, QuestionResult } from './base';

const QUESTION_SYSTEM_PROMPT = `
---Role---

You are a helpful assistant responding to questions about data in the tables provided.

---Goal---

Generate a set of {question_count} candidate questions that would be relevant to ask about the provided data.
Questions should be specific and answerable from the given context.

---Data---

{context_data}

---Instructions---

Generate {question_count} questions that would help explore and understand the data provided.
Each question should be on a separate line.
Focus on questions that would reveal insights, patterns, or important details from the data.
`;

/**
 * Search orchestration for global search mode.
 */
export class LocalQuestionGen extends BaseQuestionGen {
  private systemPrompt: string;
  private callbacks: BaseLLMCallback[];

  constructor(
    model: ChatModel,
    contextBuilder: LocalContextBuilder,
    tokenEncoder?: any,
    systemPrompt: string = QUESTION_SYSTEM_PROMPT,
    callbacks?: BaseLLMCallback[],
    modelParams?: Record<string, any>,
    contextBuilderParams?: Record<string, any>
  ) {
    super(
      model,
      contextBuilder,
      tokenEncoder,
      modelParams,
      contextBuilderParams
    );
    this.systemPrompt = systemPrompt;
    this.callbacks = callbacks || [];
  }

  async agenerate(
    questionHistory: string[],
    contextData: string | undefined,
    questionCount: number,
    ...kwargs: any[]
  ): Promise<QuestionResult> {
    /**
     * Generate a question based on the question history and context data.
     * 
     * If context data is not provided, it will be generated by the local context builder
     */
    const startTime = Date.now();

    let questionText = '';
    let conversationHistory: ConversationHistory | undefined;

    if (questionHistory.length === 0) {
      questionText = '';
      conversationHistory = undefined;
    } else {
      // construct current query and conversation history
      questionText = questionHistory[questionHistory.length - 1];
      const history = questionHistory.slice(0, -1).map(query => ({
        role: 'user' as const,
        content: query
      }));
      conversationHistory = ConversationHistory.fromList(history);
    }

    let contextRecords: Record<string, any>;

    if (contextData === undefined) {
      // generate context data based on the question history
      const result = (this.contextBuilder as LocalContextBuilder).buildContext(
        questionText,
        conversationHistory,
        { ...kwargs, ...this.contextBuilderParams }
      ) as ContextBuilderResult;
      contextData = result.contextChunks as string;
      contextRecords = result.contextRecords;
    } else {
      contextRecords = { contextData };
    }

    console.debug(
      `GENERATE QUESTION: ${startTime}. LAST QUESTION: ${questionText}`
    );

    let systemPrompt = '';
    try {
      systemPrompt = this.systemPrompt
        .replace('{context_data}', contextData)
        .replace(/{question_count}/g, questionCount.toString());

      const questionMessages = [
        { role: 'system' as const, content: systemPrompt }
      ];

      let response = '';
      const stream = this.model.achatStream(
        questionText,
        questionMessages,
        this.modelParams
      );

      for await (const chunk of stream) {
        response += chunk;
        for (const callback of this.callbacks) {
          callback.onLlmNewToken(chunk);
        }
      }

      return {
        response: response.split('\n').filter(line => line.trim()),
        contextData: {
          questionContext: questionText,
          ...contextRecords
        },
        completionTime: Date.now() - startTime,
        llmCalls: 1,
        promptTokens: numTokens(systemPrompt, this.tokenEncoder)
      };

    } catch (error) {
      console.error('Exception in generating question', error);
      return {
        response: [],
        contextData: contextRecords,
        completionTime: Date.now() - startTime,
        llmCalls: 1,
        promptTokens: numTokens(systemPrompt, this.tokenEncoder)
      };
    }
  }

  async generate(
    questionHistory: string[],
    contextData: string | undefined,
    questionCount: number,
    ...kwargs: any[]
  ): Promise<QuestionResult> {
    /**
     * Generate a question based on the question history and context data.
     * 
     * If context data is not provided, it will be generated by the local context builder
     */
    const startTime = Date.now();

    let questionText = '';
    let conversationHistory: ConversationHistory | undefined;

    if (questionHistory.length === 0) {
      questionText = '';
      conversationHistory = undefined;
    } else {
      // construct current query and conversation history
      questionText = questionHistory[questionHistory.length - 1];
      const history = questionHistory.slice(0, -1).map(query => ({
        role: 'user' as const,
        content: query
      }));
      conversationHistory = ConversationHistory.fromList(history);
    }

    let contextRecords: Record<string, any>;

    if (contextData === undefined) {
      // generate context data based on the question history
      const result = (this.contextBuilder as LocalContextBuilder).buildContext(
        questionText,
        conversationHistory,
        { ...kwargs, ...this.contextBuilderParams }
      ) as ContextBuilderResult;
      contextData = result.contextChunks as string;
      contextRecords = result.contextRecords;
    } else {
      contextRecords = { contextData };
    }

    console.debug(
      `GENERATE QUESTION: ${startTime}. QUESTION HISTORY: ${questionText}`
    );

    let systemPrompt = '';
    try {
      systemPrompt = this.systemPrompt
        .replace('{context_data}', contextData)
        .replace(/{question_count}/g, questionCount.toString());

      const questionMessages = [
        { role: 'system' as const, content: systemPrompt },
        { role: 'user' as const, content: questionText }
      ];

      let response = '';
      const stream = this.model.achatStream(
        questionText,
        questionMessages,
        this.modelParams
      );

      for await (const chunk of stream) {
        response += chunk;
        for (const callback of this.callbacks) {
          callback.onLlmNewToken(chunk);
        }
      }

      return {
        response: response.split('\n').filter(line => line.trim()),
        contextData: {
          questionContext: questionText,
          ...contextRecords
        },
        completionTime: Date.now() - startTime,
        llmCalls: 1,
        promptTokens: numTokens(systemPrompt, this.tokenEncoder)
      };

    } catch (error) {
      console.error('Exception in generating questions', error);
      return {
        response: [],
        contextData: contextRecords,
        completionTime: Date.now() - startTime,
        llmCalls: 1,
        promptTokens: numTokens(systemPrompt, this.tokenEncoder)
      };
    }
  }
}