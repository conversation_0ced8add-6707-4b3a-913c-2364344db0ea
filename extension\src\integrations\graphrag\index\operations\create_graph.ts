/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing create_graph definition.
 */

import { DataFrame } from '../../data-model/types';
import { Graph } from '../utils/graphs';

/**
 * Create a networkx-like graph from nodes and edges dataframes.
 * @param edges - DataFrame containing edge data
 * @param edgeAttr - List of edge attributes to include
 * @param nodes - Optional DataFrame containing node data
 * @param nodeId - Column name to use as node ID (default: "title")
 * @returns Graph object
 */
export function createGraph(
    edges: DataFrame,
    edgeAttr?: string[],
    nodes?: DataFrame,
    nodeId: string = "title"
): Graph {
    const graph: Graph = {
        nodes: new Map(),
        edges: new Map()
    };

    // Add edges
    edges.data.forEach((edge, index) => {
        const source = String(edge.source || edge.from);
        const target = String(edge.target || edge.to);
        
        // Create edge data object
        const edgeData: any = {};
        if (edgeAttr) {
            edgeAttr.forEach(attr => {
                if (attr in edge) {
                    edgeData[attr] = edge[attr];
                }
            });
        }
        
        const edgeKey = `${source}-${target}`;
        graph.edges.set(edgeKey, {
            source,
            target,
            weight: edge.weight || 1,
            data: edgeData
        });

        // Add nodes if they don't exist
        if (!graph.nodes.has(source)) {
            graph.nodes.set(source, {});
        }
        if (!graph.nodes.has(target)) {
            graph.nodes.set(target, {});
        }
    });

    // Add node data if provided
    if (nodes) {
        nodes.data.forEach(node => {
            const nodeKey = String(node[nodeId]);
            const nodeData = { ...node };
            delete nodeData[nodeId]; // Remove the ID field from data
            
            graph.nodes.set(nodeKey, nodeData);
        });
    }

    return graph;
}