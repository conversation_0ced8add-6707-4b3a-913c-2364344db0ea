// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the cluster graph configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';

/**
 * Configuration section for clustering graphs.
 */
export interface ClusterGraphConfig {
  /**
   * The maximum cluster size to use.
   */
  maxClusterSize: number;

  /**
   * Whether to use the largest connected component.
   */
  useLcc: boolean;

  /**
   * The seed to use for the clustering.
   */
  seed: number;
}

/**
 * Create a ClusterGraphConfig with default values.
 */
export function createClusterGraphConfig(config: Partial<ClusterGraphConfig> = {}): ClusterGraphConfig {
  return {
    maxClusterSize: config.maxClusterSize ?? graphragConfigDefaults.clusterGraph.maxClusterSize,
    useLcc: config.useLcc ?? graphragConfigDefaults.clusterGraph.useLcc,
    seed: config.seed ?? graphragConfigDefaults.clusterGraph.seed,
  };
}
