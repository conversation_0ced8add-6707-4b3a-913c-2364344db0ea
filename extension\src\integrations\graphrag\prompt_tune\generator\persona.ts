// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Persona generating module for fine-tuning GraphRAG prompts.
 */

import { ChatModel } from '../../language_model/protocol/base';
import { DEFAULT_TASK } from '../defaults';
import { GENERATE_PERSONA_PROMPT } from '../prompt/persona';

/**
 * Generate an LLM persona to use for GraphRAG prompts.
 * 
 * @param model - The LLM to use for generation
 * @param domain - The domain to generate a persona for
 * @param task - The task to generate a persona for (defaults to DEFAULT_TASK)
 * @returns The generated persona
 */
export async function generatePersona(
    model: ChatModel, 
    domain: string, 
    task: string = DEFAULT_TASK
): Promise<string> {
    const formattedTask = task.replace('{domain}', domain);
    const personaPrompt = GENERATE_PERSONA_PROMPT.replace('{sample_task}', formattedTask);

    const response = await model.achat(personaPrompt);

    return response.output.content || '';
}

/**
 * Generate persona with validation.
 */
export async function generatePersonaWithValidation(
    model: ChatModel, 
    domain: string, 
    task: string = DEFAULT_TASK,
    minLength: number = 50
): Promise<{ persona: string; isValid: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    // Validate inputs
    if (!domain.trim()) {
        errors.push('Domain cannot be empty');
    }
    
    if (!task.trim()) {
        errors.push('Task cannot be empty');
    }
    
    if (errors.length > 0) {
        return { persona: '', isValid: false, errors };
    }
    
    try {
        const persona = await generatePersona(model, domain, task);
        
        // Validate output
        if (!persona.trim()) {
            errors.push('Generated persona is empty');
        } else if (persona.length < minLength) {
            errors.push(`Generated persona is too short (${persona.length} < ${minLength} characters)`);
        }
        
        return {
            persona,
            isValid: errors.length === 0,
            errors
        };
    } catch (error) {
        errors.push(`Failed to generate persona: ${error instanceof Error ? error.message : String(error)}`);
        return { persona: '', isValid: false, errors };
    }
}
