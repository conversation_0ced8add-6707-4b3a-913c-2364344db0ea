/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing load method definition.
 */

import { DataFrame } from '../../data_model/types.js';
import { InputConfig } from '../../config/models/input_config.js';
import { PipelineStorage } from '../../storage/pipeline_storage.js';
import { loadFiles } from './util';
import { genSha512Hash } from '../utils/hashing';

const logger = console;

/**
 * Load text inputs from a directory.
 * @param config - Input configuration
 * @param storage - Pipeline storage instance
 * @returns Promise resolving to DataFrame with text data
 */
export async function loadText(
    config: InputConfig,
    storage: PipelineStorage,
): Promise<DataFrame> {

    async function loadFile(path: string, group?: Record<string, any>): Promise<DataFrame> {
        const groupData = group || {};

        const text = await storage.get(path, false, config.encoding);
        const newItem: Record<string, any> = {
            ...groupData,
            text: text
        };

        // Generate ID from the item data
        newItem.id = genSha512Hash(newItem, Object.keys(newItem));

        // Extract filename from path
        const pathParts = path.split('/');
        newItem.title = pathParts[pathParts.length - 1];

        newItem.creation_date = await storage.getCreationDate(path);
        
        return {
            columns: Object.keys(newItem),
            data: [newItem]
        };
    }

    return await loadFiles(loadFile, config, storage);
}