// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Entity Extraction prompt generator module.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as defs from '../../config/defaults';
import { numTokensFromString } from '../../index/utils/tokens';
import {
    EXAMPLE_EXTRACTION_TEMPLATE,
    GRAPH_EXTRACTION_JSON_PROMPT,
    GRAPH_EXTRACTION_PROMPT,
    UNTYPED_EXAMPLE_EXTRACTION_TEMPLATE,
    UNTYPED_GRAPH_EXTRACTION_PROMPT,
} from '../template/extract_graph';

export const EXTRACT_GRAPH_FILENAME = 'extract_graph.txt';

/**
 * Parameters for creating extract graph prompt.
 */
export interface CreateExtractGraphPromptParams {
    entityTypes: string | string[] | null;
    docs: string[];
    examples: string[];
    language: string;
    maxTokenCount: number;
    encodingModel?: string;
    jsonMode?: boolean;
    outputPath?: string;
    minExamplesRequired?: number;
}

/**
 * Create a prompt for entity extraction.
 * 
 * @param params - The parameters for creating the prompt
 * @returns The entity extraction prompt
 */
export function createExtractGraphPrompt(params: CreateExtractGraphPromptParams): string {
    const {
        entityTypes,
        docs,
        examples,
        language,
        maxTokenCount,
        encodingModel = defs.ENCODING_MODEL,
        jsonMode = false,
        outputPath,
        minExamplesRequired = 2
    } = params;

    let prompt = entityTypes
        ? (jsonMode ? GRAPH_EXTRACTION_JSON_PROMPT : GRAPH_EXTRACTION_PROMPT)
        : UNTYPED_GRAPH_EXTRACTION_PROMPT;

    const entityTypesStr = Array.isArray(entityTypes) 
        ? entityTypes.join(', ')
        : entityTypes;

    let tokensLeft = maxTokenCount - numTokensFromString(prompt, encodingModel);
    
    if (entityTypesStr) {
        tokensLeft -= numTokensFromString(entityTypesStr, encodingModel);
    }

    let examplesPrompt = '';

    // Iterate over examples, while we have tokens left or examples left
    for (let i = 0; i < examples.length; i++) {
        const output = examples[i];
        const input = docs[i];
        
        const exampleFormatted = entityTypesStr
            ? EXAMPLE_EXTRACTION_TEMPLATE
                .replace('{n}', String(i + 1))
                .replace('{input_text}', input)
                .replace('{entity_types}', entityTypesStr)
                .replace('{output}', output)
            : UNTYPED_EXAMPLE_EXTRACTION_TEMPLATE
                .replace('{n}', String(i + 1))
                .replace('{input_text}', input)
                .replace('{output}', output);

        const exampleTokens = numTokensFromString(exampleFormatted, encodingModel);

        // Ensure at least minimum examples are included
        if (i >= minExamplesRequired && exampleTokens > tokensLeft) {
            break;
        }

        examplesPrompt += exampleFormatted;
        tokensLeft -= exampleTokens;
    }

    prompt = entityTypesStr
        ? prompt
            .replace('{entity_types}', entityTypesStr)
            .replace('{examples}', examplesPrompt)
            .replace('{language}', language)
        : prompt
            .replace('{examples}', examplesPrompt)
            .replace('{language}', language);

    if (outputPath) {
        // Ensure directory exists
        fs.mkdirSync(outputPath, { recursive: true });

        const fullPath = path.join(outputPath, EXTRACT_GRAPH_FILENAME);
        
        // Write file to output path
        fs.writeFileSync(fullPath, prompt, { encoding: 'utf-8' });
    }

    return prompt;
}

/**
 * Create extract graph prompt asynchronously.
 */
export async function createExtractGraphPromptAsync(params: CreateExtractGraphPromptParams): Promise<string> {
    const {
        entityTypes,
        docs,
        examples,
        language,
        maxTokenCount,
        encodingModel = defs.ENCODING_MODEL,
        jsonMode = false,
        outputPath,
        minExamplesRequired = 2
    } = params;

    let prompt = entityTypes
        ? (jsonMode ? GRAPH_EXTRACTION_JSON_PROMPT : GRAPH_EXTRACTION_PROMPT)
        : UNTYPED_GRAPH_EXTRACTION_PROMPT;

    const entityTypesStr = Array.isArray(entityTypes) 
        ? entityTypes.join(', ')
        : entityTypes;

    let tokensLeft = maxTokenCount - numTokensFromString(prompt, encodingModel);
    
    if (entityTypesStr) {
        tokensLeft -= numTokensFromString(entityTypesStr, encodingModel);
    }

    let examplesPrompt = '';

    // Iterate over examples, while we have tokens left or examples left
    for (let i = 0; i < examples.length; i++) {
        const output = examples[i];
        const input = docs[i];
        
        const exampleFormatted = entityTypesStr
            ? EXAMPLE_EXTRACTION_TEMPLATE
                .replace('{n}', String(i + 1))
                .replace('{input_text}', input)
                .replace('{entity_types}', entityTypesStr)
                .replace('{output}', output)
            : UNTYPED_EXAMPLE_EXTRACTION_TEMPLATE
                .replace('{n}', String(i + 1))
                .replace('{input_text}', input)
                .replace('{output}', output);

        const exampleTokens = numTokensFromString(exampleFormatted, encodingModel);

        // Ensure at least minimum examples are included
        if (i >= minExamplesRequired && exampleTokens > tokensLeft) {
            break;
        }

        examplesPrompt += exampleFormatted;
        tokensLeft -= exampleTokens;
    }

    prompt = entityTypesStr
        ? prompt
            .replace('{entity_types}', entityTypesStr)
            .replace('{examples}', examplesPrompt)
            .replace('{language}', language)
        : prompt
            .replace('{examples}', examplesPrompt)
            .replace('{language}', language);

    if (outputPath) {
        // Ensure directory exists
        await fs.promises.mkdir(outputPath, { recursive: true });

        const fullPath = path.join(outputPath, EXTRACT_GRAPH_FILENAME);
        
        // Write file to output path
        await fs.promises.writeFile(fullPath, prompt, { encoding: 'utf-8' });
    }

    return prompt;
}

/**
 * Validate extract graph prompt parameters.
 */
export function validateExtractGraphPromptParams(params: CreateExtractGraphPromptParams): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const { entityTypes, docs, examples, language, maxTokenCount, minExamplesRequired = 2 } = params;

    if (!Array.isArray(docs) || docs.length === 0) {
        errors.push('Docs must be a non-empty array');
    }

    if (!Array.isArray(examples) || examples.length === 0) {
        errors.push('Examples must be a non-empty array');
    }

    if (docs.length !== examples.length) {
        errors.push('Docs and examples arrays must have the same length');
    }

    if (!language || language.trim() === '') {
        errors.push('Language cannot be empty');
    }

    if (maxTokenCount <= 0) {
        errors.push('Max token count must be positive');
    }

    if (minExamplesRequired < 0) {
        errors.push('Min examples required cannot be negative');
    }

    if (examples.length < minExamplesRequired) {
        errors.push(`Not enough examples provided (${examples.length} < ${minExamplesRequired})`);
    }

    if (entityTypes && Array.isArray(entityTypes) && entityTypes.length === 0) {
        errors.push('Entity types array cannot be empty if provided');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}
