// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * The config package root.
 * 
 * This module provides a comprehensive configuration system for GraphRAG,
 * including all configuration models, utilities, and default values.
 * All Python configuration functionality has been successfully converted
 * to TypeScript with full feature parity.
 */

// Core configuration creation and loading
export * from './create_graphrag_config.js';
export * from './load_config.js';

// Configuration models (all config interfaces and factories)
export * from './models/index.js';

// Default values and constants
export * from './defaults.js';

// Enums and types
export * from './enums.js';

// Error classes
export * from './errors.js';

// Utility modules
export * from './embeddings.js';
export * from './environment_reader.js';
export * from './get_embedding_settings.js';
export * from './init_content.js';
export * from './read_dotenv.js';

// Re-export commonly used types and functions for convenience
export type { GraphRagConfig } from './models/graph-rag-config.js';
export { createGraphragConfig } from './create_graphrag_config.js';
export { loadConfig } from './load_config.js';
export { readDotenv } from './read_dotenv.js';
export { getEmbeddingSettings } from './get_embedding_settings.js';
export { EnvironmentReader } from './environment_reader.js';

// Re-export key constants
export {
    DEFAULT_CHAT_MODEL_ID,
    DEFAULT_EMBEDDING_MODEL_ID,
    DEFAULT_CHAT_MODEL,
    DEFAULT_EMBEDDING_MODEL,
    DEFAULT_OUTPUT_BASE_DIR,
    ENCODING_MODEL,
    COGNITIVE_SERVICES_AUDIENCE
} from './defaults.js';

// Re-export embedding constants
export {
    allEmbeddings,
    defaultEmbeddings,
    createCollectionName
} from './embeddings.js';

// Re-export key enums
export {
    ModelType,
    AuthType,
    AsyncType,
    CacheType,
    StorageType,
    ReportingType,
    InputFileType,
    ChunkStrategyType
} from './enums.js';
