/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing run_workflow method definition.
 */

import { v4 as uuidv4 } from 'uuid';
import { DataFrame } from '../../data-model/types';
import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { COMMUNITIES_FINAL_COLUMNS } from '../../data-model/schemas';
import { clusterGraph } from '../operations/cluster-graph';
import { createGraph } from '../operations/create-graph';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

/**
 * All the steps to transform final communities.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: create_communities");
    
    const entities = await loadTableFromStorage("entities", context.outputStorage);
    const relationships = await loadTableFromStorage("relationships", context.outputStorage);

    const maxClusterSize = config.clusterGraph.maxClusterSize;
    const useLcc = config.clusterGraph.useLcc;
    const seed = config.clusterGraph.seed;

    const output = createCommunities(
        entities,
        relationships,
        maxClusterSize,
        useLcc,
        seed
    );

    await writeTableToStorage(output, "communities", context.outputStorage);

    logger.info("Workflow completed: create_communities");
    return {
        result: output,
        stop: false
    };
}

/**
 * All the steps to transform final communities.
 */
export function createCommunities(
    entities: DataFrame,
    relationships: DataFrame,
    maxClusterSize: number,
    useLcc: boolean,
    seed?: number
): DataFrame {
    const graph = createGraph(relationships, ['weight']);

    const clusters = clusterGraph(
        graph,
        maxClusterSize,
        useLcc,
        seed
    );

    // Convert clusters to DataFrame format
    const communitiesData: any[] = [];
    clusters.forEach(([level, community, parent, nodes]) => {
        nodes.forEach(title => {
            communitiesData.push({
                level,
                community: parseInt(String(community), 10),
                parent,
                title
            });
        });
    });

    const communities: DataFrame = {
        columns: ['level', 'community', 'parent', 'title'],
        data: communitiesData
    };

    // Aggregate entity ids for each community
    const entityIds = new Map<number, string[]>();
    communities.data.forEach(row => {
        const entity = entities.data.find(e => e.title === row.title);
        if (entity) {
            if (!entityIds.has(row.community)) {
                entityIds.set(row.community, []);
            }
            entityIds.get(row.community)!.push(entity.id);
        }
    });

    // Process relationships and text units for each community
    const maxLevel = Math.max(...communities.data.map(row => row.level));
    const allGrouped: any[] = [];

    for (let level = 0; level <= maxLevel; level++) {
        const communitiesAtLevel = communities.data.filter(row => row.level === level);
        
        // Simplified relationship processing
        const relationshipIds = new Map<number, string[]>();
        const textUnitIds = new Map<number, string[]>();
        
        communitiesAtLevel.forEach(row => {
            if (!relationshipIds.has(row.community)) {
                relationshipIds.set(row.community, []);
                textUnitIds.set(row.community, []);
            }
        });

        communitiesAtLevel.forEach(row => {
            allGrouped.push({
                community: row.community,
                level: row.level,
                parent: row.parent,
                relationship_ids: relationshipIds.get(row.community) || [],
                text_unit_ids: textUnitIds.get(row.community) || []
            });
        });
    }

    // Create final communities
    const finalCommunitiesData = allGrouped.map(row => {
        const entityIdList = entityIds.get(row.community) || [];
        
        return {
            id: uuidv4(),
            community: row.community,
            level: row.level,
            parent: parseInt(String(row.parent), 10),
            title: `Community ${row.community}`,
            human_readable_id: row.community,
            entity_ids: entityIdList,
            relationship_ids: Array.from(new Set(row.relationship_ids)),
            text_unit_ids: Array.from(new Set(row.text_unit_ids)),
            children: [], // Will be populated below
            period: new Date().toISOString().split('T')[0],
            size: entityIdList.length
        };
    });

    // Add children relationships
    const parentChildMap = new Map<number, number[]>();
    finalCommunitiesData.forEach(row => {
        if (row.parent !== -1) {
            if (!parentChildMap.has(row.parent)) {
                parentChildMap.set(row.parent, []);
            }
            parentChildMap.get(row.parent)!.push(row.community);
        }
    });

    finalCommunitiesData.forEach(row => {
        row.children = parentChildMap.get(row.community) || [];
    });

    // Filter to final columns
    const filteredData = finalCommunitiesData.map(row => {
        const newRow: Record<string, any> = {};
        COMMUNITIES_FINAL_COLUMNS.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });

    return {
        columns: COMMUNITIES_FINAL_COLUMNS,
        data: filteredData
    };
}