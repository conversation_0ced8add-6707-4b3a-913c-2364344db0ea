// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing 'TextEmbeddingResult' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineCache } from '../../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../../callbacks/workflow_callbacks.js';

/**
 * Text embedding result class definition.
 * Matches the Python dataclass structure exactly.
 */
export interface TextEmbeddingResult {
  embeddings: (number[] | null)[] | null;
}

/**
 * Text embedding strategy function type.
 * Matches the Python Callable type signature exactly.
 */
export type TextEmbeddingStrategy = (
  input: string[],
  callbacks: WorkflowCallbacks,
  cache: PipelineCache,
  args: Record<string, any>
) => Promise<TextEmbeddingResult>;
