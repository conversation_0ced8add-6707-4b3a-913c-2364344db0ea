/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A package containing the 'TextUnit' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Identified } from './identified'

/**
 * An interface for a TextUnit item in a Document database.
 */
export interface TextUnit extends Identified {
    /** The text of the unit. */
    text: string

    /** List of entity IDs related to the text unit (optional). */
    entity_ids?: string[]

    /** List of relationship IDs related to the text unit (optional). */
    relationship_ids?: string[]

    /** Dictionary of different types of covariates related to the text unit (optional). */
    covariate_ids?: Record<string, string[]>

    /** The number of tokens in the text (optional). */
    n_tokens?: number

    /** List of document IDs in which the text unit appears (optional). */
    document_ids?: string[]

    /** A dictionary of additional attributes associated with the text unit (optional). */
    attributes?: Record<string, any>
}

/**
 * Create a new text unit from the dict data.
 */
export function createTextUnitFromDict(
    d: Record<string, any>,
    options: {
        id_key?: string
        short_id_key?: string
        text_key?: string
        entities_key?: string
        relationships_key?: string
        covariates_key?: string
        n_tokens_key?: string
        document_ids_key?: string
        attributes_key?: string
    } = {}
): TextUnit {
    const {
        id_key = "id",
        short_id_key = "human_readable_id",
        text_key = "text",
        entities_key = "entity_ids",
        relationships_key = "relationship_ids",
        covariates_key = "covariate_ids",
        n_tokens_key = "n_tokens",
        document_ids_key = "document_ids",
        attributes_key = "attributes"
    } = options

    return {
        id: d[id_key],
        short_id: d[short_id_key],
        text: d[text_key],
        entity_ids: d[entities_key],
        relationship_ids: d[relationships_key],
        covariate_ids: d[covariates_key],
        n_tokens: d[n_tokens_key],
        document_ids: d[document_ids_key],
        attributes: d[attributes_key]
    }
}