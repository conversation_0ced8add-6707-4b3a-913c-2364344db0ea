/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Orchestration Context Builders.
 */

import { Entity } from '../../data_model/entity';
import { Relationship } from '../../data_model/relationship';
import { EmbeddingModel } from '../../language_model/protocol/base';
import {
    getEntityById,
    getEntityByKey,
    getEntityByName,
} from '../input/retrieval/entities';
import { BaseVectorStore } from '../../vector_stores/base';

export enum EntityVectorStoreKey {
    ID = "id",
    TITLE = "title",
}

export function entityVectorStoreKeyFromString(value: string): EntityVectorStoreKey {
    switch (value) {
        case "id":
            return EntityVectorStoreKey.ID;
        case "title":
            return EntityVectorStoreKey.TITLE;
        default:
            throw new Error(`Invalid EntityVectorStoreKey: ${value}`);
    }
}

export function mapQueryToEntities(options: {
    query: string;
    textEmbeddingVectorstore: BaseVectorStore;
    textEmbedder: EmbeddingModel;
    allEntitiesDict: Record<string, Entity>;
    embeddingVectorstoreKey?: string;
    includeEntityNames?: string[];
    excludeEntityNames?: string[];
    k?: number;
    oversampleScaler?: number;
}): Entity[] {
    const {
        query,
        textEmbeddingVectorstore,
        textEmbedder,
        allEntitiesDict,
        embeddingVectorstoreKey = EntityVectorStoreKey.ID,
        includeEntityNames = [],
        excludeEntityNames = [],
        k = 10,
        oversampleScaler = 2
    } = options;

    const allEntities = Object.values(allEntitiesDict);
    let matchedEntities: Entity[] = [];

    if (query !== "") {
        // Get entities with highest semantic similarity to query
        // Oversample to account for excluded entities
        const searchResults = textEmbeddingVectorstore.similaritySearchByText({
            text: query,
            textEmbedder: (t: string) => textEmbedder.embed(t),
            k: k * oversampleScaler,
        });

        for (const result of searchResults) {
            let matched: Entity | undefined;
            
            if (embeddingVectorstoreKey === EntityVectorStoreKey.ID && 
                typeof result.document.id === 'string') {
                matched = getEntityById(allEntitiesDict, result.document.id);
            } else {
                matched = getEntityByKey({
                    entities: allEntities,
                    key: embeddingVectorstoreKey,
                    value: result.document.id,
                });
            }
            
            if (matched) {
                matchedEntities.push(matched);
            }
        }
    } else {
        allEntities.sort((a, b) => (b.rank || 0) - (a.rank || 0));
        matchedEntities = allEntities.slice(0, k);
    }

    // Filter out excluded entities
    if (excludeEntityNames.length > 0) {
        matchedEntities = matchedEntities.filter(
            entity => !excludeEntityNames.includes(entity.title)
        );
    }

    // Add entities in the include_entity list
    const includedEntities: Entity[] = [];
    for (const entityName of includeEntityNames) {
        includedEntities.push(...getEntityByName(allEntities, entityName));
    }

    return [...includedEntities, ...matchedEntities];
}

export function findNearestNeighborsByEntityRank(options: {
    entityName: string;
    allEntities: Entity[];
    allRelationships: Relationship[];
    excludeEntityNames?: string[];
    k?: number;
}): Entity[] {
    const {
        entityName,
        allEntities,
        allRelationships,
        excludeEntityNames = [],
        k = 10
    } = options;

    const entityRelationships = allRelationships.filter(
        rel => rel.source === entityName || rel.target === entityName
    );

    const sourceEntityNames = new Set(entityRelationships.map(rel => rel.source));
    const targetEntityNames = new Set(entityRelationships.map(rel => rel.target));
    const relatedEntityNames = new Set([
        ...sourceEntityNames,
        ...targetEntityNames
    ]);

    // Remove excluded entities
    for (const excludeName of excludeEntityNames) {
        relatedEntityNames.delete(excludeName);
    }

    const topRelations = allEntities.filter(
        entity => relatedEntityNames.has(entity.title)
    );

    topRelations.sort((a, b) => (b.rank || 0) - (a.rank || 0));

    return k ? topRelations.slice(0, k) : topRelations;
}