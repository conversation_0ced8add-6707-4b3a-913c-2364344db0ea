/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'TextChunk' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { ChunkingConfig } from '../../../config/models/chunking_config.js';

/**
 * Text chunk class definition.
 * Matches the Python dataclass structure exactly.
 */
export interface TextChunk {
    /** The text chunk content */
    text_chunk: string;
    /** Source document indices */
    source_doc_indices: number[];
    /** Number of tokens (optional) */
    n_tokens?: number | null;
}

/**
 * Input to a chunking strategy. Can be a string, a list of strings, or a list of tuples of (id, text).
 */
export type ChunkInput = string | string[] | Array<[string, string]>;

/**
 * Chunk strategy function type
 */
export type ChunkStrategy = (
    input: string[],
    config: ChunkingConfig,
    tick: (increment: number) => void
) => Iterable<TextChunk>;