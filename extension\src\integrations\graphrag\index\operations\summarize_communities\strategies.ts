/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Graph intelligence strategy for community summarization.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../callbacks/workflow_callbacks.js';
import { CommunityReport, StrategyConfig } from './typing.js';
import { CommunityReportsExtractor } from './community_reports_extractor.js';
import { ChatModel } from '../../../query/llm/base.js';

/**
 * Run graph intelligence strategy for community summarization.
 * Matches the Python run_graph_intelligence function exactly.
 */
export async function run_graph_intelligence(
    community_id: string,
    community_context: string,
    community_level: number,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    config: StrategyConfig
): Promise<CommunityReport | null> {
    // Python: llm = config.get("llm")
    const llm: ChatModel | undefined = config.llm;

    // Python: extraction_prompt = config.get("extraction_prompt", "")
    const extraction_prompt: string = config.extraction_prompt || "";

    // Python: max_report_length = config.get("max_report_length", 1500)
    const max_report_length: number = config.max_report_length || 1500;

    if (!llm) {
        console.warn('No LLM provided for graph intelligence strategy');
        return null;
    }

    if (!community_context || community_context.trim().length === 0) {
        return null;
    }

    // Python: extractor = CommunityReportsExtractor(
    //     llm=llm,
    //     extraction_prompt=extraction_prompt,
    //     max_report_length=max_report_length,
    //     on_error=lambda e, stack, details: callbacks.error("Community Report Extraction Error", e),
    // )
    const extractor = new CommunityReportsExtractor(
        llm,
        extraction_prompt,
        (error, stack, details) => {
            callbacks.error("Community Report Extraction Error", error);
        },
        max_report_length
    );

    // Python: return await extractor(community_id, community_context, community_level)
    return await extractor.call(community_id, community_context, community_level);
}

// Compatibility export for existing code
export const runGraphIntelligence = run_graph_intelligence;
}