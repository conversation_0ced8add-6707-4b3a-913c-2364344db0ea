// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * API for GraphRAG.
 *
 * WARNING: This API is under development and may undergo changes in future releases.
 * Backwards compatibility is not guaranteed at this time.
 */

// Index API
export { buildIndex, registerWorkflowFunction } from './index-impl';

// Prompt Tuning API
export { generateIndexingPrompts } from './prompt_tune';

// Query API
export {
    globalSearch,
    globalSearchStreaming,
    localSearch,
    localSearchStreaming,
    driftSearch,
    driftSearchStreaming,
    basicSearch,
    basicSearchStreaming,
    multiIndexBasicSearch,
    multiIndexDriftSearch,
    multiIndexGlobalSearch,
    multiIndexLocalSearch,
} from './query';

// Types
export { DocSelectionType } from '../prompt_tune/types';