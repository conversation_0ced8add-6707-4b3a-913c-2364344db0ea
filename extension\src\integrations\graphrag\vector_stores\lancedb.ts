// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * The LanceDB vector storage implementation package.
 */

import { TextEmbedder } from '../data_model/types';
import {
    BaseVectorStore,
    VectorStoreDocument,
    VectorStoreSearchResult,
} from './base';

// Note: LanceDB TypeScript client would need to be installed
// For now, we'll create a placeholder interface
interface LanceDBConnection {
    tableNames(): string[];
    openTable(name: string): LanceDBTable;
    createTable(name: string, options: any): LanceDBTable;
}

interface LanceDBTable {
    add(data: any[]): void;
    search(options: any): LanceDBSearchBuilder;
}

interface LanceDBSearchBuilder {
    where(filter: string, options?: any): LanceDBSearchBuilder;
    limit(k: number): LanceDBSearchBuilder;
    toList(): any[];
}

// Placeholder for LanceDB connection function
declare function connectLanceDB(uri: string): LanceDBConnection;

/**
 * LanceDB vector storage implementation.
 */
export class LanceDBVectorStore extends BaseVectorStore {
    constructor(kwargs: Record<string, any> = {}) {
        super(
            kwargs.collection_name || '',
            kwargs.db_connection,
            kwargs.document_collection,
            kwargs.query_filter,
            kwargs
        );
    }

    /**
     * Connect to the vector storage.
     */
    connect(kwargs: Record<string, any> = {}): void {
        // Note: This would require the actual LanceDB TypeScript client
        // this.db_connection = connectLanceDB(kwargs.db_uri);
        
        // For now, we'll create a mock implementation
        this.db_connection = {
            tableNames: () => [],
            openTable: (name: string) => ({
                add: (data: any[]) => {},
                search: (options: any) => ({
                    where: (filter: string, options?: any) => ({
                        limit: (k: number) => ({
                            toList: () => []
                        })
                    }),
                    limit: (k: number) => ({
                        toList: () => []
                    })
                })
            }),
            createTable: (name: string, options: any) => ({
                add: (data: any[]) => {},
                search: (options: any) => ({
                    where: (filter: string, options?: any) => ({
                        limit: (k: number) => ({
                            toList: () => []
                        })
                    }),
                    limit: (k: number) => ({
                        toList: () => []
                    })
                })
            })
        };

        if (
            this.collection_name &&
            this.db_connection.tableNames().includes(this.collection_name)
        ) {
            this.document_collection = this.db_connection.openTable(this.collection_name);
        }
    }

    /**
     * Load documents into vector storage.
     */
    loadDocuments(documents: VectorStoreDocument[], overwrite: boolean = true): void {
        const data = documents
            .filter(document => document.vector !== null && document.vector !== undefined)
            .map(document => ({
                id: document.id,
                text: document.text,
                vector: document.vector,
                attributes: JSON.stringify(document.attributes),
            }));

        if (data.length === 0) {
            return;
        }

        const schema = {
            id: 'string',
            text: 'string',
            vector: 'list<float64>',
            attributes: 'string',
        };

        // NOTE: If modifying the next section of code, ensure that the schema remains the same.
        //       The vector field format may change if the order of operations is changed
        //       and will break vector search.
        if (overwrite) {
            if (data.length > 0) {
                this.document_collection = this.db_connection.createTable(
                    this.collection_name,
                    { data, mode: "overwrite" }
                );
            } else {
                this.document_collection = this.db_connection.createTable(
                    this.collection_name,
                    { schema, mode: "overwrite" }
                );
            }
        } else {
            // add data to existing table
            this.document_collection = this.db_connection.openTable(this.collection_name);
            if (data.length > 0) {
                this.document_collection.add(data);
            }
        }
    }

    /**
     * Build a query filter to filter documents by id.
     */
    filterById(includeIds: (string | number)[]): any {
        if (includeIds.length === 0) {
            this.query_filter = null;
        } else {
            if (typeof includeIds[0] === 'string') {
                const idFilter = includeIds.map(id => `'${id}'`).join(', ');
                this.query_filter = `id in (${idFilter})`;
            } else {
                this.query_filter = `id in (${includeIds.join(', ')})`;
            }
        }
        return this.query_filter;
    }

    /**
     * Perform a vector-based similarity search.
     */
    similaritySearchByVector(
        queryEmbedding: number[],
        k: number = 10,
        kwargs: Record<string, any> = {}
    ): VectorStoreSearchResult[] {
        let docs: any[];

        if (this.query_filter) {
            docs = this.document_collection
                .search({ query: queryEmbedding, vector_column_name: "vector" })
                .where(this.query_filter, { prefilter: true })
                .limit(k)
                .toList();
        } else {
            docs = this.document_collection
                .search({ query: queryEmbedding, vector_column_name: "vector" })
                .limit(k)
                .toList();
        }

        return docs.map(doc => ({
            document: {
                id: doc.id,
                text: doc.text,
                vector: doc.vector,
                attributes: JSON.parse(doc.attributes),
            },
            score: 1 - Math.abs(parseFloat(doc._distance)),
        }));
    }

    /**
     * Perform a similarity search using a given input text.
     */
    similaritySearchByText(
        text: string,
        textEmbedder: TextEmbedder,
        k: number = 10,
        kwargs: Record<string, any> = {}
    ): VectorStoreSearchResult[] {
        const queryEmbedding = textEmbedder(text);
        if (queryEmbedding) {
            return this.similaritySearchByVector(queryEmbedding, k);
        }
        return [];
    }

    /**
     * Search for a document by id.
     */
    searchById(id: string): VectorStoreDocument {
        const docs = this.document_collection
            .search({})
            .where(`id == '${id}'`, { prefilter: true })
            .toList();

        if (docs.length > 0) {
            const doc = docs[0];
            return {
                id: doc.id,
                text: doc.text,
                vector: doc.vector,
                attributes: JSON.parse(doc.attributes),
            };
        }

        return {
            id,
            text: null,
            vector: null,
            attributes: {},
        };
    }
}