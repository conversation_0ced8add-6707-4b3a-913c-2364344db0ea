// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Generate a rating description for community report rating.
 */

import { ChatModel } from '../../language_model/protocol/base';
import { GENERATE_REPORT_RATING_PROMPT } from '../prompt/community_report_rating';

/**
 * Generate an LLM persona to use for GraphRAG prompts.
 * 
 * @param model - The LLM to use for generation
 * @param domain - The domain to generate a rating for
 * @param persona - The persona to generate a rating for
 * @param docs - Documents used to contextualize the rating
 * @returns The generated rating description prompt response
 */
export async function generateCommunityReportRating(
    model: ChatModel,
    domain: string,
    persona: string,
    docs: string | string[]
): Promise<string> {
    const docsStr = Array.isArray(docs) ? docs.join(' ') : docs;
    const domainPrompt = GENERATE_REPORT_RATING_PROMPT
        .replace('{domain}', domain)
        .replace('{persona}', persona)
        .replace('{input_text}', docsStr);

    const response = await model.achat(domainPrompt);

    return (response.output.content || '').trim();
}

/**
 * Generate community report rating with validation.
 */
export async function generateCommunityReportRatingSafe(
    model: ChatModel,
    domain: string,
    persona: string,
    docs: string | string[]
): Promise<{ rating?: string; success: boolean; errors: string[] }> {
    const errors: string[] = [];

    // Validate inputs
    if (!domain.trim()) {
        errors.push('Domain cannot be empty');
    }

    if (!persona.trim()) {
        errors.push('Persona cannot be empty');
    }

    const docsArray = Array.isArray(docs) ? docs : [docs];
    if (docsArray.length === 0 || docsArray.every(doc => !doc.trim())) {
        errors.push('Documents cannot be empty');
    }

    if (errors.length > 0) {
        return { success: false, errors };
    }

    try {
        const rating = await generateCommunityReportRating(model, domain, persona, docs);
        
        if (!rating.trim()) {
            errors.push('Generated rating is empty');
            return { success: false, errors };
        }

        return {
            rating,
            success: true,
            errors: []
        };
    } catch (error) {
        errors.push(`Failed to generate rating: ${error instanceof Error ? error.message : String(error)}`);
        return { success: false, errors };
    }
}

/**
 * Validate community report rating parameters.
 */
export function validateCommunityReportRatingParams(
    domain: string,
    persona: string,
    docs: string | string[]
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!domain || domain.trim() === '') {
        errors.push('Domain cannot be empty');
    }

    if (!persona || persona.trim() === '') {
        errors.push('Persona cannot be empty');
    }

    const docsArray = Array.isArray(docs) ? docs : [docs];
    if (docsArray.length === 0) {
        errors.push('Documents array cannot be empty');
    } else if (docsArray.every(doc => !doc || doc.trim() === '')) {
        errors.push('All documents cannot be empty');
    }

    if (domain.length > 1000) {
        errors.push('Domain is too long (max 1000 characters)');
    }

    if (persona.length > 2000) {
        errors.push('Persona is too long (max 2000 characters)');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}
