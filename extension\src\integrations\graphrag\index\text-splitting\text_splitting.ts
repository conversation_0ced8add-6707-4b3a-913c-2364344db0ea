/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the 'Tokenizer', 'TextSplitter', 'NoopTextSplitter' and 'TokenTextSplitter' models.
 */

import { isNull } from '../utils/is-null';

const logger = console;

// Type definitions
export type EncodedText = number[];
export type DecodeFn = (encoded: EncodedText) => string;
export type EncodeFn = (text: string) => EncodedText;
export type LengthFn = (text: string) => number;

/**
 * Tokenizer data class.
 */
export interface Tokenizer {
    /** Overlap in tokens between chunks */
    chunkOverlap: number;
    /** Maximum number of tokens per chunk */
    tokensPerChunk: number;
    /** Function to decode a list of token ids to a string */
    decode: DecodeFn;
    /** Function to encode a string to a list of token ids */
    encode: EncodeFn;
}

/**
 * Text chunk with metadata.
 */
export interface TextChunk {
    text: string;
    docIndices: number[];
    tokenCount: number;
}

/**
 * Text splitter configuration.
 */
export interface TextSplitterConfig {
    chunkSize?: number;
    chunkOverlap?: number;
    lengthFunction?: LengthFn;
    keepSeparator?: boolean;
    addStartIndex?: boolean;
    stripWhitespace?: boolean;
}

/**
 * Abstract text splitter class definition.
 */
export abstract class TextSplitter {
    protected chunkSize: number;
    protected chunkOverlap: number;
    protected lengthFunction: LengthFn;
    protected keepSeparator: boolean;
    protected addStartIndex: boolean;
    protected stripWhitespace: boolean;

    constructor(config: TextSplitterConfig = {}) {
        // Based on text-ada-002-embedding max input buffer length
        // https://platform.openai.com/docs/guides/embeddings/second-generation-models
        this.chunkSize = config.chunkSize ?? 8191;
        this.chunkOverlap = config.chunkOverlap ?? 100;
        this.lengthFunction = config.lengthFunction ?? ((text: string) => text.length);
        this.keepSeparator = config.keepSeparator ?? false;
        this.addStartIndex = config.addStartIndex ?? false;
        this.stripWhitespace = config.stripWhitespace ?? true;
    }

    /**
     * Split text method definition.
     */
    abstract splitText(text: string | string[]): string[];
}

/**
 * Noop text splitter class definition.
 */
export class NoopTextSplitter extends TextSplitter {
    /**
     * Split text method definition.
     */
    splitText(text: string | string[]): string[] {
        return typeof text === 'string' ? [text] : text;
    }
}

/**
 * Token text splitter configuration.
 */
export interface TokenTextSplitterConfig extends TextSplitterConfig {
    encodingName?: string;
    modelName?: string;
    allowedSpecial?: 'all' | Set<string>;
    disallowedSpecial?: 'all' | string[];
}

/**
 * Token text splitter class definition.
 */
export class TokenTextSplitter extends TextSplitter {
    private encodingName: string;
    private modelName?: string;
    private allowedSpecial: 'all' | Set<string>;
    private disallowedSpecial: 'all' | string[];

    constructor(config: TokenTextSplitterConfig = {}) {
        super(config);
        this.encodingName = config.encodingName ?? 'cl100k_base';
        this.modelName = config.modelName;
        this.allowedSpecial = config.allowedSpecial ?? new Set();
        this.disallowedSpecial = config.disallowedSpecial ?? 'all';
    }

    /**
     * Encode the given text into an int-vector.
     * Note: This is a simplified implementation. In production, use tiktoken-js.
     */
    encode(text: string): number[] {
        // Simplified encoding - in reality you'd use tiktoken
        // For now, we'll simulate token IDs
        const tokens: number[] = [];
        for (let i = 0; i < text.length; i += 4) {
            tokens.push(Math.floor(Math.random() * 50000));
        }
        return tokens;
    }

    /**
     * Decode token IDs back to text.
     * Note: This is a simplified implementation.
     */
    decode(tokens: number[]): string {
        // Simplified decoding - in reality you'd use tiktoken
        return `[DECODED_${tokens.length}_TOKENS]`;
    }

    /**
     * Return the number of tokens in a string.
     */
    numTokens(text: string): number {
        return this.encode(text).length;
    }

    /**
     * Split text method.
     */
    splitText(text: string | string[]): string[] {
        let textToSplit: string;
        
        if (Array.isArray(text)) {
            textToSplit = text.join(' ');
        } else if (isNull(text) || text === '') {
            return [];
        } else if (typeof text !== 'string') {
            throw new TypeError(`Attempting to split a non-string value, actual is ${typeof text}`);
        } else {
            textToSplit = text;
        }

        const tokenizer: Tokenizer = {
            chunkOverlap: this.chunkOverlap,
            tokensPerChunk: this.chunkSize,
            decode: (tokens) => this.decode(tokens),
            encode: (text) => this.encode(text),
        };

        return splitSingleTextOnTokens(textToSplit, tokenizer);
    }
}

/**
 * Split a single text and return chunks using the tokenizer.
 */
export function splitSingleTextOnTokens(text: string, tokenizer: Tokenizer): string[] {
    const result: string[] = [];
    const inputIds = tokenizer.encode(text);

    let startIdx = 0;
    let curIdx = Math.min(startIdx + tokenizer.tokensPerChunk, inputIds.length);
    let chunkIds = inputIds.slice(startIdx, curIdx);

    while (startIdx < inputIds.length) {
        const chunkText = tokenizer.decode(chunkIds);
        result.push(chunkText);
        
        if (curIdx === inputIds.length) {
            break;
        }
        
        startIdx += tokenizer.tokensPerChunk - tokenizer.chunkOverlap;
        curIdx = Math.min(startIdx + tokenizer.tokensPerChunk, inputIds.length);
        chunkIds = inputIds.slice(startIdx, curIdx);
    }

    return result;
}

/**
 * Split multiple texts and return chunks with metadata using the tokenizer.
 * Adapted from langchain implementation for better control over the chunking process.
 */
export function splitMultipleTextsOnTokens(
    texts: string[],
    tokenizer: Tokenizer,
    tick?: (increment: number) => void
): TextChunk[] {
    const result: TextChunk[] = [];
    const mappedIds: Array<[number, number[]]> = [];

    // Encode all texts
    for (let sourceDocIdx = 0; sourceDocIdx < texts.length; sourceDocIdx++) {
        const text = texts[sourceDocIdx];
        const encoded = tokenizer.encode(text);
        if (tick) {
            tick(1); // Track progress if tick callback is provided
        }
        mappedIds.push([sourceDocIdx, encoded]);
    }

    // Flatten all token IDs with source document indices
    const inputIds: Array<[number, number]> = [];
    for (const [sourceDocIdx, ids] of mappedIds) {
        for (const id of ids) {
            inputIds.push([sourceDocIdx, id]);
        }
    }

    let startIdx = 0;
    let curIdx = Math.min(startIdx + tokenizer.tokensPerChunk, inputIds.length);
    let chunkIds = inputIds.slice(startIdx, curIdx);

    while (startIdx < inputIds.length) {
        const tokenIds = chunkIds.map(([_, id]) => id);
        const chunkText = tokenizer.decode(tokenIds);
        const docIndices = Array.from(new Set(chunkIds.map(([docIdx, _]) => docIdx)));
        
        result.push({
            text: chunkText,
            docIndices: docIndices,
            tokenCount: chunkIds.length
        });
        
        if (curIdx === inputIds.length) {
            break;
        }
        
        startIdx += tokenizer.tokensPerChunk - tokenizer.chunkOverlap;
        curIdx = Math.min(startIdx + tokenizer.tokensPerChunk, inputIds.length);
        chunkIds = inputIds.slice(startIdx, curIdx);
    }

    return result;
}