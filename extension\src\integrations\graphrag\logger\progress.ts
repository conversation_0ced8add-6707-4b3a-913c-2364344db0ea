// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Progress Logging Utilities.
 */

/**
 * A class representing the progress of a task.
 */
export interface Progress {
    /** 0 - 1 progress */
    percent?: number | null;
    /** Description of the progress */
    description?: string | null;
    /** Total number of items */
    totalItems?: number | null;
    /** Number of items completed */
    completedItems?: number | null;
}

/**
 * A function to handle progress reports.
 */
export type ProgressHandler = (progress: Progress) => void;

/**
 * A class that emits progress reports incrementally.
 */
export class ProgressTicker {
    private callback: ProgressHandler | null;
    private description: string;
    private numTotal: number;
    private numComplete: number;

    constructor(
        callback: ProgressHandler | null,
        numTotal: number,
        description: string = ""
    ) {
        this.callback = callback;
        this.description = description;
        this.numTotal = numTotal;
        this.numComplete = 0;
    }

    /**
     * Emit progress.
     */
    tick(numTicks: number = 1): void {
        this.numComplete += numTicks;
        if (this.callback !== null) {
            const p: Progress = {
                totalItems: this.numTotal,
                completedItems: this.numComplete,
                description: this.description,
            };
            if (p.description) {
                console.log(`${p.description}${p.completedItems}/${p.totalItems}`);
            }
            this.callback(p);
        }
    }

    /**
     * Mark the progress as done.
     */
    done(): void {
        if (this.callback !== null) {
            this.callback({
                totalItems: this.numTotal,
                completedItems: this.numTotal,
                description: this.description,
            });
        }
    }
}

/**
 * Create a progress ticker.
 */
export function progressTicker(
    callback: ProgressHandler | null,
    numTotal: number,
    description: string = ""
): ProgressTicker {
    return new ProgressTicker(callback, numTotal, description);
}

/**
 * Wrap an iterable with a progress handler. Every time an item is yielded, the progress handler will be called with the current progress.
 */
export function* progressIterable<T>(
    iterable: Iterable<T>,
    progress: ProgressHandler | null,
    numTotal?: number | null,
    description: string = ""
): Generator<T, void, unknown> {
    let actualNumTotal = numTotal;
    if (actualNumTotal === null || actualNumTotal === undefined) {
        // Convert iterable to array to get length
        const items = Array.from(iterable);
        actualNumTotal = items.length;
        
        const tick = new ProgressTicker(progress, actualNumTotal, description);
        
        for (const item of items) {
            tick.tick(1);
            yield item;
        }
    } else {
        const tick = new ProgressTicker(progress, actualNumTotal, description);
        
        for (const item of iterable) {
            tick.tick(1);
            yield item;
        }
    }
}