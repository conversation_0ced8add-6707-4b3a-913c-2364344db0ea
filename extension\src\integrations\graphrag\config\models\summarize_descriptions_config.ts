// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the summarize descriptions configuration.
 */

import { readFileSync } from 'fs';
import { join } from 'path';
import { graphragConfigDefaults } from '../defaults.js';
import { LanguageModelConfig } from './language-model-config.js';
import { SummarizeStrategyType } from '../../index/operations/summarize-descriptions/types.js';

/**
 * Configuration section for description summarization.
 */
export interface SummarizeDescriptionsConfig {
  /**
   * The model ID to use for summarization.
   */
  modelId: string;

  /**
   * The description summarization prompt to use.
   */
  prompt?: string;

  /**
   * The description summarization maximum length.
   */
  maxLength: number;

  /**
   * Maximum tokens to submit from the input entity descriptions.
   */
  maxInputTokens: number;

  /**
   * The override strategy to use.
   */
  strategy?: Record<string, any>;
}

/**
 * Create a SummarizeDescriptionsConfig with default values.
 */
export function createSummarizeDescriptionsConfig(config: Partial<SummarizeDescriptionsConfig> = {}): SummarizeDescriptionsConfig {
  return {
    modelId: config.modelId ?? graphragConfigDefaults.summarizeDescriptions.modelId,
    prompt: config.prompt ?? graphragConfigDefaults.summarizeDescriptions.prompt,
    maxLength: config.maxLength ?? graphragConfigDefaults.summarizeDescriptions.maxLength,
    maxInputTokens: config.maxInputTokens ?? graphragConfigDefaults.summarizeDescriptions.maxInputTokens,
    strategy: config.strategy ?? graphragConfigDefaults.summarizeDescriptions.strategy,
  };
}

/**
 * Get the resolved description summarization strategy.
 */
export function getResolvedSummarizeDescriptionsStrategy(
  config: SummarizeDescriptionsConfig,
  rootDir: string,
  modelConfig: LanguageModelConfig
): Record<string, any> {
  return config.strategy || {
    type: SummarizeStrategyType.GraphIntelligence,
    llm: modelConfig,
    summarizePrompt: config.prompt
      ? readFileSync(join(rootDir, config.prompt), 'utf-8')
      : undefined,
    maxSummaryLength: config.maxLength,
    maxInputTokens: config.maxInputTokens,
  };
}
