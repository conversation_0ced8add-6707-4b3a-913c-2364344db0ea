// Copyright (c) 2025 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Base model events protocol.
 */

/**
 * Protocol for Model event handling.
 */
export interface ModelEventHandler {
    /**
     * Handle a model error.
     * 
     * @param error - The error that occurred.
     * @param traceback - The error traceback.
     * @param arguments - Additional arguments related to the error.
     */
    onError(
        error: Error | null,
        traceback?: string | null,
        arguments?: Record<string, any> | null
    ): Promise<void>;
}