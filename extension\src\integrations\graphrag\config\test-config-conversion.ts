// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Comprehensive test file for GraphRAG config conversion from Python to TypeScript.
 * This file validates that all converted TypeScript code maintains functional parity
 * with the original Python implementation.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

// Import all config modules
import {
    createGraphragConfig,
    loadConfig,
    readDotenv,
    getEmbeddingSettings,
    EnvironmentReader,
    GraphRagConfig,
    DEFAULT_CHAT_MODEL_ID,
    DEFAULT_EMBEDDING_MODEL_ID,
    allEmbeddings,
    defaultEmbeddings,
    createCollectionName,
    INIT_YAML
} from './index.js';

import { LanguageModelConfig } from './models/language_model_config.js';

/**
 * Test results interface.
 */
interface TestResult {
    name: string;
    passed: boolean;
    error?: string;
    details?: string;
}

/**
 * Test runner class.
 */
class ConfigConversionTester {
    private results: TestResult[] = [];
    private tempDir: string;

    constructor() {
        this.tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'graphrag-test-'));
    }

    /**
     * Add a test result.
     */
    private addResult(name: string, passed: boolean, error?: string, details?: string): void {
        this.results.push({ name, passed, error, details });
    }

    /**
     * Test basic GraphRAG config creation.
     */
    testBasicConfigCreation(): void {
        try {
            const config = createGraphragConfig({
                rootDir: this.tempDir,
                models: {
                    [DEFAULT_CHAT_MODEL_ID]: new LanguageModelConfig(),
                    [DEFAULT_EMBEDDING_MODEL_ID]: new LanguageModelConfig()
                }
            });

            const passed = config.rootDir === this.tempDir &&
                          Object.keys(config.models).length === 2 &&
                          config.models[DEFAULT_CHAT_MODEL_ID] !== undefined;

            this.addResult(
                'Basic Config Creation',
                passed,
                undefined,
                `Root dir: ${config.rootDir}, Models: ${Object.keys(config.models).length}`
            );
        } catch (error) {
            this.addResult('Basic Config Creation', false, error.message);
        }
    }

    /**
     * Test config validation.
     */
    testConfigValidation(): void {
        try {
            // This should fail due to missing required models
            createGraphragConfig({
                rootDir: this.tempDir,
                models: {} // Empty models should trigger validation error
            });

            this.addResult('Config Validation', false, 'Validation should have failed but didn\'t');
        } catch (error) {
            const passed = error.message.includes('not found in configuration');
            this.addResult(
                'Config Validation',
                passed,
                passed ? undefined : `Unexpected error: ${error.message}`,
                'Correctly rejected invalid config'
            );
        }
    }

    /**
     * Test environment reader functionality.
     */
    testEnvironmentReader(): void {
        try {
            const reader = new EnvironmentReader();
            
            // Test basic functionality
            reader.use({ test_key: 'test_value' });
            const value = reader.str('test_key', 'default');
            
            const passed = value === 'test_value';
            this.addResult(
                'Environment Reader',
                passed,
                undefined,
                `Retrieved value: ${value}`
            );
        } catch (error) {
            this.addResult('Environment Reader', false, error.message);
        }
    }

    /**
     * Test embeddings functionality.
     */
    testEmbeddings(): void {
        try {
            const allEmbeddingsCount = allEmbeddings.length;
            const defaultEmbeddingsCount = defaultEmbeddings.length;
            const collectionName = createCollectionName('test');

            const passed = allEmbeddingsCount > 0 &&
                          defaultEmbeddingsCount > 0 &&
                          typeof collectionName === 'string' &&
                          collectionName.length > 0;

            this.addResult(
                'Embeddings',
                passed,
                undefined,
                `All: ${allEmbeddingsCount}, Default: ${defaultEmbeddingsCount}, Collection: ${collectionName}`
            );
        } catch (error) {
            this.addResult('Embeddings', false, error.message);
        }
    }

    /**
     * Test dotenv reading functionality.
     */
    testDotenvReading(): void {
        try {
            // Create a test .env file
            const envPath = path.join(this.tempDir, '.env');
            fs.writeFileSync(envPath, 'TEST_VAR=test_value\nANOTHER_VAR="quoted value"');

            // Test reading
            readDotenv(this.tempDir);

            this.addResult(
                'Dotenv Reading',
                true,
                undefined,
                'Successfully read .env file'
            );
        } catch (error) {
            this.addResult('Dotenv Reading', false, error.message);
        }
    }

    /**
     * Test init content generation.
     */
    testInitContent(): void {
        try {
            const passed = typeof INIT_YAML === 'string' &&
                          INIT_YAML.includes('models:') &&
                          INIT_YAML.includes('input:') &&
                          INIT_YAML.includes('chunks:');

            this.addResult(
                'Init Content',
                passed,
                undefined,
                `YAML length: ${INIT_YAML.length} characters`
            );
        } catch (error) {
            this.addResult('Init Content', false, error.message);
        }
    }

    /**
     * Test config loading functionality.
     */
    testConfigLoading(): void {
        try {
            // Create a test config file
            const configPath = path.join(this.tempDir, 'settings.yaml');
            const testConfig = `
models:
  ${DEFAULT_CHAT_MODEL_ID}:
    type: openai_chat
    api_key: test_key
    model: gpt-4
  ${DEFAULT_EMBEDDING_MODEL_ID}:
    type: openai_embedding
    api_key: test_key
    model: text-embedding-ada-002

input:
  storage:
    type: file
    base_dir: input
  file_type: text

chunks:
  size: 300
  overlap: 100
`;
            fs.writeFileSync(configPath, testConfig);

            const config = loadConfig(this.tempDir);
            const passed = config !== null &&
                          config.models[DEFAULT_CHAT_MODEL_ID] !== undefined;

            this.addResult(
                'Config Loading',
                passed,
                undefined,
                `Loaded config with ${Object.keys(config?.models || {}).length} models`
            );
        } catch (error) {
            this.addResult('Config Loading', false, error.message);
        }
    }

    /**
     * Test embedding settings functionality.
     */
    testEmbeddingSettings(): void {
        try {
            const config = createGraphragConfig({
                rootDir: this.tempDir,
                models: {
                    [DEFAULT_CHAT_MODEL_ID]: new LanguageModelConfig(),
                    [DEFAULT_EMBEDDING_MODEL_ID]: new LanguageModelConfig()
                }
            });

            const settings = getEmbeddingSettings(config);
            const passed = settings !== null && typeof settings === 'object';

            this.addResult(
                'Embedding Settings',
                passed,
                undefined,
                'Successfully retrieved embedding settings'
            );
        } catch (error) {
            this.addResult('Embedding Settings', false, error.message);
        }
    }

    /**
     * Run all tests and return results.
     */
    runAllTests(): TestResult[] {
        console.log('🚀 Starting comprehensive GraphRAG config conversion tests...\n');

        this.testBasicConfigCreation();
        this.testConfigValidation();
        this.testEnvironmentReader();
        this.testEmbeddings();
        this.testDotenvReading();
        this.testInitContent();
        this.testConfigLoading();
        this.testEmbeddingSettings();

        return this.results;
    }

    /**
     * Print test results.
     */
    printResults(): void {
        const passed = this.results.filter(r => r.passed).length;
        const total = this.results.length;

        console.log('\n📊 Test Results Summary:');
        console.log('========================');

        for (const result of this.results) {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.name}`);
            
            if (result.details) {
                console.log(`   Details: ${result.details}`);
            }
            
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        }

        console.log('\n📈 Overall Results:');
        console.log(`   Passed: ${passed}/${total} (${Math.round(passed/total*100)}%)`);
        
        if (passed === total) {
            console.log('🎉 All tests passed! TypeScript conversion is successful.');
        } else {
            console.log('⚠️  Some tests failed. Please review the conversion.');
        }
    }

    /**
     * Cleanup test resources.
     */
    cleanup(): void {
        try {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
        } catch (error) {
            console.warn(`Warning: Could not cleanup temp directory: ${error.message}`);
        }
    }
}

/**
 * Run tests if this file is executed directly.
 */
function runTests(): void {
    const tester = new ConfigConversionTester();
    
    try {
        tester.runAllTests();
        tester.printResults();
    } finally {
        tester.cleanup();
    }
}

// Export for use in other modules
export { ConfigConversionTester, runTests };

// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}
