/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing run and _create_node_position methods definitions.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Graph } from '../../utils/graphs.js';
import { NodeEmbeddings } from '../embed_graph/typing.js';
import { GraphLayout, NodePosition, createNodePosition } from './typing.js';
import { ErrorHandlerFn } from '../../typing/error_handler.js';

const logger = console;

/**
 * Run UMAP layout algorithm.
 * Matches the Python run function exactly.
 */
export function run(
    graph: Graph,
    embeddings: NodeEmbeddings,
    on_error: ErrorHandlerFn
): GraphLayout {
    const node_clusters: number[] = [];
    const node_sizes: number[] = [];

    const filtered_embeddings = _filter_raw_embeddings(embeddings);
    const nodes = Object.keys(filtered_embeddings);
    const embedding_vectors = nodes.map(node_id => filtered_embeddings[node_id]);

    for (const node_id of nodes) {
        const node = graph.nodes.get(node_id) || {};
        const cluster = node.cluster || node.community || -1;
        node_clusters.push(cluster);
        const size = node.degree || node.size || 0;
        node_sizes.push(size);
    }

    const additional_args: any = {};
    if (node_clusters.length > 0) {
        additional_args.node_categories = node_clusters;
    }
    if (node_sizes.length > 0) {
        additional_args.node_sizes = node_sizes;
    }

    try {
        return compute_umap_positions(
            embedding_vectors,
            nodes,
            additional_args.node_categories,
            additional_args.node_sizes
        );
    } catch (e) {
        const error = e instanceof Error ? e : new Error(String(e));
        logger.error('Error running UMAP', error);
        on_error(error, error.stack || '', null);

        // Umap may fail due to input sparseness or memory pressure.
        // For now, in these cases, we'll just return a layout with all nodes at (0, 0)
        const result: NodePosition[] = [];
        for (let i = 0; i < nodes.length; i++) {
            const cluster = node_clusters.length > 0 ? node_clusters[i] : 1;
            result.push({
                x: 0,
                y: 0,
                label: nodes[i],
                size: 0,
                cluster: String(cluster)
            });
        }
        return result;
    }
}

/**
 * Filter out null embeddings.
 * Matches the Python _filter_raw_embeddings function exactly.
 */
function _filter_raw_embeddings(embeddings: NodeEmbeddings): NodeEmbeddings {
    const filtered: NodeEmbeddings = {};
    for (const [node_id, embedding] of Object.entries(embeddings)) {
        if (embedding !== null && embedding !== undefined) {
            filtered[node_id] = embedding;
        }
    }
    return filtered;
}

/**
 * Project embedding vectors down to 2D/3D using UMAP.
 * Matches the Python compute_umap_positions function exactly.
 */
function compute_umap_positions(
    embedding_vectors: number[][],
    node_labels: string[],
    node_categories?: number[],
    node_sizes?: number[],
    min_dist: number = 0.75,
    n_neighbors: number = 5,
    spread: number = 1,
    metric: string = "euclidean",
    n_components: number = 2,
    random_state: number = 86
): NodePosition[] {
    // NOTE: This import is done here to reduce the initial import time of the graphrag package
    // TODO: Implement actual UMAP algorithm or use umap-js library
    console.warn('UMAP implementation is simplified. Consider using umap-js or similar library.');

    const embedding_position_data: NodePosition[] = [];

    for (let index = 0; index < node_labels.length; index++) {
        const node_name = node_labels[index];
        // Simple positioning as placeholder - in Python this would use actual UMAP
        const node_points = [
            Math.random() * 100 - 50, // x: -50 to 50
            Math.random() * 100 - 50  // y: -50 to 50
        ];

        const node_category = node_categories ? node_categories[index] : 1;
        const node_size = node_sizes ? node_sizes[index] : 1;

        if (n_components === 2) {
            embedding_position_data.push({
                label: String(node_name),
                x: node_points[0],
                y: node_points[1],
                cluster: String(Math.floor(node_category)),
                size: Math.floor(node_size)
            });
        } else {
            embedding_position_data.push({
                label: String(node_name),
                x: node_points[0],
                y: node_points[1],
                z: Math.random() * 100 - 50, // z: -50 to 50
                cluster: String(Math.floor(node_category)),
                size: Math.floor(node_size)
            });
        }
    }

    return embedding_position_data;
}