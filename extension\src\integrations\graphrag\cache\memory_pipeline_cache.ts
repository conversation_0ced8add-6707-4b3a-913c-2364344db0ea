// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing 'InMemoryCache' model.
 */

import { PipelineCache } from './pipeline_cache';

/**
 * In memory cache class definition.
 */
export class InMemoryCache extends PipelineCache {
    private _cache: Record<string, any>;
    private _name: string;

    /**
     * Init method definition.
     */
    constructor(name?: string | null) {
        super();
        this._cache = {};
        this._name = name || "";
    }

    /**
     * Get the value for the given key.
     *
     * @param key - The key to get the value for.
     * @returns The value for the given key.
     */
    async get(key: string): Promise<any> {
        const cacheKey = this._createCacheKey(key);
        return this._cache[cacheKey];
    }

    /**
     * Set the value for the given key.
     *
     * @param key - The key to set the value for.
     * @param value - The value to set.
     * @param debugData - Optional debug data (ignored in memory cache).
     */
    async set(key: string, value: any, debugData?: Record<string, any> | null): Promise<void> {
        const cacheKey = this._createCacheKey(key);
        this._cache[cacheKey] = value;
    }

    /**
     * Return True if the given key exists in the storage.
     *
     * @param key - The key to check for.
     * @returns True if the key exists in the storage, False otherwise.
     */
    async has(key: string): Promise<boolean> {
        const cacheKey = this._createCacheKey(key);
        return cacheKey in this._cache;
    }

    /**
     * Delete the given key from the storage.
     *
     * @param key - The key to delete.
     */
    async delete(key: string): Promise<void> {
        const cacheKey = this._createCacheKey(key);
        delete this._cache[cacheKey];
    }

    /**
     * Clear the storage.
     */
    async clear(): Promise<void> {
        this._cache = {};
    }

    /**
     * Create a sub cache with the given name.
     */
    child(name: string): PipelineCache {
        return new InMemoryCache(name);
    }

    /**
     * Create a cache key for the given key.
     */
    private _createCacheKey(key: string): string {
        return `${this._name}${key}`;
    }
}