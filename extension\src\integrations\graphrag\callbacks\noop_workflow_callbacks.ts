// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A no-op implementation of WorkflowCallbacks.
 */

import { WorkflowCallbacks } from './workflow_callbacks';
import { PipelineRunResult } from '../index/typing/pipeline_run_result';
import { Progress } from '../logger/progress';

/**
 * A no-op implementation of WorkflowCallbacks that logs all events to standard logging.
 */
export class NoopWorkflowCallbacks implements WorkflowCallbacks {
    /**
     * Execute this callback to signal when the entire pipeline starts.
     */
    pipeline_start(names: string[]): void {
        // No-op implementation
    }

    /**
     * Execute this callback to signal when the entire pipeline ends.
     */
    pipeline_end(results: PipelineRunResult[]): void {
        // No-op implementation
    }

    /**
     * Execute this callback when a workflow starts.
     */
    workflow_start(name: string, instance: object): void {
        // No-op implementation
    }

    /**
     * Execute this callback when a workflow ends.
     */
    workflow_end(name: string, instance: object): void {
        // No-op implementation
    }

    /**
     * Handle when progress occurs.
     */
    progress(progress: Progress): void {
        // No-op implementation
    }
}