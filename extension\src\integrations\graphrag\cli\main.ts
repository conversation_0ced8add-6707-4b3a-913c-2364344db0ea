// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * CLI entrypoint.
 */

import * as fs from 'fs';
import * as path from 'path';
import { graphragConfigDefaults } from '../config/defaults';
import { IndexingMethod, SearchMethod } from '../config/enums';
import { LIMIT, MAX_TOKEN_COUNT, N_SUBSET_MAX, K } from '../prompt_tune/defaults';
import { DocSelectionType } from '../prompt_tune/types';

const INVALID_METHOD_ERROR = "Invalid method";

/**
 * Path autocomplete options.
 */
export interface PathAutocompleteOptions {
    fileOkay?: boolean;
    dirOkay?: boolean;
    readable?: boolean;
    writable?: boolean;
    matchWildcard?: string;
}

/**
 * A workaround for typer's lack of support for proper autocompletion of file/directory paths
 * For more detail, watch
 *   https://github.com/fastapi/typer/discussions/682
 *   https://github.com/fastapi/typer/issues/951
 */
export function pathAutocomplete(options: PathAutocompleteOptions = {}): (incomplete: string) => string[] {
    const {
        fileOkay = true,
        dirOkay = true,
        readable = true,
        writable = false,
        matchWildcard = null
    } = options;

    function wildcardMatch(string: string, pattern: string): boolean {
        const regex = pattern.replace(/\?/g, '.').replace(/\*/g, '.*');
        return new RegExp(`^${regex}$`).test(string);
    }

    return function completer(incomplete: string): string[] {
        try {
            // List items in the current directory
            const items = fs.readdirSync('.', { withFileTypes: true });
            const completions: string[] = [];

            for (const item of items) {
                // Filter based on file/directory properties
                if (!fileOkay && item.isFile()) {
                    continue;
                }
                if (!dirOkay && item.isDirectory()) {
                    continue;
                }
                if (readable && !fs.constants.R_OK) {
                    try {
                        fs.accessSync(item.name, fs.constants.R_OK);
                    } catch {
                        continue;
                    }
                }
                if (writable && !fs.constants.W_OK) {
                    try {
                        fs.accessSync(item.name, fs.constants.W_OK);
                    } catch {
                        continue;
                    }
                }

                // Append the name of the matching item
                completions.push(item.name);
            }

            // Apply wildcard matching if required
            let filteredCompletions = completions;
            if (matchWildcard) {
                filteredCompletions = completions.filter(i => wildcardMatch(i, matchWildcard));
            }

            // Return completions that start with the given incomplete string
            return filteredCompletions.filter(i => i.startsWith(incomplete));
        } catch {
            return [];
        }
    };
}

export const CONFIG_AUTOCOMPLETE = pathAutocomplete({
    fileOkay: true,
    dirOkay: false,
    matchWildcard: "*.yaml",
    readable: true,
});

export const ROOT_AUTOCOMPLETE = pathAutocomplete({
    fileOkay: false,
    dirOkay: true,
    writable: true,
    matchWildcard: "*",
});

/**
 * CLI interface for GraphRAG commands.
 */
export interface GraphRagCLI {
    init(options: InitOptions): Promise<void>;
    index(options: IndexOptions): Promise<void>;
    update(options: UpdateOptions): Promise<void>;
    promptTune(options: PromptTuneOptions): Promise<void>;
    query(options: QueryOptions): Promise<void>;
}

export interface InitOptions {
    root?: string;
    force?: boolean;
}

export interface IndexOptions {
    config?: string;
    root?: string;
    method?: IndexingMethod;
    verbose?: boolean;
    memprofile?: boolean;
    dryRun?: boolean;
    cache?: boolean;
    skipValidation?: boolean;
    output?: string;
}

export interface UpdateOptions {
    config?: string;
    root?: string;
    method?: IndexingMethod;
    verbose?: boolean;
    memprofile?: boolean;
    cache?: boolean;
    skipValidation?: boolean;
    output?: string;
}

export interface PromptTuneOptions {
    root?: string;
    config?: string;
    verbose?: boolean;
    domain?: string;
    selectionMethod?: DocSelectionType;
    nSubsetMax?: number;
    k?: number;
    limit?: number;
    maxTokens?: number;
    minExamplesRequired?: number;
    chunkSize?: number;
    overlap?: number;
    language?: string;
    discoverEntityTypes?: boolean;
    output?: string;
}

export interface QueryOptions {
    method: SearchMethod;
    query: string;
    config?: string;
    verbose?: boolean;
    data?: string;
    root?: string;
    communityLevel?: number;
    dynamicCommunitySelection?: boolean;
    responseType?: string;
    streaming?: boolean;
}

/**
 * Implementation of GraphRAG CLI.
 */
export class GraphRagCLIImpl implements GraphRagCLI {
    /**
     * Generate a default configuration file.
     */
    async init(options: InitOptions = {}): Promise<void> {
        const { root = process.cwd(), force = false } = options;
        const { initialize_project_at } = await import('./initialize');
        await initialize_project_at(root, force);
    }

    /**
     * Build a knowledge graph index.
     */
    async index(options: IndexOptions = {}): Promise<void> {
        const {
            config,
            root = process.cwd(),
            method = IndexingMethod.STANDARD,
            verbose = false,
            memprofile = false,
            dryRun = false,
            cache = true,
            skipValidation = false,
            output
        } = options;

        const { index_cli } = await import('./index');
        await index_cli({
            root_dir: root,
            verbose,
            memprofile,
            cache,
            config_filepath: config,
            dry_run: dryRun,
            skip_validation: skipValidation,
            output_dir: output,
            method,
        });
    }

    /**
     * Update an existing knowledge graph index.
     */
    async update(options: UpdateOptions = {}): Promise<void> {
        const {
            config,
            root = process.cwd(),
            method = IndexingMethod.STANDARD,
            verbose = false,
            memprofile = false,
            cache = true,
            skipValidation = false,
            output
        } = options;

        const { update_cli } = await import('./index');
        await update_cli({
            root_dir: root,
            verbose,
            memprofile,
            cache,
            config_filepath: config,
            skip_validation: skipValidation,
            output_dir: output,
            method,
        });
    }

    /**
     * Generate custom graphrag prompts with your own data (i.e. auto templating).
     */
    async promptTune(options: PromptTuneOptions = {}): Promise<void> {
        const {
            root = process.cwd(),
            config,
            verbose = false,
            domain,
            selectionMethod = DocSelectionType.RANDOM,
            nSubsetMax = N_SUBSET_MAX,
            k = K,
            limit = LIMIT,
            maxTokens = MAX_TOKEN_COUNT,
            minExamplesRequired = 2,
            chunkSize = graphragConfigDefaults.chunks.size,
            overlap = graphragConfigDefaults.chunks.overlap,
            language,
            discoverEntityTypes = true,
            output = "prompts"
        } = options;

        const { prompt_tune } = await import('./prompt_tune');
        await prompt_tune({
            root,
            config,
            domain,
            verbose,
            selection_method: selectionMethod,
            limit,
            max_tokens: maxTokens,
            chunk_size: chunkSize,
            overlap,
            language,
            discover_entity_types: discoverEntityTypes,
            output,
            n_subset_max: nSubsetMax,
            k,
            min_examples_required: minExamplesRequired,
        });
    }

    /**
     * Query a knowledge graph index.
     */
    async query(options: QueryOptions): Promise<void> {
        const {
            method,
            query,
            config,
            verbose = false,
            data,
            root = process.cwd(),
            communityLevel = 2,
            dynamicCommunitySelection = false,
            responseType = "Multiple Paragraphs",
            streaming = false
        } = options;

        const {
            run_basic_search,
            run_drift_search,
            run_global_search,
            run_local_search,
        } = await import('./query');

        switch (method) {
            case SearchMethod.LOCAL:
                await run_local_search({
                    config_filepath: config,
                    data_dir: data,
                    root_dir: root,
                    community_level: communityLevel,
                    response_type: responseType,
                    streaming,
                    query,
                    verbose,
                });
                break;
            case SearchMethod.GLOBAL:
                await run_global_search({
                    config_filepath: config,
                    data_dir: data,
                    root_dir: root,
                    community_level: communityLevel,
                    dynamic_community_selection: dynamicCommunitySelection,
                    response_type: responseType,
                    streaming,
                    query,
                    verbose,
                });
                break;
            case SearchMethod.DRIFT:
                await run_drift_search({
                    config_filepath: config,
                    data_dir: data,
                    root_dir: root,
                    community_level: communityLevel,
                    streaming,
                    response_type: responseType,
                    query,
                    verbose,
                });
                break;
            case SearchMethod.BASIC:
                await run_basic_search({
                    config_filepath: config,
                    data_dir: data,
                    root_dir: root,
                    streaming,
                    query,
                    verbose,
                });
                break;
            default:
                throw new Error(INVALID_METHOD_ERROR);
        }
    }
}

/**
 * Create a new GraphRAG CLI instance.
 */
export function createGraphRagCLI(): GraphRagCLI {
    return new GraphRagCLIImpl();
}