/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing run_graph_intelligence, run_extract_graph and _create_text_splitter methods to run graph intelligence.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Graph } from '../../utils/graphs.js';
import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { LanguageModelConfig } from '../../../config/models/language_model_config.js';
import { ModelManager } from '../../../language_model/manager.js';
import { ChatModel } from '../../../language_model/protocol/base.js';
import { GraphExtractor } from './graph_extractor.js';
import { Document, EntityExtractionResult, EntityTypes, StrategyConfig } from './typing.js';

const logger = console;

/**
 * Run the graph intelligence entity extraction strategy.
 * Matches the Python run_graph_intelligence function exactly.
 */
export async function runGraphIntelligence(
    docs: Document[],
    entity_types: EntityTypes,
    cache: PipelineCache,
    args: StrategyConfig
): Promise<EntityExtractionResult> {
    const llm_config = args.llm as LanguageModelConfig;

    const llm = ModelManager.getInstance().getOrCreateChatModel(
        "extract_graph",
        llm_config.type,
        llm_config
    );

    return await runExtractGraph(llm, docs, entity_types, args);
}

/**
 * Run the entity extraction chain.
 * Matches the Python run_extract_graph function exactly.
 */
export async function runExtractGraph(
    model: ChatModel,
    docs: Document[],
    entity_types: EntityTypes,
    args: StrategyConfig
): Promise<EntityExtractionResult> {
    const tuple_delimiter = args.tuple_delimiter || null;
    const record_delimiter = args.record_delimiter || null;
    const completion_delimiter = args.completion_delimiter || null;
    const extraction_prompt = args.extraction_prompt || null;
    const max_gleanings = args.max_gleanings || 1; // TODO: Use actual default

    const extractor = new GraphExtractor(
        model,
        undefined, // tuple_delimiter_key
        undefined, // record_delimiter_key
        undefined, // input_text_key
        undefined, // entity_types_key
        undefined, // completion_delimiter_key
        extraction_prompt,
        true, // join_descriptions
        max_gleanings,
        (e, s, d) => {
            logger.error("Entity Extraction Error", e, { stack: s, details: d });
        }
    );

    const text_list = docs.map(doc => doc.text.trim());

    const results = await extractor.call(text_list, {
        entity_types: entity_types,
        tuple_delimiter: tuple_delimiter,
        record_delimiter: record_delimiter,
        completion_delimiter: completion_delimiter,
    });

    const graph = results.output;

    // Map the "source_id" back to the "id" field
    for (const [node_name, node_data] of graph.nodes.entries()) {
        if (node_data) {
            const source_ids = node_data.source_id.split(",");
            node_data.source_id = source_ids.map((id: string) => docs[parseInt(id.trim())].id).join(",");
        }
    }

    for (const [, edge_data] of graph.edges.entries()) {
        if (edge_data && edge_data.data) {
            const source_ids = edge_data.data.source_id.split(",");
            edge_data.data.source_id = source_ids.map((id: string) => docs[parseInt(id.trim())].id).join(",");
        }
    }

    const entities = Array.from(graph.nodes.entries()).map(([node_name, node_data]) => ({
        title: node_name,
        ...(node_data || {})
    }));

    const relationships = Array.from(graph.edges.entries()).map(([, edge_data]) => ({
        source: edge_data.source,
        target: edge_data.target,
        weight: edge_data.weight,
        ...(edge_data.data || {})
    }));

    return {
        entities,
        relationships,
        graph
    };
}