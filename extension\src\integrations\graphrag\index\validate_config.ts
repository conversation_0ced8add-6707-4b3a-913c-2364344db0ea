/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing validate_config_names definition.
 */

import { NoopWorkflowCallbacks } from '../callbacks/noop-workflow-callbacks';
import { GraphRagConfig } from '../config/models/graph-rag-config';
import { ModelManager } from '../language-model/manager';

const logger = console; // Using console for logging in TypeScript

/**
 * Validate config file for LLM deployment name typos.
 * @param parameters - The GraphRAG configuration to validate
 */
export async function validateConfigNames(parameters: GraphRagConfig): Promise<void> {
    // Validate Chat LLM configs
    // TODO: Replace default_chat_model with a way to select the model
    const defaultLlmSettings = parameters.getLanguageModelConfig("default_chat_model");

    const modelManager = new ModelManager();
    const llm = modelManager.registerChat({
        name: "test-llm",
        modelType: defaultLlmSettings.type,
        config: defaultLlmSettings,
        callbacks: new NoopWorkflowCallbacks(),
        cache: null,
    });

    try {
        await llm.achat("This is an LLM connectivity test. Say Hello World");
        logger.info("LLM Config Params Validated");
    } catch (e) {
        logger.error(`LLM configuration error detected. Exiting...\n${e}`);
        process.exit(1);
    }

    // Validate Embeddings LLM configs
    const embeddingLlmSettings = parameters.getLanguageModelConfig(
        parameters.embedText.modelId
    );

    const embedLlm = modelManager.registerEmbedding({
        name: "test-embed-llm",
        modelType: embeddingLlmSettings.type,
        config: embeddingLlmSettings,
        callbacks: new NoopWorkflowCallbacks(),
        cache: null,
    });

    try {
        await embedLlm.aembedBatch(["This is an LLM Embedding Test String"]);
        logger.info("Embedding LLM Config Params Validated");
    } catch (e) {
        logger.error(`Embedding LLM configuration error detected. Exiting...\n${e}`);
        process.exit(1);
    }
}