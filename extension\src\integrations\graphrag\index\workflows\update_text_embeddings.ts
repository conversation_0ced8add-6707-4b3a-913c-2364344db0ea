/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { getEmbeddingSettings } from '../../config/get-embedding-settings';
import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { getUpdateStorages } from '../run/utils';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { generateTextEmbeddings } from './generate-text-embeddings';
import { writeTableToStorage } from '../../utils/storage';

const logger = console;

/**
 * Update the text embeddings from a incremental index run.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: update_text_embeddings");
    
    const { outputStorage } = getUpdateStorages(
        config, 
        context.state["update_timestamp"]
    );

    const finalDocumentsDF = context.state["incremental_update_final_documents"];
    const mergedRelationshipsDF = context.state["incremental_update_merged_relationships"];
    const mergedTextUnits = context.state["incremental_update_merged_text_units"];
    const mergedEntitiesDF = context.state["incremental_update_merged_entities"];
    const mergedCommunityReports = context.state["incremental_update_merged_community_reports"];

    const embeddedFields = config.embedText.names;
    const textEmbed = getEmbeddingSettings(config);
    
    const result = await generateTextEmbeddings({
        documents: finalDocumentsDF,
        relationships: mergedRelationshipsDF,
        textUnits: mergedTextUnits,
        entities: mergedEntitiesDF,
        communityReports: mergedCommunityReports,
        callbacks: context.callbacks,
        cache: context.cache,
        textEmbedConfig: textEmbed,
        embeddedFields,
    });

    if (config.snapshots.embeddings) {
        for (const [name, table] of Object.entries(result)) {
            await writeTableToStorage(
                table,
                `embeddings.${name}`,
                outputStorage,
            );
        }
    }

    logger.info("Workflow completed: update_text_embeddings");
    return { result: null };
}