// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Base classes for vector stores.
 */

import { TextEmbedder } from '../data_model/types';

export const DEFAULT_VECTOR_SIZE: number = 1536;

/**
 * A document that is stored in vector storage.
 */
export interface VectorStoreDocument {
    /** unique id for the document */
    id: string | number;
    text?: string | null;
    vector?: number[] | null;
    /** store any additional metadata, e.g. title, date ranges, etc */
    attributes: Record<string, any>;
}

/**
 * A vector storage search result.
 */
export interface VectorStoreSearchResult {
    /** Document that was found. */
    document: VectorStoreDocument;
    /** Similarity score between -1 and 1. Higher is more similar. */
    score: number;
}

/**
 * The base class for vector storage data-access classes.
 */
export abstract class BaseVectorStore {
    protected collection_name: string;
    protected db_connection: any | null;
    protected document_collection: any | null;
    protected query_filter: any | null;
    protected kwargs: Record<string, any>;

    constructor(
        collection_name: string,
        db_connection: any | null = null,
        document_collection: any | null = null,
        query_filter: any | null = null,
        ...kwargs: any[]
    ) {
        this.collection_name = collection_name;
        this.db_connection = db_connection;
        this.document_collection = document_collection;
        this.query_filter = query_filter;
        this.kwargs = kwargs.length > 0 ? kwargs[0] : {};
    }

    /**
     * Connect to vector storage.
     */
    abstract connect(kwargs?: Record<string, any>): Promise<void> | void;

    /**
     * Load documents into the vector-store.
     */
    abstract loadDocuments(
        documents: VectorStoreDocument[],
        overwrite?: boolean
    ): Promise<void> | void;

    /**
     * Perform ANN search by vector.
     */
    abstract similaritySearchByVector(
        queryEmbedding: number[],
        k?: number,
        kwargs?: Record<string, any>
    ): Promise<VectorStoreSearchResult[]> | VectorStoreSearchResult[];

    /**
     * Perform ANN search by text.
     */
    abstract similaritySearchByText(
        text: string,
        textEmbedder: TextEmbedder,
        k?: number,
        kwargs?: Record<string, any>
    ): Promise<VectorStoreSearchResult[]> | VectorStoreSearchResult[];

    /**
     * Build a query filter to filter documents by id.
     */
    abstract filterById(includeIds: (string | number)[]): any;

    /**
     * Search for a document by id.
     */
    abstract searchById(id: string): Promise<VectorStoreDocument> | VectorStoreDocument;
}