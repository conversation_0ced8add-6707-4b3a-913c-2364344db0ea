/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { getUpdateStorages } from '../run/utils';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { PipelineStorage } from '../../storage/pipeline-storage';
import {
    loadTableFromStorage,
    storageHasTable,
    writeTableToStorage,
} from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * Update the covariates from a incremental index run.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: update_covariates");
    
    const { outputStorage, previousStorage, deltaStorage } = getUpdateStorages(
        config, 
        context.state["update_timestamp"]
    );

    if (await storageHasTable("covariates", previousStorage) && 
        await storageHasTable("covariates", deltaStorage)) {
        logger.info("Updating Covariates");
        await updateCovariates(previousStorage, deltaStorage, outputStorage);
    }

    logger.info("Workflow completed: update_covariates");
    return { result: null };
}

/**
 * Update the covariates output.
 */
async function updateCovariates(
    previousStorage: PipelineStorage,
    deltaStorage: PipelineStorage,
    outputStorage: PipelineStorage,
): Promise<void> {
    const oldCovariates = await loadTableFromStorage("covariates", previousStorage);
    const deltaCovariates = await loadTableFromStorage("covariates", deltaStorage);
    const mergedCovariates = mergeCovariates(oldCovariates, deltaCovariates);

    await writeTableToStorage(mergedCovariates, "covariates", outputStorage);
}

/**
 * Merge the covariates.
 * 
 * @param oldCovariates - The old covariates.
 * @param deltaCovariates - The delta covariates.
 * @returns The merged covariates.
 */
function mergeCovariates(
    oldCovariates: DataFrame, 
    deltaCovariates: DataFrame
): DataFrame {
    // Get the max human readable id from the old covariates and update the delta covariates
    const oldHumanReadableIds = oldCovariates.human_readable_id || [];
    const maxId = Math.max(...oldHumanReadableIds, 0);
    const initialId = maxId + 1;
    
    // Create new human_readable_id array for delta covariates
    const newHumanReadableIds = Array.from(
        { length: deltaCovariates.length }, 
        (_, i) => initialId + i
    );
    
    // Update delta covariates with new human_readable_id
    const updatedDeltaCovariates = { ...deltaCovariates };
    updatedDeltaCovariates.human_readable_id = newHumanReadableIds;

    // Concatenate the old and delta covariates
    return concatenateDataFrames([oldCovariates, updatedDeltaCovariates]);
}

/**
 * Concatenate multiple DataFrames.
 */
function concatenateDataFrames(dataframes: DataFrame[]): DataFrame {
    if (dataframes.length === 0) {
        return { length: 0 };
    }

    const result: DataFrame = { length: 0 };
    
    // Get all unique column names
    const allColumns = new Set<string>();
    for (const df of dataframes) {
        Object.keys(df).forEach(key => {
            if (key !== 'length') {
                allColumns.add(key);
            }
        });
    }

    // Calculate total length
    const totalLength = dataframes.reduce((sum, df) => sum + df.length, 0);
    result.length = totalLength;

    // Concatenate each column
    for (const column of allColumns) {
        const concatenatedColumn: any[] = [];
        
        for (const df of dataframes) {
            const columnData = df[column] || new Array(df.length).fill(null);
            concatenatedColumn.push(...columnData);
        }
        
        result[column] = concatenatedColumn;
    }

    return result;
}