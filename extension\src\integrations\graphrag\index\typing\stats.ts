/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Pipeline stats types.
 */

/**
 * Pipeline running stats.
 */
export interface PipelineRunStats {
    /** Float representing the total runtime */
    totalRuntime: number;
    
    /** Number of documents */
    numDocuments: number;
    
    /** Number of update documents */
    updateDocuments: number;
    
    /** Float representing the input load time */
    inputLoadTime: number;
    
    /** A dictionary of workflows */
    workflows: Record<string, Record<string, number>>;
}

/**
 * Create a new PipelineRunStats with default values
 */
export function createPipelineRunStats(): PipelineRunStats {
    return {
        totalRuntime: 0,
        numDocuments: 0,
        updateDocuments: 0,
        inputLoadTime: 0,
        workflows: {}
    };
}