/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the 'PipelineRunContext' models.
 */

import { PipelineCache } from '../../cache/pipeline-cache';
import { WorkflowCallbacks } from '../../callbacks/workflow-callbacks';
import { PipelineStorage } from '../../storage/pipeline-storage';
import { PipelineState } from './state';
import { PipelineRunStats } from './stats';

/**
 * Provides the context for the current pipeline run.
 */
export interface PipelineRunContext {
    /** Pipeline run statistics */
    stats: PipelineRunStats;
    
    /** Storage for input documents */
    inputStorage: PipelineStorage;
    
    /** Long-term storage for pipeline verbs to use. Items written here will be written to the storage provider */
    outputStorage: PipelineStorage;
    
    /** Storage for previous pipeline run when running in update mode */
    previousStorage: PipelineStorage;
    
    /** Cache instance for reading previous LLM responses */
    cache: PipelineCache;
    
    /** Callbacks to be called during the pipeline run */
    callbacks: WorkflowCallbacks;
    
    /** Arbitrary property bag for runtime state, persistent pre-computes, or experimental features */
    state: PipelineState;
}