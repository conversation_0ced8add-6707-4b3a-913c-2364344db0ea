/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Utilities for community summarization.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../data_model/types.js';
import * as schemas from '../../../data_model/schemas.js';

/**
 * Get levels from nodes DataFrame.
 * Matches the Python get_levels function exactly.
 */
export function getLevels(nodes: DataFrame, level_column: string = schemas.COMMUNITY_LEVEL): number[] {
    const levels = new Set<number>();

    // Python: levels = nodes[level_column].unique()
    nodes.data.forEach(row => {
        const level_value = row[level_column];
        if (level_value !== undefined && level_value !== null) {
            levels.add(parseInt(String(level_value), 10));
        }
    });

    // Python: return sorted(levels)
    return Array.from(levels).sort((a, b) => a - b);
}