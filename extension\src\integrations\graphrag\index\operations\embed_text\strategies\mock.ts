// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing run and _embed_text methods definitions.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineCache } from '../../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../../callbacks/workflow_callbacks.js';
import { TextEmbeddingResult } from './types.js';
import { ProgressTicker, progressTicker } from '../../../../logger/progress.js';

/**
 * Run the mock text embedding strategy.
 * Matches the Python implementation exactly.
 */
export async function run(
  input: string[],
  callbacks: WorkflowCallbacks,
  cache: PipelineCache,
  _args: Record<string, any>
): Promise<TextEmbeddingResult> {
  // Python: input = input if isinstance(input, Iterable) else [input]
  const inputArray = Array.isArray(input) ? input : [input];

  // Python: ticker = progress_ticker(callbacks.progress, len(input), description="generate embeddings progress: ")
  const ticker = progressTicker(
    callbacks.progress,
    inputArray.length,
    'generate embeddings progress: '
  );

  // Python: return TextEmbeddingResult(embeddings=[_embed_text(cache, text, ticker) for text in input])
  return {
    embeddings: inputArray.map(text => _embedText(cache, text, ticker))
  };
}

/**
 * Embed a single piece of text using mock random values.
 * Matches the Python function _embed_text exactly.
 */
function _embedText(_cache: PipelineCache, _text: string, tick: ProgressTicker): number[] {
  // Python: tick(1)
  tick.tick(1);
  // Python: return [random.random(), random.random(), random.random()]
  return [Math.random(), Math.random(), Math.random()];
}
