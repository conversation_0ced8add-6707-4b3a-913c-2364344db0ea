// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Test file for GraphRagConfig to verify the conversion works correctly.
 */

import { createGraphRagConfig, GraphRagConfig } from './graph-rag-config.js';
import { LanguageModelConfig } from './language_model_config.js';

/**
 * Test basic GraphRagConfig creation.
 */
function testBasicGraphRagConfig(): void {
  console.log('Testing basic GraphRagConfig creation...');
  
  try {
    // Create a minimal config with required models
    const config = createGraphRagConfig({
      rootDir: process.cwd(),
      models: {
        "default_chat": new LanguageModelConfig(),
        "default_embedding": new LanguageModelConfig()
      }
    });
    
    console.log('✅ Basic GraphRagConfig creation successful');
    console.log('Root directory:', config.rootDir);
    console.log('Models count:', Object.keys(config.models).length);
    
  } catch (error) {
    console.error('❌ Basic GraphRagConfig creation failed:', error);
  }
}

/**
 * Test GraphRagConfig validation.
 */
function testGraphRagConfigValidation(): void {
  console.log('Testing GraphRagConfig validation...');
  
  try {
    // This should fail due to missing required models
    const config = createGraphRagConfig({
      rootDir: process.cwd(),
      models: {} // Empty models should trigger validation error
    });
    
    console.error('❌ Validation should have failed but didn\'t');
    
  } catch (error) {
    console.log('✅ Validation correctly failed:', error.message);
  }
}

/**
 * Test GraphRagConfig utility methods.
 */
function testGraphRagConfigUtilityMethods(): void {
  console.log('Testing GraphRagConfig utility methods...');
  
  try {
    const testModel = new LanguageModelConfig();
    const config = createGraphRagConfig({
      rootDir: process.cwd(),
      models: {
        "default_chat": testModel,
        "default_embedding": testModel,
        "custom_model": testModel
      }
    });
    
    // Test getLanguageModelConfig
    const retrievedModel = config.getLanguageModelConfig("custom_model");
    console.log('✅ getLanguageModelConfig works');
    
    // Test getVectorStoreConfig
    const vectorStore = config.getVectorStoreConfig("default");
    console.log('✅ getVectorStoreConfig works');
    
    // Test toString
    const configString = config.toString();
    console.log('✅ toString works, length:', configString.length);
    
  } catch (error) {
    console.error('❌ Utility methods test failed:', error);
  }
}

/**
 * Run all tests.
 */
function runTests(): void {
  console.log('🚀 Starting GraphRagConfig tests...\n');
  
  testBasicGraphRagConfig();
  console.log('');
  
  testGraphRagConfigValidation();
  console.log('');
  
  testGraphRagConfigUtilityMethods();
  console.log('');
  
  console.log('🎉 GraphRagConfig tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

export { runTests };
