// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the basic search configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';

/**
 * The configuration section for Basic Search.
 */
export interface BasicSearchConfig {
  /**
   * The basic search prompt to use.
   */
  prompt?: string;

  /**
   * The model ID to use for basic search.
   */
  chatModelId: string;

  /**
   * The model ID to use for text embeddings.
   */
  embeddingModelId: string;

  /**
   * The number of text units to include in search context.
   */
  k: number;

  /**
   * The maximum tokens.
   */
  maxContextTokens: number;
}

/**
 * Create a BasicSearchConfig with default values.
 */
export function createBasicSearchConfig(config: Partial<BasicSearchConfig> = {}): BasicSearchConfig {
  return {
    prompt: config.prompt ?? graphragConfigDefaults.basicSearch.prompt,
    chatModelId: config.chatModelId ?? graphragConfigDefaults.basicSearch.chatModelId,
    embeddingModelId: config.embeddingModelId ?? graphragConfigDefaults.basicSearch.embeddingModelId,
    k: config.k ?? graphragConfigDefaults.basicSearch.k,
    maxContextTokens: config.maxContextTokens ?? graphragConfigDefaults.basicSearch.maxContextTokens,
  };
}
