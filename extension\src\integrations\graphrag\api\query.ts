// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Query Engine API.
 * 
 * This API provides access to the query engine of graphrag, allowing external applications
 * to hook into graphrag and run queries over a knowledge graph generated by graphrag.
 * 
 * Contains the following functions:
 *  - globalSearch: Perform a global search.
 *  - globalSearchStreaming: Perform a global search and stream results back.
 *  - localSearch: Perform a local search.
 *  - localSearchStreaming: Perform a local search and stream results back.
 * 
 * WARNING: This API is under development and may undergo changes in future releases.
 * Backwards compatibility is not guaranteed at this time.
 */

import { NoopQueryCallbacks } from '../callbacks/noop-query-callbacks';
import { QueryCallbacks } from '../callbacks/query-callbacks';
import {
    communityFullContentEmbedding,
    entityDescriptionEmbedding,
    textUnitTextEmbedding,
} from '../config/embeddings';
import { GraphRagConfig } from '../config/models/graph-rag-config';
import { initLoggers } from '../logger/standard-logging';
import {
    getBasicSearchEngine,
    getDriftSearchEngine,
    getGlobalSearchEngine,
    getLocalSearchEngine,
} from '../query/factory';
import {
    readIndexerCommunities,
    readIndexerCovariates,
    readIndexerEntities,
    readIndexerRelationships,
    readIndexerReportEmbeddings,
    readIndexerReports,
    readIndexerTextUnits,
} from '../query/indexer-adapters';
import {
    getEmbeddingStore,
    loadSearchPrompt,
    truncate,
    updateContextData,
} from '../utils/api';
import { redact } from '../utils/cli';
import { DataFrame } from '../utils/storage';

// Type definitions for search results
export type SearchResult = string | Record<string, any> | Record<string, any>[];
export type ContextData = string | DataFrame[] | Record<string, DataFrame>;

/**
 * Perform a global search and return the context data and response.
 */
export async function globalSearch(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    communityLevel?: number | null;
    dynamicCommunitySelection: boolean;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false, callbacks = [] } = options;
    
    initLoggers({ config, verbose });

    let fullResponse = "";
    let contextData: any = {};

    function onContext(context: any): void {
        contextData = context;
    }

    const localCallbacks = new NoopQueryCallbacks();
    localCallbacks.onContext = onContext;
    callbacks.push(localCallbacks);

    console.debug(`Executing global search query: ${options.query}`);
    
    for await (const chunk of globalSearchStreaming(options)) {
        fullResponse += chunk;
    }
    
    console.debug(`Query response: ${truncate(fullResponse, 400)}`);
    return [fullResponse, contextData];
}

/**
 * Perform a global search and return the context data and response via a generator.
 */
export async function* globalSearchStreaming(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    communityLevel?: number | null;
    dynamicCommunitySelection: boolean;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): AsyncGenerator<string, void, unknown> {
    const { config, verbose = false } = options;
    
    initLoggers({ config, verbose });

    const communities_ = readIndexerCommunities(options.communities, options.communityReports);
    const reports = readIndexerReports(
        options.communityReports,
        options.communities,
        options.communityLevel,
        options.dynamicCommunitySelection
    );
    const entities_ = readIndexerEntities(
        options.entities,
        options.communities,
        options.communityLevel
    );
    
    const mapPrompt = loadSearchPrompt(config.rootDir, config.globalSearch.mapPrompt);
    const reducePrompt = loadSearchPrompt(config.rootDir, config.globalSearch.reducePrompt);
    const knowledgePrompt = loadSearchPrompt(config.rootDir, config.globalSearch.knowledgePrompt);

    console.debug(`Executing streaming global search query: ${options.query}`);
    
    const searchEngine = getGlobalSearchEngine({
        config,
        reports,
        entities: entities_,
        communities: communities_,
        responseType: options.responseType,
        dynamicCommunitySelection: options.dynamicCommunitySelection,
        mapSystemPrompt: mapPrompt,
        reduceSystemPrompt: reducePrompt,
        generalKnowledgeInclusionPrompt: knowledgePrompt,
        callbacks: options.callbacks,
    });
    
    yield* searchEngine.streamSearch(options.query);
}

/**
 * Perform a local search and return the context data and response.
 */
export async function localSearch(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    textUnits: DataFrame;
    relationships: DataFrame;
    covariates?: DataFrame | null;
    communityLevel: number;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false, callbacks = [] } = options;
    
    initLoggers({ config, verbose });

    let fullResponse = "";
    let contextData: any = {};

    function onContext(context: any): void {
        contextData = context;
    }

    const localCallbacks = new NoopQueryCallbacks();
    localCallbacks.onContext = onContext;
    callbacks.push(localCallbacks);

    console.debug(`Executing local search query: ${options.query}`);
    
    for await (const chunk of localSearchStreaming(options)) {
        fullResponse += chunk;
    }
    
    console.debug(`Query response: ${truncate(fullResponse, 400)}`);
    return [fullResponse, contextData];
}

/**
 * Perform a local search and return the context data and response via a generator.
 */
export async function* localSearchStreaming(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    textUnits: DataFrame;
    relationships: DataFrame;
    covariates?: DataFrame | null;
    communityLevel: number;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): AsyncGenerator<string, void, unknown> {
    const { config, verbose = false } = options;
    
    initLoggers({ config, verbose });

    const vectorStoreArgs: Record<string, any> = {};
    for (const [index, store] of Object.entries(config.vectorStore)) {
        vectorStoreArgs[index] = store.modelDump();
    }
    const msg = `Vector Store Args: ${redact(vectorStoreArgs)}`;
    console.debug(msg);

    const descriptionEmbeddingStore = getEmbeddingStore(
        vectorStoreArgs,
        entityDescriptionEmbedding
    );

    const entities_ = readIndexerEntities(options.entities, options.communities, options.communityLevel);
    const covariates_ = options.covariates ? readIndexerCovariates(options.covariates) : [];
    const prompt = loadSearchPrompt(config.rootDir, config.localSearch.prompt);

    console.debug(`Executing streaming local search query: ${options.query}`);
    
    const searchEngine = getLocalSearchEngine({
        config,
        reports: readIndexerReports(options.communityReports, options.communities, options.communityLevel),
        textUnits: readIndexerTextUnits(options.textUnits),
        entities: entities_,
        relationships: readIndexerRelationships(options.relationships),
        covariates: { claims: covariates_ },
        descriptionEmbeddingStore,
        responseType: options.responseType,
        systemPrompt: prompt,
        callbacks: options.callbacks,
    });
    
    yield* searchEngine.streamSearch(options.query);
}

/**
 * Perform a DRIFT search and return the context data and response.
 */
export async function driftSearch(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    textUnits: DataFrame;
    relationships: DataFrame;
    communityLevel: number;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false, callbacks = [] } = options;
    
    initLoggers({ config, verbose });

    let fullResponse = "";
    let contextData: any = {};

    function onContext(context: any): void {
        contextData = context;
    }

    const localCallbacks = new NoopQueryCallbacks();
    localCallbacks.onContext = onContext;
    callbacks.push(localCallbacks);

    console.debug(`Executing DRIFT search query: ${options.query}`);
    
    for await (const chunk of driftSearchStreaming(options)) {
        fullResponse += chunk;
    }
    
    console.debug(`Query response: ${truncate(fullResponse, 400)}`);
    return [fullResponse, contextData];
}

/**
 * Perform a DRIFT search and return the context data and response via a generator.
 */
export async function* driftSearchStreaming(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    textUnits: DataFrame;
    relationships: DataFrame;
    communityLevel: number;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): AsyncGenerator<string, void, unknown> {
    const { config, verbose = false } = options;

    initLoggers({ config, verbose });

    const vectorStoreArgs: Record<string, any> = {};
    for (const [index, store] of Object.entries(config.vectorStore)) {
        vectorStoreArgs[index] = (store as any).modelDump();
    }
    const msg = `Vector Store Args: ${redact(vectorStoreArgs)}`;
    console.debug(msg);

    const descriptionEmbeddingStore = getEmbeddingStore(
        vectorStoreArgs,
        entityDescriptionEmbedding
    );

    const fullContentEmbeddingStore = getEmbeddingStore(
        vectorStoreArgs,
        communityFullContentEmbedding
    );

    const entities_ = readIndexerEntities(options.entities, options.communities, options.communityLevel);
    const reports = readIndexerReports(options.communityReports, options.communities, options.communityLevel);
    readIndexerReportEmbeddings(reports, fullContentEmbeddingStore);
    const prompt = loadSearchPrompt(config.rootDir, config.driftSearch.prompt);
    const reducePrompt = loadSearchPrompt(config.rootDir, config.driftSearch.reducePrompt);

    console.debug(`Executing streaming drift search query: ${options.query}`);

    const searchEngine = getDriftSearchEngine(
        config,
        reports,
        readIndexerTextUnits(options.textUnits),
        entities_,
        readIndexerRelationships(options.relationships),
        descriptionEmbeddingStore,
        options.responseType,
        prompt || undefined,
        reducePrompt || undefined,
        options.callbacks || undefined
    );

    yield* searchEngine.streamSearch(options.query);
}

/**
 * Perform a basic search and return the context data and response.
 */
export async function basicSearch(options: {
    config: GraphRagConfig;
    textUnits: DataFrame;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false, callbacks = [] } = options;
    
    initLoggers({ config, verbose });

    let fullResponse = "";
    let contextData: any = {};

    function onContext(context: any): void {
        contextData = context;
    }

    const localCallbacks = new NoopQueryCallbacks();
    localCallbacks.onContext = onContext;
    callbacks.push(localCallbacks);

    console.debug(`Executing basic search query: ${options.query}`);
    
    for await (const chunk of basicSearchStreaming(options)) {
        fullResponse += chunk;
    }
    
    console.debug(`Query response: ${truncate(fullResponse, 400)}`);
    return [fullResponse, contextData];
}

/**
 * Perform a basic search and return the context data and response via a generator.
 */
export async function* basicSearchStreaming(options: {
    config: GraphRagConfig;
    textUnits: DataFrame;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): AsyncGenerator<string, void, unknown> {
    const { config, verbose = false } = options;
    
    initLoggers({ config, verbose });

    const vectorStoreArgs: Record<string, any> = {};
    for (const [index, store] of Object.entries(config.vectorStore)) {
        vectorStoreArgs[index] = (store as any).modelDump();
    }

    const textEmbeddingStore = getEmbeddingStore(
        vectorStoreArgs,
        textUnitTextEmbedding
    );

    const prompt = loadSearchPrompt(config.rootDir, config.basicSearch.prompt);

    console.debug(`Executing streaming basic search query: ${options.query}`);
    
    const searchEngine = getBasicSearchEngine(
        readIndexerTextUnits(options.textUnits),
        textEmbeddingStore,
        config,
        prompt || undefined,
        options.responseType,
        options.callbacks || undefined
    );
    
    yield* searchEngine.streamSearch(options.query);
}

/**
 * Helper function to combine multiple DataFrames into one.
 */
function combineDataFrames(dataFrames: DataFrame[]): DataFrame {
    if (dataFrames.length === 0) {
        return [];
    }

    if (dataFrames.length === 1) {
        return dataFrames[0];
    }

    // Combine all data from multiple DataFrames
    const combinedData: Record<string, any>[] = [];

    for (const df of dataFrames) {
        combinedData.push(...df);
    }

    return combinedData;
}

/**
 * Perform a multi-index global search and return the context data and response.
 */
export async function multiIndexGlobalSearch(options: {
    config: GraphRagConfig;
    entities: DataFrame[];
    communities: DataFrame[];
    communityReports: DataFrame[];
    communityLevel: number;
    responseType: string;
    query: string;
    dynamicCommunitySelection?: boolean;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false } = options;

    initLoggers({ config, verbose });

    // Combine all DataFrames from multiple indexes
    const combinedEntities = combineDataFrames(options.entities);
    const combinedCommunities = combineDataFrames(options.communities);
    const combinedCommunityReports = combineDataFrames(options.communityReports);

    // Use the regular global search with combined data
    return await globalSearch({
        config,
        entities: combinedEntities,
        communities: combinedCommunities,
        communityReports: combinedCommunityReports,
        communityLevel: options.communityLevel,
        responseType: options.responseType,
        query: options.query,
        dynamicCommunitySelection: options.dynamicCommunitySelection || false,
        callbacks: options.callbacks,
        verbose
    });
}

/**
 * Perform a multi-index local search and return the context data and response.
 */
export async function multiIndexLocalSearch(options: {
    config: GraphRagConfig;
    entities: DataFrame[];
    communities: DataFrame[];
    communityReports: DataFrame[];
    textUnits: DataFrame[];
    relationships: DataFrame[];
    covariates?: DataFrame[] | null;
    communityLevel: number;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false } = options;

    initLoggers({ config, verbose });

    // Combine all DataFrames from multiple indexes
    const combinedEntities = combineDataFrames(options.entities);
    const combinedCommunities = combineDataFrames(options.communities);
    const combinedCommunityReports = combineDataFrames(options.communityReports);
    const combinedTextUnits = combineDataFrames(options.textUnits);
    const combinedRelationships = combineDataFrames(options.relationships);
    const combinedCovariates = options.covariates ? combineDataFrames(options.covariates) : null;

    // Use the regular local search with combined data
    return await localSearch({
        config,
        entities: combinedEntities,
        communities: combinedCommunities,
        communityReports: combinedCommunityReports,
        textUnits: combinedTextUnits,
        relationships: combinedRelationships,
        covariates: combinedCovariates,
        communityLevel: options.communityLevel,
        responseType: options.responseType,
        query: options.query,
        callbacks: options.callbacks,
        verbose
    });
}

/**
 * Perform a multi-index DRIFT search and return the context data and response.
 */
export async function multiIndexDriftSearch(options: {
    config: GraphRagConfig;
    entities: DataFrame[];
    communities: DataFrame[];
    communityReports: DataFrame[];
    textUnits: DataFrame[];
    relationships: DataFrame[];
    communityLevel: number;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false } = options;

    initLoggers({ config, verbose });

    // Combine all DataFrames from multiple indexes
    const combinedEntities = combineDataFrames(options.entities);
    const combinedCommunities = combineDataFrames(options.communities);
    const combinedCommunityReports = combineDataFrames(options.communityReports);
    const combinedTextUnits = combineDataFrames(options.textUnits);
    const combinedRelationships = combineDataFrames(options.relationships);

    // Use the regular drift search with combined data
    return await driftSearch({
        config,
        entities: combinedEntities,
        communities: combinedCommunities,
        communityReports: combinedCommunityReports,
        textUnits: combinedTextUnits,
        relationships: combinedRelationships,
        communityLevel: options.communityLevel,
        responseType: options.responseType,
        query: options.query,
        callbacks: options.callbacks,
        verbose
    });
}

/**
 * Perform a multi-index basic search and return the context data and response.
 */
export async function multiIndexBasicSearch(options: {
    config: GraphRagConfig;
    textUnits: DataFrame[];
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false } = options;

    initLoggers({ config, verbose });

    // Combine all DataFrames from multiple indexes
    const combinedTextUnits = combineDataFrames(options.textUnits);

    // Use the regular basic search with combined data
    return await basicSearch({
        config,
        textUnits: combinedTextUnits,
        responseType: options.responseType,
        query: options.query,
        callbacks: options.callbacks,
        verbose
    });
}