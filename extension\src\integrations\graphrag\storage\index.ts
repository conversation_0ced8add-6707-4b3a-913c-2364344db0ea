/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * The storage package root.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

// Core storage interfaces
export * from './pipeline-storage'

// Storage implementations
export * from './file-pipeline-storage'
export * from './memory-pipeline-storage'
export * from './blob-pipeline-storage'
export * from './cosmosdb-pipeline-storage'

// Factory
export * from './factory'

// Re-export for convenience
export type { PipelineStorage } from './pipeline-storage'
export { StorageFactory } from './factory'
export { FilePipelineStorage, createFileStorage } from './file-pipeline-storage'
export { MemoryPipelineStorage } from './memory-pipeline-storage'
export { BlobPipelineStorage, createBlobStorage, validateBlobContainerName } from './blob-pipeline-storage'
export { CosmosDBPipelineStorage, createCosmosDbStorage } from './cosmosdb-pipeline-storage'