/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the explode_communities method definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../data_model/types.js';
import * as schemas from '../../../data_model/schemas.js';

/**
 * Explode communities into separate rows.
 * Matches the Python explode_communities function exactly.
 */
export function explode_communities(df: DataFrame): DataFrame {
    // Python: df = df.copy()
    const df_copy = {
        columns: [...df.columns],
        data: df.data.map(row => ({ ...row }))
    };

    // Python: df[schemas.COMMUNITY_ID] = df[schemas.COMMUNITY_ID].astype(str)
    df_copy.data.forEach(row => {
        if (row[schemas.COMMUNITY_ID] !== null && row[schemas.COMMUNITY_ID] !== undefined) {
            row[schemas.COMMUNITY_ID] = String(row[schemas.COMMUNITY_ID]);
        }
    });

    // Python: df = df.explode(schemas.COMMUNITY_ID)
    const exploded_data: Record<string, any>[] = [];
    
    df_copy.data.forEach(row => {
        const community_ids = row[schemas.COMMUNITY_ID];
        
        if (Array.isArray(community_ids)) {
            // If COMMUNITY_ID is an array, explode it
            community_ids.forEach(community_id => {
                exploded_data.push({
                    ...row,
                    [schemas.COMMUNITY_ID]: community_id
                });
            });
        } else if (typeof community_ids === 'string' && community_ids.includes(',')) {
            // If COMMUNITY_ID is a comma-separated string, split and explode
            const ids = community_ids.split(',').map(id => id.trim()).filter(id => id.length > 0);
            ids.forEach(community_id => {
                exploded_data.push({
                    ...row,
                    [schemas.COMMUNITY_ID]: community_id
                });
            });
        } else {
            // Single value, keep as is
            exploded_data.push(row);
        }
    });

    // Python: df[schemas.COMMUNITY_ID] = df[schemas.COMMUNITY_ID].astype(int)
    exploded_data.forEach(row => {
        if (row[schemas.COMMUNITY_ID] !== null && row[schemas.COMMUNITY_ID] !== undefined) {
            const community_id = String(row[schemas.COMMUNITY_ID]).trim();
            if (community_id !== '' && !isNaN(Number(community_id))) {
                row[schemas.COMMUNITY_ID] = parseInt(community_id, 10);
            }
        }
    });

    // Python: return df.reset_index(drop=True)
    return {
        columns: df_copy.columns,
        data: exploded_data
    };
}
