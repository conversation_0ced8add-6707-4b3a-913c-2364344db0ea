/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Create a noun phrase extractor from a configuration.
 */

import { NounPhraseExtractorType } from '../../../../config/enums.js';
import { TextAnalyzerConfig } from '../../../../config/models/extract_graph_nlp_config.js';
import { BaseNounPhraseExtractor } from './base.js';
import { CFGNounPhraseExtractor } from './cfg_extractor.js';
import { RegexENNounPhraseExtractor } from './regex_extractor.js';
import { SyntacticNounPhraseExtractor } from './syntactic_parsing_extractor.js';
import { EN_STOP_WORDS } from './stop_words.js';

/**
 * A factory class for creating noun phrase extractor.
 */
export class NounPhraseExtractorFactory {
    private static npExtractorTypes: Record<string, any> = {};

    /**
     * Register a noun phrase extractor type.
     */
    static register(npExtractorType: string, npExtractor: any): void {
        this.npExtractorTypes[npExtractorType] = npExtractor;
    }

    /**
     * Get the noun phrase extractor from configuration.
     * This method creates the appropriate extractor based on the configuration type.
     */
    static getNpExtractor(config: TextAnalyzerConfig): BaseNounPhraseExtractor {
        const npExtractorType = config.extractorType;
        const excludeNouns = config.excludeNouns || EN_STOP_WORDS;

        switch (npExtractorType) {
            case NounPhraseExtractorType.SYNTACTIC:
                return new SyntacticNounPhraseExtractor(
                    config.modelName,
                    config.maxWordLength,
                    config.includeNamedEntities,
                    config.excludeEntityTags,
                    config.excludePosTags,
                    excludeNouns,
                    config.wordDelimiter
                );

            case NounPhraseExtractorType.CFG:
                // Convert grammar configuration from string keys to tuple keys
                const grammars: Record<string, string> = {};
                for (const [key, value] of Object.entries(config.nounPhraseGrammars)) {
                    grammars[key] = value;
                }

                return new CFGNounPhraseExtractor(
                    config.modelName,
                    config.maxWordLength,
                    config.includeNamedEntities,
                    config.excludeEntityTags,
                    config.excludePosTags,
                    excludeNouns,
                    config.wordDelimiter,
                    grammars,
                    config.nounPhraseTags
                );

            case NounPhraseExtractorType.REGEX_ENGLISH:
                return new RegexENNounPhraseExtractor(
                    excludeNouns,
                    config.maxWordLength,
                    config.wordDelimiter
                );

            default:
                throw new Error(`Unknown noun phrase extractor type: ${npExtractorType}`);
        }
    }
}

/**
 * Create a noun phrase extractor from a configuration.
 */
export function createNounPhraseExtractor(
    analyzerConfig: TextAnalyzerConfig
): BaseNounPhraseExtractor {
    return NounPhraseExtractorFactory.getNpExtractor(analyzerConfig);
}