/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing load method definition.
 */

import { DataFrame } from '../../data_model/types.js';
import { InputConfig } from '../../config/models/input_config.js';
import { PipelineStorage } from '../../storage/pipeline_storage.js';
import { loadFiles, processDataColumns } from './util';

const logger = console;

/**
 * Parse CSV content into array of objects.
 * @param csvText - Raw CSV text content
 * @returns Array of row objects
 */
function parseCsv(csvText: string): Record<string, any>[] {
    const lines = csvText.trim().split('\n');
    if (lines.length === 0) return [];

    // Parse header line
    const headers = parseCsvLine(lines[0]);

    // Parse data lines
    const data: Record<string, any>[] = [];
    for (let i = 1; i < lines.length; i++) {
        const values = parseCsvLine(lines[i]);
        const row: Record<string, any> = {};

        headers.forEach((header, index) => {
            row[header] = values[index] || '';
        });

        data.push(row);
    }

    return data;
}

/**
 * Parse a single CSV line, handling quoted fields.
 * @param line - CSV line to parse
 * @returns Array of field values
 */
function parseCsvLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
        const char = line[i];

        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                // Escaped quote
                current += '"';
                i += 2;
            } else {
                // Toggle quote state
                inQuotes = !inQuotes;
                i++;
            }
        } else if (char === ',' && !inQuotes) {
            // Field separator
            result.push(current.trim());
            current = '';
            i++;
        } else {
            current += char;
            i++;
        }
    }

    // Add the last field
    result.push(current.trim());

    return result;
}

/**
 * Load csv inputs from a directory.
 * @param config - Input configuration
 * @param storage - Pipeline storage instance
 * @returns Promise resolving to DataFrame with CSV data
 */
export async function loadCsv(
    config: InputConfig,
    storage: PipelineStorage,
): Promise<DataFrame> {
    logger.info(`Loading csv files from ${config.storage.baseDir}`);

    async function loadFile(path: string, group?: Record<string, any>): Promise<DataFrame> {
        const groupData = group || {};
        
        // Get file content as bytes and parse CSV
        const buffer = await storage.get(path, true);
        const csvText = new TextDecoder(config.encoding || 'utf-8').decode(buffer);
        
        // Parse CSV content (improved parsing to handle quoted fields)
        const data = parseCsv(csvText);

        // Get column names from the first row (if any data exists)
        const baseColumns = data.length > 0 ? Object.keys(data[0]) : [];

        // Add group data to each row
        const additionalKeys = Object.keys(groupData);
        if (additionalKeys.length > 0) {
            data.forEach((row: Record<string, any>) => {
                additionalKeys.forEach(key => {
                    row[key] = groupData[key];
                });
            });
        }

        let result: DataFrame = {
            columns: [...baseColumns, ...additionalKeys],
            data: data
        };

        result = processDataColumns(result, config, path);

        const creationDate = await storage.getCreationDate(path);
        result.data = result.data.map(row => ({
            ...row,
            creation_date: creationDate
        }));

        return result;
    }

    return await loadFiles(loadFile, config, storage);
}