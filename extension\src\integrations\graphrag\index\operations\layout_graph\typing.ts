/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'NodePosition' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * Node position class definition.
 * Matches the Python dataclass structure exactly.
 */
export interface NodePosition {
    /** Node label */
    label: string;
    /** Cluster identifier */
    cluster: string;
    /** Node size */
    size: number;
    /** X coordinate */
    x: number;
    /** Y coordinate */
    y: number;
    /** Z coordinate (optional for 3D layouts) */
    z?: number | null;
}

/**
 * Graph layout type - array of node positions
 */
export type GraphLayout = NodePosition[];

/**
 * Convert NodePosition to pandas-like tuple format.
 * Matches the Python to_pandas method exactly.
 */
export function nodePositionToPandas(position: NodePosition): [string, number, number, string, number] {
    return [position.label, position.x, position.y, position.cluster, position.size];
}

/**
 * Create a NodePosition instance.
 * Helper function to match Python dataclass constructor behavior.
 */
export function createNodePosition(
    label: string,
    cluster: string,
    size: number,
    x: number,
    y: number,
    z?: number | null
): NodePosition {
    return {
        label,
        cluster,
        size,
        x,
        y,
        z: z !== undefined ? z : null
    };
}