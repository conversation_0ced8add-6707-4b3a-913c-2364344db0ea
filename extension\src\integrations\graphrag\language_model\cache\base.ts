// Copyright (c) 2025 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Base cache protocol definition.
 */

/**
 * Base cache protocol.
 */
export interface ModelCache {
    /**
     * Check if the cache has a value.
     * 
     * @param key - The cache key to check.
     * @returns True if the cache has the value, false otherwise.
     */
    has(key: string): Promise<boolean>;

    /**
     * Retrieve a value from the cache.
     * 
     * @param key - The cache key to retrieve.
     * @returns The cached value or null if not found.
     */
    get(key: string): Promise<any | null>;

    /**
     * Write a value into the cache.
     * 
     * @param key - The cache key to write to.
     * @param value - The value to cache.
     * @param metadata - Optional metadata associated with the cached value.
     */
    set(key: string, value: any, metadata?: Record<string, any> | null): Promise<void>;

    /**
     * Remove a value from the cache.
     * 
     * @param key - The cache key to remove.
     */
    remove(key: string): Promise<void>;

    /**
     * Clear the cache.
     */
    clear(): Promise<void>;

    /**
     * Create a child cache.
     * 
     * @param key - The key for the child cache.
     * @returns A child cache instance.
     */
    child(key: string): ModelCache;
}