/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing compute_edge_combined_degree methods definition.
 */

import { DataFrame } from '../../data-model/types';

/**
 * Compute the combined degree for each edge in a graph.
 * @param edgeDF - DataFrame containing edge data
 * @param nodeDegreeDF - DataFrame containing node degree data
 * @param nodeNameColumn - Column name for node names
 * @param nodeDegreeColumn - Column name for node degrees
 * @param edgeSourceColumn - Column name for edge source
 * @param edgeTargetColumn - Column name for edge target
 * @returns Array of combined degrees
 */
export function computeEdgeCombinedDegree(
    edgeDF: DataFrame,
    nodeDegreeDF: DataFrame,
    nodeNameColumn: string,
    nodeDegreeColumn: string,
    edgeSourceColumn: string,
    edgeTargetColumn: string
): number[] {
    // Create lookup maps for degrees
    const degreeMap = new Map<string, number>();
    nodeDegreeDF.data.forEach(row => {
        degreeMap.set(row[nodeNameColumn], row[nodeDegreeColumn] || 0);
    });

    // Calculate combined degrees for each edge
    return edgeDF.data.map(edge => {
        const sourceDegree = degreeMap.get(edge[edgeSourceColumn]) || 0;
        const targetDegree = degreeMap.get(edge[edgeTargetColumn]) || 0;
        return sourceDegree + targetDegree;
    });
}

/**
 * Helper function to generate degree column name.
 */
function degreeColname(column: string): string {
    return `${column}_degree`;
}