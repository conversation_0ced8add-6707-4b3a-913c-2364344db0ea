/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing create_community_reports and load_strategy methods definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../data_model/types.js';
import { PipelineCache } from '../../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../../callbacks/workflow_callbacks.js';
import { NoopWorkflowCallbacks } from '../../../callbacks/noop_workflow_callbacks.js';
import { AsyncType } from '../../../config/enums.js';
import { deriveFromRows } from '../../utils/derive_from_rows.js';
import {
    CommunityReport,
    CommunityReportsStrategy,
    CreateCommunityReportsStrategyType
} from './typing.js';
import { getLevels } from './utils.js';
import { run_graph_intelligence } from './strategies.js';
import * as schemas from '../../../data_model/schemas.js';

const logger = console;

/**
 * Generate community summaries.
 * Matches the Python create_community_reports function exactly.
 */
export async function create_community_reports(
    nodes: DataFrame,
    communities: DataFrame,
    local_contexts: DataFrame,
    level_context_builder: (
        reports: DataFrame,
        community_hierarchy_df: DataFrame,
        local_context_df: DataFrame,
        level: number,
        max_context_tokens: number
    ) => DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    strategy: Record<string, any>,
    max_input_length: number,
    async_mode: AsyncType = AsyncType.AsyncIO,
    num_threads: number = 4
): Promise<DataFrame> {
    const reports: Array<CommunityReport | null> = [];
    let completed = 0;
    const total_contexts = local_contexts.data.length;

    const tick = () => {
        completed += 1;
        if (callbacks.progress) {
            callbacks.progress(completed / total_contexts, 'Generating community reports');
        }
    };

    const strategy_exec = load_strategy(strategy.type);
    const strategy_config = { ...strategy };

    // Python: community_hierarchy = create_community_hierarchy(communities)
    const community_hierarchy = create_community_hierarchy(communities);

    const levels = getLevels(nodes, schemas.COMMUNITY_LEVEL);
    const level_contexts: DataFrame[] = [];

    for (const level of levels) {
        const reports_df: DataFrame = {
            columns: ['community_id', 'title', 'summary', 'full_content', 'rank', 'level'],
            data: reports.filter(r => r !== null).map(r => r!)
        };

        const level_context = level_context_builder(
            reports_df,
            community_hierarchy,
            local_contexts,
            level,
            max_input_length
        );
        level_contexts.push(level_context);
    }

    for (let i = 0; i < level_contexts.length; i++) {
        const level_context = level_contexts[i];

        async function run_generate(record: Record<string, any>): Promise<CommunityReport | null> {
            const result = await generate_report(
                strategy_exec,
                record[schemas.COMMUNITY_ID],
                record[schemas.COMMUNITY_LEVEL],
                record[schemas.CONTEXT_STRING],
                callbacks,
                cache,
                strategy_config
            );
            tick();
            return result;
        }

        const local_reports = await deriveFromRows(
            level_context,
            run_generate,
            new NoopWorkflowCallbacks(),
            num_threads,
            async_mode,
            `level ${levels[i]} summarize communities progress: `
        );

        reports.push(...local_reports.filter(lr => lr !== null));
    }

    // Python: Convert reports to DataFrame
    const valid_reports = reports.filter(r => r !== null) as CommunityReport[];
    const report_data = valid_reports.map(report => ({
        community_id: report.community_id,
        title: report.title,
        summary: report.summary,
        full_content: report.full_content,
        full_content_json: report.full_content_json,
        rank: report.rank,
        level: report.level,
        rank_explanation: report.rank_explanation,
        findings: report.findings
    }));

    return {
        columns: [
            'community_id', 'title', 'summary', 'full_content', 'full_content_json',
            'rank', 'level', 'rank_explanation', 'findings'
        ],
        data: report_data
    };
}
}

/**
 * Generate a report for a single community.
 * Matches the Python generate_report function exactly.
 */
async function generate_report(
    runner: CommunityReportsStrategy,
    community_id: string,
    community_level: number,
    community_context: string,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    strategy: Record<string, any>
): Promise<CommunityReport | null> {
    return await runner(
        community_id,
        community_context,
        community_level,
        callbacks,
        cache,
        strategy
    );
}

/**
 * Load strategy method definition.
 * Matches the Python load_strategy function exactly.
 */
function load_strategy(strategy: CreateCommunityReportsStrategyType): CommunityReportsStrategy {
    switch (strategy) {
        case CreateCommunityReportsStrategyType.graph_intelligence:
            return run_graph_intelligence;
        default:
            throw new Error(`Unknown strategy: ${strategy}`);
    }
}

/**
 * Create community hierarchy DataFrame.
 * Matches the Python create_community_hierarchy function exactly.
 */
function create_community_hierarchy(communities: DataFrame): DataFrame {
    const hierarchy_data: any[] = [];

    communities.data.forEach(row => {
        if (row.children && Array.isArray(row.children)) {
            row.children.forEach((child: any) => {
                hierarchy_data.push({
                    community_id: row.community_id,
                    level: row.level,
                    sub_community: child
                });
            });
        }
    });

    return {
        columns: ['community_id', 'level', 'sub_community'],
        data: hierarchy_data.filter(row => row.sub_community != null)
    };
}

// Compatibility exports for existing code
export const summarizeCommunities = create_community_reports;
export const generateReport = generate_report;
export const loadStrategy = load_strategy;
export const createCommunityHierarchy = create_community_hierarchy;