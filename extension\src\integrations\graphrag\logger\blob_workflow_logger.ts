// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A logger that emits updates from the indexing engine to a blob in Azure Storage.
 */

import * as path from 'path';

// Mock Azure SDK interfaces for TypeScript
interface BlobServiceClient {
    getBlobClient(containerName: string, blobName: string): BlobClient;
}

interface BlobClient {
    exists(): Promise<boolean>;
    createAppendBlob(): Promise<void>;
    appendBlock(data: string): Promise<void>;
}

interface DefaultAzureCredential {
    // Azure credential interface
}

// Mock implementations
class MockBlobServiceClient implements BlobServiceClient {
    static fromConnectionString(connectionString: string): MockBlobServiceClient {
        return new MockBlobServiceClient();
    }

    constructor(url?: string, credential?: DefaultAzureCredential) {}

    getBlobClient(containerName: string, blobName: string): BlobClient {
        return new MockBlobClient();
    }
}

class MockBlobClient implements BlobClient {
    async exists(): Promise<boolean> {
        return false;
    }

    async createAppendBlob(): Promise<void> {
        // Mock implementation
    }

    async appendBlock(data: string): Promise<void> {
        // Mock implementation - in real implementation would append to blob
        console.log('Appending to blob:', data);
    }
}

class MockDefaultAzureCredential implements DefaultAzureCredential {}

export enum LogLevel {
    DEBUG = 10,
    INFO = 20,
    WARNING = 30,
    ERROR = 40,
    CRITICAL = 50
}

export interface LogRecord {
    level: LogLevel;
    message: string;
    details?: any;
    error?: Error;
    stack?: string;
}

/**
 * A logging handler that writes to a blob storage account.
 */
export class BlobWorkflowLogger {
    private blobServiceClient: BlobServiceClient;
    private containerName: string;
    private blobName: string;
    private blobClient: BlobClient;
    private numBlocks: number = 0;
    private maxBlockCount: number = 25000; // 25k blocks per blob
    private connectionString?: string;
    private storageAccountBlobUrl?: string;

    constructor(
        connectionString?: string | null,
        containerName?: string | null,
        blobName: string = "",
        baseDir?: string | null,
        storageAccountBlobUrl?: string | null,
        level: LogLevel = LogLevel.INFO
    ) {
        if (!containerName) {
            throw new Error("No container name provided for blob storage.");
        }
        if (!connectionString && !storageAccountBlobUrl) {
            throw new Error("No storage account blob url provided for blob storage.");
        }

        this.connectionString = connectionString || undefined;
        this.storageAccountBlobUrl = storageAccountBlobUrl || undefined;

        if (this.connectionString) {
            this.blobServiceClient = MockBlobServiceClient.fromConnectionString(this.connectionString);
        } else {
            if (!storageAccountBlobUrl) {
                throw new Error("Either connection_string or storage_account_blob_url must be provided.");
            }

            this.blobServiceClient = new MockBlobServiceClient(
                storageAccountBlobUrl,
                new MockDefaultAzureCredential()
            );
        }

        if (blobName === "") {
            const now = new Date();
            blobName = `report/${now.toISOString().replace(/[:.]/g, '-')}.logs.json`;
        }

        this.blobName = path.join(baseDir || "", blobName);
        this.containerName = containerName;
        this.blobClient = this.blobServiceClient.getBlobClient(this.containerName, this.blobName);
        
        // Initialize blob if it doesn't exist
        this.initializeBlob();
        this.numBlocks = 0; // refresh block counter
    }

    private async initializeBlob(): Promise<void> {
        if (!(await this.blobClient.exists())) {
            await this.blobClient.createAppendBlob();
        }
    }

    /**
     * Emit a log record to blob storage.
     */
    async emit(record: LogRecord): Promise<void> {
        try {
            // Create JSON structure based on record
            const logData: Record<string, any> = {
                type: this.getLogType(record.level),
                data: record.message,
            };

            // Add additional fields if they exist
            if (record.details) {
                logData.details = record.details;
            }
            if (record.error) {
                logData.cause = record.error.message;
            }
            if (record.stack) {
                logData.stack = record.stack;
            }

            await this.writeLog(logData);
        } catch (error) {
            console.error('Error writing to blob storage:', error);
        }
    }

    private getLogType(level: LogLevel): string {
        if (level >= LogLevel.ERROR) {
            return "error";
        }
        if (level >= LogLevel.WARNING) {
            return "warning";
        }
        return "log";
    }

    private async writeLog(log: Record<string, any>): Promise<void> {
        // create a new file when block count hits close 25k
        if (this.numBlocks >= this.maxBlockCount) {
            // Reinitialize with new blob name
            const now = new Date();
            const newBlobName = `report/${now.toISOString().replace(/[:.]/g, '-')}.logs.json`;
            this.blobName = newBlobName;
            this.blobClient = this.blobServiceClient.getBlobClient(this.containerName, this.blobName);
            await this.initializeBlob();
            this.numBlocks = 0;
        }

        const logString = JSON.stringify(log, null, 4) + "\n";
        await this.blobClient.appendBlock(logString);

        // update the blob's block count
        this.numBlocks += 1;
    }
}