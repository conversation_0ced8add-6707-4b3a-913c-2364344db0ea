/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 *
 * A configuration reader utility class.
 * Converted from Python to TypeScript for GraphRAG integration.
 */



type KeyValue = string | { value: string }
type EnvKeySet = string | string[]

/**
 * Read a key value.
 */
function readKey(value: KeyValue): string {
    if (typeof value !== 'string') {
        return value.value.toLowerCase()
    }
    return value.toLowerCase()
}

/**
 * Get environment variable value safely.
 */
function getEnvVar(key: string): string | undefined {
    // Use globalThis to access process.env safely
    return (globalThis as any).process?.env?.[key]
}

/**
 * A configuration reader utility class.
 */
export class EnvironmentReader {
    private _configStack: Record<string, any>[] = []
    private _envPrefix: string = ""

    constructor() {
        // Initialize empty config stack
    }

    /**
     * Read environment variable with fallback.
     */
    private _readEnv<T>(
        envKey: string | string[], 
        defaultValue: T, 
        reader: (key: string, defaultVal: T) => T
    ): T {
        const keys = Array.isArray(envKey) ? envKey : [envKey]
        
        for (const k of keys) {
            const result = reader(k.toUpperCase(), defaultValue)
            if (result !== defaultValue) {
                return result
            }
        }
        
        return defaultValue
    }

    /**
     * Set the environment variable prefix.
     */
    envvarPrefix(prefix: KeyValue): EnvironmentReader {
        const prefixStr = readKey(prefix)
        this._envPrefix = `${prefixStr}_`.toUpperCase()
        return this
    }

    /**
     * Create a context to push the value into the config stack.
     */
    use(value: Record<string, any> | null): {
        enter: () => void
        exit: () => void
    } {
        return {
            enter: () => {
                this._configStack.push(value || {})
            },
            exit: () => {
                this._configStack.pop()
            }
        }
    }

    /**
     * Get the current section.
     */
    get section(): Record<string, any> {
        return this._configStack.length > 0 
            ? this._configStack[this._configStack.length - 1] 
            : {}
    }

    /**
     * Read a string configuration value.
     */
    str(
        key: KeyValue,
        envKey?: EnvKeySet,
        defaultValue?: string
    ): string | undefined {
        const keyStr = readKey(key)
        
        if (this.section && keyStr in this.section) {
            return this.section[keyStr]
        }

        return this._readEnv(
            envKey || keyStr,
            defaultValue,
            (k, dv) => getEnvVar(this._envPrefix + k) || getEnvVar(k) || dv
        )
    }

    /**
     * Read an integer configuration value.
     */
    int(
        key: KeyValue,
        envKey?: EnvKeySet,
        defaultValue?: number
    ): number | undefined {
        const keyStr = readKey(key)
        
        if (this.section && keyStr in this.section) {
            return parseInt(this.section[keyStr], 10)
        }

        return this._readEnv(
            envKey || keyStr,
            defaultValue,
            (k, dv) => {
                const val = getEnvVar(this._envPrefix + k) || getEnvVar(k)
                return val ? parseInt(val, 10) : dv
            }
        )
    }

    /**
     * Read a boolean configuration value.
     */
    bool(
        key: KeyValue,
        envKey?: EnvKeySet,
        defaultValue?: boolean
    ): boolean | undefined {
        const keyStr = readKey(key)
        
        if (this.section && keyStr in this.section) {
            return Boolean(this.section[keyStr])
        }

        return this._readEnv(
            envKey || keyStr,
            defaultValue,
            (k, dv) => {
                const val = getEnvVar(this._envPrefix + k) || getEnvVar(k)
                if (!val) return dv
                return val.toLowerCase() === 'true' || val === '1'
            }
        )
    }

    /**
     * Read a float configuration value.
     */
    float(
        key: KeyValue,
        envKey?: EnvKeySet,
        defaultValue?: number
    ): number | undefined {
        const keyStr = readKey(key)
        
        if (this.section && keyStr in this.section) {
            return parseFloat(this.section[keyStr])
        }

        return this._readEnv(
            envKey || keyStr,
            defaultValue,
            (k, dv) => {
                const val = getEnvVar(this._envPrefix + k) || getEnvVar(k)
                return val ? parseFloat(val) : dv
            }
        )
    }

    /**
     * Parse a list configuration value.
     */
    list(
        key: KeyValue,
        envKey?: EnvKeySet,
        defaultValue?: string[]
    ): string[] | undefined {
        const keyStr = readKey(key)
        let result: any = null
        
        if (this.section && keyStr in this.section) {
            result = this.section[keyStr]
            if (Array.isArray(result)) {
                return result
            }
        }

        if (result === null) {
            result = this.str(key, envKey)
        }
        
        if (result !== null && result !== undefined) {
            const items = result.split(',').map((s: string) => s.trim())
            return items.filter((s: string) => s.length > 0)
        }
        
        return defaultValue
    }
}