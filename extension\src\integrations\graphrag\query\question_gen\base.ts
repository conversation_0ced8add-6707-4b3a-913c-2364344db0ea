// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Base classes for generating questions based on previously asked questions and most recent context data.
 */

import { ChatModel } from '../../language_model/protocol/base';
import { GlobalContextBuilder, LocalContextBuilder } from '../context_builder/builders';

/**
 * A Structured Question Result.
 */
export interface QuestionResult {
  response: string[];
  contextData: string | Record<string, any>;
  completionTime: number;
  llmCalls: number;
  promptTokens: number;
}

/**
 * The Base Question Gen implementation.
 */
export abstract class BaseQuestionGen {
  protected model: ChatModel;
  protected contextBuilder: GlobalContextBuilder | LocalContextBuilder;
  protected tokenEncoder?: any;
  protected modelParams: Record<string, any>;
  protected contextBuilderParams: Record<string, any>;

  constructor(
    model: ChatModel,
    contextBuilder: GlobalContextBuilder | LocalContextBuilder,
    tokenEncoder?: any,
    modelParams?: Record<string, any>,
    contextBuilderParams?: Record<string, any>
  ) {
    this.model = model;
    this.contextBuilder = contextBuilder;
    this.tokenEncoder = tokenEncoder;
    this.modelParams = modelParams || {};
    this.contextBuilderParams = contextBuilderParams || {};
  }

  /**
   * Generate questions.
   */
  abstract generate(
    questionHistory: string[],
    contextData: string | undefined,
    questionCount: number,
    ...kwargs: any[]
  ): Promise<QuestionResult>;

  /**
   * Generate questions asynchronously.
   */
  abstract agenerate(
    questionHistory: string[],
    contextData: string | undefined,
    questionCount: number,
    ...kwargs: any[]
  ): Promise<QuestionResult>;
}