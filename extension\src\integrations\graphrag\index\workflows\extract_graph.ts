/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing run_workflow method definition.
 */

import { DataFrame } from '../../data-model/types';
import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';
import { extractGraph as extractor } from '../operations/extract-graph/extract-graph';
import { summarizeDescriptions } from '../operations/summarize-descriptions/summarize-descriptions';

const logger = console;

/**
 * All the steps to create the base entity graph.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: extract_graph");
    
    const textUnits = await loadTableFromStorage("text_units", context.outputStorage);

    const extractGraphLlmSettings = config.getLanguageModelConfig(
        config.extractGraph.modelId
    );
    const extractionStrategy = config.extractGraph.resolvedStrategy(
        config.rootDir, 
        extractGraphLlmSettings
    );

    const summarizationLlmSettings = config.getLanguageModelConfig(
        config.summarizeDescriptions.modelId
    );
    const summarizationStrategy = config.summarizeDescriptions.resolvedStrategy(
        config.rootDir, 
        summarizationLlmSettings
    );

    const [entities, relationships, rawEntities, rawRelationships] = await extractGraphWorkflow(
        textUnits,
        context.callbacks,
        context.cache,
        extractionStrategy,
        extractGraphLlmSettings.concurrentRequests,
        extractGraphLlmSettings.asyncMode,
        config.extractGraph.entityTypes,
        summarizationStrategy,
        summarizationLlmSettings.concurrentRequests
    );

    await writeTableToStorage(entities, "entities", context.outputStorage);
    await writeTableToStorage(relationships, "relationships", context.outputStorage);

    if (config.snapshots.rawGraph) {
        await writeTableToStorage(rawEntities, "raw_entities", context.outputStorage);
        await writeTableToStorage(rawRelationships, "raw_relationships", context.outputStorage);
    }

    logger.info("Workflow completed: extract_graph");
    return {
        result: {
            entities,
            relationships
        },
        stop: false
    };
}

/**
 * All the steps to create the base entity graph.
 */
async function extractGraphWorkflow(
    textUnits: DataFrame,
    callbacks: any,
    cache: any,
    extractionStrategy?: Record<string, any>,
    extractionNumThreads: number = 4,
    extractionAsyncMode: any = 'AsyncIO',
    entityTypes?: string[],
    summarizationStrategy?: Record<string, any>,
    summarizationNumThreads: number = 4
): Promise<[DataFrame, DataFrame, DataFrame, DataFrame]> {
    // Extract entities and relationships
    const [extractedEntities, extractedRelationships] = await extractor(
        textUnits,
        callbacks,
        cache,
        "text",
        "id",
        extractionStrategy,
        extractionAsyncMode,
        entityTypes,
        extractionNumThreads
    );

    if (!validateData(extractedEntities)) {
        const errorMsg = "Entity Extraction failed. No entities detected during extraction.";
        logger.error(errorMsg);
        throw new Error(errorMsg);
    }

    if (!validateData(extractedRelationships)) {
        const errorMsg = "Entity Extraction failed. No relationships detected during extraction.";
        logger.error(errorMsg);
        throw new Error(errorMsg);
    }

    // Copy raw data before summarization
    const rawEntities = { ...extractedEntities, data: [...extractedEntities.data] };
    const rawRelationships = { ...extractedRelationships, data: [...extractedRelationships.data] };

    // Get summarized entities and relationships
    const [entities, relationships] = await getSummarizedEntitiesRelationships(
        extractedEntities,
        extractedRelationships,
        callbacks,
        cache,
        summarizationStrategy,
        summarizationNumThreads
    );

    return [entities, relationships, rawEntities, rawRelationships];
}

/**
 * Summarize the entities and relationships.
 */
async function getSummarizedEntitiesRelationships(
    extractedEntities: DataFrame,
    extractedRelationships: DataFrame,
    callbacks: any,
    cache: any,
    summarizationStrategy?: Record<string, any>,
    summarizationNumThreads: number = 4
): Promise<[DataFrame, DataFrame]> {
    const [entitySummaries, relationshipSummaries] = await summarizeDescriptions(
        extractedEntities,
        extractedRelationships,
        callbacks,
        cache,
        summarizationStrategy,
        summarizationNumThreads
    );

    // Merge summaries back with original data
    // This is a simplified merge - in reality you'd do proper DataFrame joins
    const relationships = {
        ...extractedRelationships,
        data: extractedRelationships.data.map(row => {
            const summary = relationshipSummaries.data.find(s => 
                s.source === row.source && s.target === row.target
            );
            return {
                ...row,
                description: summary?.description || row.description
            };
        })
    };

    const entities = {
        ...extractedEntities,
        data: extractedEntities.data.map(row => {
            const summary = entitySummaries.data.find(s => s.title === row.title);
            return {
                ...row,
                description: summary?.description || row.description
            };
        })
    };

    return [entities, relationships];
}

/**
 * Validate that the dataframe has data.
 */
function validateData(df: DataFrame): boolean {
    return df.data.length > 0;
}