/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { EmbedGraphConfig } from '../../config/models/embed-graph-config';
import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { createGraph } from '../operations/create-graph';
import { finalizeEntities } from '../operations/finalize-entities';
import { finalizeRelationships } from '../operations/finalize-relationships';
import { snapshotGraphml } from '../operations/snapshot-graphml';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * All the steps to create the base entity graph.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: finalize_graph");
    
    const entities = await loadTableFromStorage("entities", context.outputStorage);
    const relationships = await loadTableFromStorage("relationships", context.outputStorage);

    const { finalEntities, finalRelationships } = finalizeGraph({
        entities,
        relationships,
        embedConfig: config.embedGraph,
        layoutEnabled: config.umap.enabled,
    });

    await writeTableToStorage(finalEntities, "entities", context.outputStorage);
    await writeTableToStorage(finalRelationships, "relationships", context.outputStorage);

    if (config.snapshots.graphml) {
        // TODO: extract graphs at each level, and add in meta like descriptions
        const graph = createGraph(finalRelationships, ["weight"]);

        await snapshotGraphml({
            graph,
            name: "graph",
            storage: context.outputStorage,
        });
    }

    logger.info("Workflow completed: finalize_graph");
    return {
        result: {
            entities,
            relationships,
        }
    };
}

export interface FinalizeGraphParams {
    entities: DataFrame;
    relationships: DataFrame;
    embedConfig?: EmbedGraphConfig | null;
    layoutEnabled?: boolean;
}

export interface FinalizeGraphResult {
    finalEntities: DataFrame;
    finalRelationships: DataFrame;
}

/**
 * All the steps to finalize the entity and relationship formats.
 */
export function finalizeGraph({
    entities,
    relationships,
    embedConfig = null,
    layoutEnabled = false,
}: FinalizeGraphParams): FinalizeGraphResult {
    const finalEntities = finalizeEntities({
        entities,
        relationships,
        embedConfig,
        layoutEnabled,
    });
    const finalRelationships = finalizeRelationships(relationships);
    
    return {
        finalEntities,
        finalRelationships,
    };
}