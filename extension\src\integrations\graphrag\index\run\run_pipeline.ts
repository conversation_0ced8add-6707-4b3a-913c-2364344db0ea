/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Different methods to run the pipeline.
 */

import { WorkflowCallbacks } from '../../callbacks/workflow-callbacks';
import { GraphRagConfig } from '../../config/models/graph-rag-config';
import { PipelineStorage } from '../../storage/pipeline-storage';
import { createCacheFromConfig, createStorageFromConfig } from '../../utils/api';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';
import { PipelineRunContext } from '../typing/context';
import { Pipeline } from '../typing/pipeline';
import { PipelineRunResult } from '../typing/pipeline-run-result';
import { createRunContext } from './utils';

const logger = console;

/**
 * Run all workflows using a simplified pipeline.
 */
export async function* runPipeline(
    pipeline: Pipeline,
    config: GraphRagConfig,
    callbacks: WorkflowCallbacks,
    isUpdateRun: boolean = false
): AsyncGenerator<PipelineRunResult> {
    const rootDir = config.rootDir;

    const inputStorage = createStorageFromConfig(config.input.storage);
    const outputStorage = createStorageFromConfig(config.output);
    const cache = createCacheFromConfig(config.cache, rootDir);

    // Load existing state in case any workflows are stateful
    const stateJson = await outputStorage.get("context.json");
    let state = stateJson ? JSON.parse(stateJson) : {};

    let context: PipelineRunContext;

    if (isUpdateRun) {
        logger.info("Running incremental indexing.");

        const updateStorage = createStorageFromConfig(config.updateIndexOutput);
        // We use this to store the new subset index, and will merge its content with the previous index
        const updateTimestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const timestampedStorage = updateStorage.child(updateTimestamp);
        const deltaStorage = timestampedStorage.child("delta");
        // Copy the previous output to a backup folder, so we can replace it with the update
        // We'll read from this later when we merge the old and new indexes
        const previousStorage = timestampedStorage.child("previous");
        await copyPreviousOutput(outputStorage, previousStorage);

        state.updateTimestamp = updateTimestamp;

        context = createRunContext({
            inputStorage: inputStorage,
            outputStorage: deltaStorage,
            previousStorage: previousStorage,
            cache: cache,
            callbacks: callbacks,
            state: state,
        });
    } else {
        logger.info("Running standard indexing.");

        context = createRunContext({
            inputStorage: inputStorage,
            outputStorage: outputStorage,
            cache: cache,
            callbacks: callbacks,
            state: state,
        });
    }

    yield* runPipelineInternal(pipeline, config, context);
}

/**
 * Internal pipeline runner.
 */
async function* runPipelineInternal(
    pipeline: Pipeline,
    config: GraphRagConfig,
    context: PipelineRunContext
): AsyncGenerator<PipelineRunResult> {
    const startTime = Date.now();
    let lastWorkflow = "<startup>";

    try {
        await dumpJson(context);

        logger.info("Executing pipeline...");
        
        for (const [name, workflowFunction] of pipeline.run()) {
            lastWorkflow = name;
            context.callbacks.workflowStart?.(name, null);
            const workTime = Date.now();
            
            const result = await workflowFunction(config, context);
            
            context.callbacks.workflowEnd?.(name, result);
            
            yield {
                workflow: name,
                result: result.result,
                state: context.state,
                errors: null
            };
            
            context.stats.workflows[name] = { overall: (Date.now() - workTime) / 1000 };
            
            if (result.stop) {
                logger.info("Halting pipeline at workflow request");
                break;
            }
        }

        context.stats.totalRuntime = (Date.now() - startTime) / 1000;
        logger.info("Indexing pipeline complete.");
        await dumpJson(context);

    } catch (e) {
        const error = e instanceof Error ? e : new Error(String(e));
        logger.error(`error running workflow ${lastWorkflow}`, error);
        
        yield {
            workflow: lastWorkflow,
            result: null,
            state: context.state,
            errors: [error]
        };
    }
}

/**
 * Dump the stats and context state to the storage.
 */
async function dumpJson(context: PipelineRunContext): Promise<void> {
    await context.outputStorage.set(
        "stats.json", 
        JSON.stringify(context.stats, null, 4)
    );
    await context.outputStorage.set(
        "context.json", 
        JSON.stringify(context.state, null, 4)
    );
}

/**
 * Copy previous output files to backup storage.
 */
async function copyPreviousOutput(
    storage: PipelineStorage,
    copyStorage: PipelineStorage
): Promise<void> {
    const parquetPattern = /\.parquet$/;
    
    for (const [file] of storage.find(parquetPattern)) {
        const baseName = file.replace(".parquet", "");
        const table = await loadTableFromStorage(baseName, storage);
        await writeTableToStorage(table, baseName, copyStorage);
    }
}