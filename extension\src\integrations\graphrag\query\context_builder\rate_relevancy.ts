/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Algorithm to rate the relevancy between a query and description text.
 */

import { ChatModel } from '../../language_model/protocol/base';
import { RATE_QUERY } from './rate-prompt';
import { numTokens, tryParseJsonObject } from '../llm/text-utils';

const logger = console;

export interface RateRelevancyOptions {
    query: string;
    description: string;
    model: ChatModel;
    tokenEncoder: any;
    rateQuery?: string;
    numRepeats?: number;
    [key: string]: any; // For additional model parameters
}

export interface RateRelevancyResult {
    rating: number;
    ratings: number[];
    llmCalls: number;
    promptTokens: number;
    outputTokens: number;
}

export async function rateRelevancy(options: RateRelevancyOptions): Promise<RateRelevancyResult> {
    const {
        query,
        description,
        model,
        tokenEncoder,
        rateQuery = RATE_QUERY,
        numRepeats = 1,
        ...modelParams
    } = options;

    let llmCalls = 0;
    let promptTokens = 0;
    let outputTokens = 0;
    const ratings: number[] = [];

    const messages = [
        {
            role: "system",
            content: rateQuery.replace("{description}", description).replace("{question}", query),
        },
    ];

    for (let i = 0; i < numRepeats; i++) {
        try {
            const modelResponse = await model.achat(query, messages, {
                ...modelParams,
                json: true,
            });
            const response = modelResponse.output.content;

            try {
                const [, parsedResponse] = tryParseJsonObject(response);
                ratings.push(parsedResponse.rating);
            } catch (error) {
                // In case of json parsing error, default to rating 1 so the report is kept.
                // JSON parsing error should rarely happen.
                logger.warn("Error parsing json response, defaulting to rating 1");
                ratings.push(1);
            }

            llmCalls += 1;
            promptTokens += numTokens(messages[0].content, tokenEncoder);
            outputTokens += numTokens(response, tokenEncoder);
        } catch (error) {
            logger.error("Error in rate relevancy:", error);
            ratings.push(1); // Default rating on error
            llmCalls += 1;
        }
    }

    // Select the decision with the most votes
    const ratingCounts: Record<number, number> = {};
    for (const rating of ratings) {
        ratingCounts[rating] = (ratingCounts[rating] || 0) + 1;
    }

    let maxCount = 0;
    let finalRating = 1;
    for (const [rating, count] of Object.entries(ratingCounts)) {
        if (count > maxCount) {
            maxCount = count;
            finalRating = parseInt(rating);
        }
    }

    return {
        rating: finalRating,
        ratings,
        llmCalls,
        promptTokens,
        outputTokens,
    };
}